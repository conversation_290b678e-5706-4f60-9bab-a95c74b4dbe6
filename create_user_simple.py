#!/usr/bin/env python3
"""
简单的用户创建脚本
"""
import sqlite3
import os
import sys

# 添加backend路径以导入模块
sys.path.append('backend')

try:
    from passlib.context import CryptContext
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    def hash_password(password: str) -> str:
        """使用bcrypt哈希密码"""
        return pwd_context.hash(password)
except ImportError:
    import hashlib

    def hash_password(password: str) -> str:
        """简单的密码哈希（备用方案）"""
        return hashlib.sha256(password.encode()).hexdigest()

def create_user():
    """创建测试用户"""
    db_path = "backend/trading.db"

    # 确保数据库文件存在
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False

    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 删除现有用户（如果存在）
        cursor.execute("DELETE FROM users WHERE username = ?", ("admin",))
        print("🔧 删除现有用户（如果存在）")

        # 创建用户
        hashed_password = hash_password("123456")
        cursor.execute("""
            INSERT INTO users (username, email, hashed_password, is_active, is_superuser)
            VALUES (?, ?, ?, ?, ?)
        """, ("admin", "<EMAIL>", hashed_password, True, True))

        conn.commit()
        user_id = cursor.lastrowid

        print("✅ 测试用户创建成功:")
        print(f"   用户ID: {user_id}")
        print(f"   用户名: admin")
        print(f"   密码: 123456")
        print(f"   邮箱: <EMAIL>")

        return True

    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("🧠 个人量化交易平台 - 创建测试用户")
    print("=" * 50)

    success = create_user()

    if success:
        print("\n🎉 测试用户创建完成！")
        print("现在可以使用以下凭据登录:")
        print("用户名: admin")
        print("密码: 123456")
    else:
        print("\n❌ 测试用户创建失败")
