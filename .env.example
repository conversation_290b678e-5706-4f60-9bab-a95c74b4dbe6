# 数据库配置
DATABASE_URL=sqlite:///./trading.db

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 主密码 (用于加密API密钥)
MASTER_PASSWORD=your-master-password-for-api-key-encryption

# Telegram Bot配置
TELEGRAM_BOT_TOKEN=your-telegram-bot-token

# 应用配置
APP_NAME=Personal Trading Platform
APP_VERSION=1.0.0
DEBUG=false

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 服务端口
BACKEND_PORT=8000
FRONTEND_PORT=3000

# 风控默认配置
DEFAULT_MAX_ORDER_AMOUNT=1000
DEFAULT_MAX_DAILY_LOSS=500
DEFAULT_MAX_ORDERS_PER_STRATEGY=10
DEFAULT_ORDER_RATE_LIMIT=60

# 交易所配置
BINANCE_BASE_URL=https://api.binance.com
BINANCE_WS_URL=wss://stream.binance.com:9443
OKX_BASE_URL=https://www.okx.com
OKX_WS_URL=wss://ws.okx.com:8443

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/trading.log

# Redis配置 (可选，用于缓存)
REDIS_URL=redis://localhost:6379/0

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
