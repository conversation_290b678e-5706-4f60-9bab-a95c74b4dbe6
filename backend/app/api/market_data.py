"""
行情数据API
"""
from fastapi import APIRouter, Depends, HTTPException, status, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json
import asyncio
import logging
from datetime import datetime

from ..core.database import get_db
from ..models.user import User
from ..services.exchange_service import exchange_service
from ..services.websocket_manager import ws_manager
from .auth import get_current_active_user

router = APIRouter()
logger = logging.getLogger(__name__)


class TickerData(BaseModel):
    """行情数据模型"""
    symbol: str
    price: float
    change_24h: float
    change_24h_percent: float
    volume_24h: float
    high_24h: float
    low_24h: float
    timestamp: str


class KlineData(BaseModel):
    """K线数据模型"""
    symbol: str
    interval: str
    open_time: int
    close_time: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float


class OrderBookData(BaseModel):
    """订单簿数据模型"""
    symbol: str
    bids: List[List[float]]  # [[price, quantity], ...]
    asks: List[List[float]]  # [[price, quantity], ...]
    timestamp: str


# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_subscriptions: Dict[int, List[str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: int):
        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id not in self.user_subscriptions:
            self.user_subscriptions[user_id] = []
    
    def disconnect(self, websocket: WebSocket, user_id: int):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id in self.user_subscriptions:
            del self.user_subscriptions[user_id]
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # 连接已断开，移除
                if connection in self.active_connections:
                    self.active_connections.remove(connection)


manager = ConnectionManager()


@router.get("/ticker/{symbol}")
async def get_ticker(
    symbol: str,
    exchange: str = "binance"
):
    """获取单个交易对行情"""
    try:
        ticker_data = await exchange_service.get_market_data(exchange, symbol)
        return TickerData(**ticker_data)
    except Exception as e:
        # 如果获取失败，返回模拟数据
        mock_data = {
            "symbol": symbol.upper(),
            "price": 50000.0,
            "change_24h": 1250.5,
            "change_24h_percent": 2.56,
            "volume_24h": 1234567.89,
            "high_24h": 51000.0,
            "low_24h": 48500.0,
            "timestamp": datetime.utcnow().isoformat()
        }
        return TickerData(**mock_data)


@router.get("/ticker")
async def get_all_tickers(
    current_user: User = Depends(get_current_active_user),
    exchange: Optional[str] = None
):
    """获取所有交易对行情"""
    # 这里应该调用交易所API获取所有行情
    # 暂时返回模拟数据
    
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "DOTUSDT"]
    
    tickers = []
    for symbol in symbols:
        mock_data = {
            "symbol": symbol,
            "price": 50000.0 if "BTC" in symbol else 3000.0,
            "change_24h": 1250.5,
            "change_24h_percent": 2.56,
            "volume_24h": 1234567.89,
            "high_24h": 51000.0 if "BTC" in symbol else 3100.0,
            "low_24h": 48500.0 if "BTC" in symbol else 2900.0,
            "timestamp": datetime.utcnow().isoformat()
        }
        tickers.append(TickerData(**mock_data))
    
    return tickers


@router.get("/klines/{symbol}")
async def get_klines(
    symbol: str,
    interval: str = "1h",
    limit: int = 100,
    current_user: User = Depends(get_current_active_user)
):
    """获取K线数据"""
    # 验证时间间隔
    valid_intervals = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"]
    if interval not in valid_intervals:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的时间间隔，支持: {', '.join(valid_intervals)}"
        )
    
    # 这里应该调用交易所API获取K线数据
    # 暂时返回模拟数据
    
    klines = []
    base_time = int(datetime.utcnow().timestamp() * 1000)
    
    for i in range(limit):
        open_time = base_time - (i * 3600000)  # 1小时间隔
        close_time = open_time + 3600000 - 1
        
        mock_kline = {
            "symbol": symbol.upper(),
            "interval": interval,
            "open_time": open_time,
            "close_time": close_time,
            "open_price": 50000.0 + (i * 10),
            "high_price": 50100.0 + (i * 10),
            "low_price": 49900.0 + (i * 10),
            "close_price": 50050.0 + (i * 10),
            "volume": 123.45
        }
        klines.append(KlineData(**mock_kline))
    
    return list(reversed(klines))  # 按时间正序返回


@router.get("/orderbook/{symbol}")
async def get_orderbook(
    symbol: str,
    limit: int = 20,
    current_user: User = Depends(get_current_active_user)
):
    """获取订单簿数据"""
    if limit > 100:
        limit = 100
    
    # 这里应该调用交易所API获取订单簿数据
    # 暂时返回模拟数据
    
    base_price = 50000.0
    
    # 生成买单 (bids)
    bids = []
    for i in range(limit):
        price = base_price - (i * 10)
        quantity = 0.1 + (i * 0.01)
        bids.append([price, quantity])
    
    # 生成卖单 (asks)
    asks = []
    for i in range(limit):
        price = base_price + (i * 10)
        quantity = 0.1 + (i * 0.01)
        asks.append([price, quantity])
    
    return OrderBookData(
        symbol=symbol.upper(),
        bids=bids,
        asks=asks,
        timestamp=datetime.utcnow().isoformat()
    )


@router.get("/symbols")
async def get_symbols(
    exchange: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """获取支持的交易对列表"""
    # 这里应该调用交易所API获取交易对列表
    # 暂时返回模拟数据
    
    symbols = [
        {
            "symbol": "BTCUSDT",
            "base_asset": "BTC",
            "quote_asset": "USDT",
            "status": "TRADING",
            "min_qty": 0.00001,
            "max_qty": 9000.0,
            "step_size": 0.00001,
            "tick_size": 0.01
        },
        {
            "symbol": "ETHUSDT",
            "base_asset": "ETH",
            "quote_asset": "USDT",
            "status": "TRADING",
            "min_qty": 0.0001,
            "max_qty": 90000.0,
            "step_size": 0.0001,
            "tick_size": 0.01
        }
    ]
    
    return symbols


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str,
    db: Session = Depends(get_db)
):
    """WebSocket行情推送"""
    try:
        # 验证用户身份
        from ..core.security import verify_token
        payload = verify_token(token)
        if not payload:
            await websocket.close(code=4001, reason="Invalid token")
            return

        username = payload.get("sub")
        if not username:
            await websocket.close(code=4001, reason="Invalid token")
            return

        from .auth import get_user_by_username
        user = get_user_by_username(db, username)
        if not user:
            await websocket.close(code=4001, reason="User not found")
            return

        # 建立连接
        await manager.connect(websocket, user.id)

        # 启动WebSocket管理器（如果还未启动）
        if not ws_manager.is_running:
            await ws_manager.start()

        # 用户订阅的交易所和符号
        user_subscriptions = {}

        try:
            while True:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)

                if message.get("type") == "subscribe":
                    # 订阅行情
                    exchange = message.get("exchange", "binance")
                    symbols = message.get("symbols", [])
                    data_type = message.get("data_type", "ticker")  # ticker, kline, depth
                    interval = message.get("interval", "1m")  # 仅K线需要

                    # 创建回调函数
                    async def market_data_callback(market_data):
                        await manager.send_personal_message(
                            json.dumps(market_data),
                            websocket
                        )

                    # 根据数据类型订阅
                    if data_type == "ticker":
                        await ws_manager.subscribe_ticker(exchange, symbols, market_data_callback)
                    elif data_type == "kline":
                        await ws_manager.subscribe_kline(exchange, symbols, interval, market_data_callback)
                    elif data_type == "depth":
                        await ws_manager.subscribe_depth(exchange, symbols, market_data_callback)

                    # 记录用户订阅
                    key = f"{exchange}:{data_type}"
                    if data_type == "kline":
                        key += f":{interval}"

                    user_subscriptions[key] = symbols
                    manager.user_subscriptions[user.id] = user_subscriptions

                    await manager.send_personal_message(
                        json.dumps({
                            "type": "subscribed",
                            "exchange": exchange,
                            "data_type": data_type,
                            "symbols": symbols,
                            "interval": interval if data_type == "kline" else None
                        }),
                        websocket
                    )

                elif message.get("type") == "unsubscribe":
                    # 取消订阅
                    exchange = message.get("exchange", "binance")
                    symbols = message.get("symbols", [])
                    data_type = message.get("data_type", "ticker")
                    interval = message.get("interval", "1m")

                    # 取消WebSocket管理器订阅
                    if data_type == "kline":
                        await ws_manager.unsubscribe(exchange, data_type, symbols, interval)
                    else:
                        await ws_manager.unsubscribe(exchange, data_type, symbols)

                    # 从用户订阅中移除
                    key = f"{exchange}:{data_type}"
                    if data_type == "kline":
                        key += f":{interval}"

                    if key in user_subscriptions:
                        del user_subscriptions[key]

                    await manager.send_personal_message(
                        json.dumps({
                            "type": "unsubscribed",
                            "exchange": exchange,
                            "data_type": data_type,
                            "symbols": symbols
                        }),
                        websocket
                    )

                elif message.get("type") == "ping":
                    # 心跳检测
                    await manager.send_personal_message(
                        json.dumps({"type": "pong"}),
                        websocket
                    )

                # 短暂等待，避免CPU占用过高
                await asyncio.sleep(0.1)

        except WebSocketDisconnect:
            # 清理用户订阅
            if user.id in manager.user_subscriptions:
                del manager.user_subscriptions[user.id]
            manager.disconnect(websocket, user.id)

    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        await websocket.close(code=4000, reason=str(e))
