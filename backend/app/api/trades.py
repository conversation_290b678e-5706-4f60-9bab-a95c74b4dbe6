"""
交易记录API
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta

from ..core.database import get_db
from ..models.user import User
from ..models.trade import Trade
from .auth import get_current_active_user

router = APIRouter()


class TradeResponse(BaseModel):
    """交易记录响应模型"""
    id: int
    exchange: str
    exchange_trade_id: Optional[str]
    symbol: str
    side: str
    amount: float
    price: float
    fee: float
    fee_currency: Optional[str]
    pnl: Optional[float]
    pnl_percentage: Optional[float]
    executed_at: Optional[str]
    created_at: Optional[str]
    notes: Optional[str]


class TradeStats(BaseModel):
    """交易统计模型"""
    total_trades: int
    total_volume: float
    total_profit: float
    total_loss: float
    net_profit: float
    win_rate: float
    avg_profit: float
    avg_loss: float
    profit_factor: float
    total_fees: float


@router.get("/", response_model=List[TradeResponse])
async def get_trades(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    symbol: Optional[str] = Query(None, description="交易对筛选"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取用户交易记录"""
    query = db.query(Trade).filter(Trade.user_id == current_user.id)
    
    if symbol:
        query = query.filter(Trade.symbol == symbol)
    
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(Trade.executed_at >= start_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的开始日期格式，请使用 YYYY-MM-DD"
            )
    
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            query = query.filter(Trade.executed_at < end_dt)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的结束日期格式，请使用 YYYY-MM-DD"
            )
    
    trades = query.order_by(Trade.executed_at.desc()).limit(limit).all()
    
    return [
        TradeResponse(
            id=trade.id,
            exchange=trade.exchange,
            exchange_trade_id=trade.exchange_trade_id,
            symbol=trade.symbol,
            side=trade.side,
            amount=trade.amount,
            price=trade.price,
            fee=trade.fee,
            fee_currency=trade.fee_currency,
            pnl=trade.pnl,
            pnl_percentage=trade.pnl_percentage,
            executed_at=trade.executed_at.isoformat() if trade.executed_at else None,
            created_at=trade.created_at.isoformat() if trade.created_at else None,
            notes=trade.notes
        )
        for trade in trades
    ]


@router.get("/{trade_id}", response_model=TradeResponse)
async def get_trade(
    trade_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个交易记录"""
    trade = db.query(Trade).filter(
        Trade.id == trade_id,
        Trade.user_id == current_user.id
    ).first()
    
    if not trade:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="交易记录不存在"
        )
    
    return TradeResponse(
        id=trade.id,
        exchange=trade.exchange,
        exchange_trade_id=trade.exchange_trade_id,
        symbol=trade.symbol,
        side=trade.side,
        amount=trade.amount,
        price=trade.price,
        fee=trade.fee,
        fee_currency=trade.fee_currency,
        pnl=trade.pnl,
        pnl_percentage=trade.pnl_percentage,
        executed_at=trade.executed_at.isoformat() if trade.executed_at else None,
        created_at=trade.created_at.isoformat() if trade.created_at else None,
        notes=trade.notes
    )


@router.get("/stats/summary", response_model=TradeStats)
async def get_trade_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    days: int = Query(30, description="统计天数")
):
    """获取交易统计"""
    # 计算开始日期
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # 查询指定时间范围内的交易
    trades = db.query(Trade).filter(
        and_(
            Trade.user_id == current_user.id,
            Trade.executed_at >= start_date
        )
    ).all()
    
    if not trades:
        return TradeStats(
            total_trades=0,
            total_volume=0.0,
            total_profit=0.0,
            total_loss=0.0,
            net_profit=0.0,
            win_rate=0.0,
            avg_profit=0.0,
            avg_loss=0.0,
            profit_factor=0.0,
            total_fees=0.0
        )
    
    # 计算统计数据
    total_trades = len(trades)
    total_volume = sum(trade.total_value for trade in trades)
    total_fees = sum(trade.fee for trade in trades if trade.fee)
    
    # 盈亏统计
    profitable_trades = [t for t in trades if t.pnl and t.pnl > 0]
    losing_trades = [t for t in trades if t.pnl and t.pnl < 0]
    
    total_profit = sum(t.pnl for t in profitable_trades)
    total_loss = sum(abs(t.pnl) for t in losing_trades)
    net_profit = total_profit - total_loss
    
    win_rate = len(profitable_trades) / total_trades * 100 if total_trades > 0 else 0
    avg_profit = total_profit / len(profitable_trades) if profitable_trades else 0
    avg_loss = total_loss / len(losing_trades) if losing_trades else 0
    profit_factor = total_profit / total_loss if total_loss > 0 else 0.0
    
    return TradeStats(
        total_trades=total_trades,
        total_volume=total_volume,
        total_profit=total_profit,
        total_loss=total_loss,
        net_profit=net_profit,
        win_rate=win_rate,
        avg_profit=avg_profit,
        avg_loss=avg_loss,
        profit_factor=profit_factor,
        total_fees=total_fees
    )


@router.get("/stats/daily")
async def get_daily_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    days: int = Query(30, description="统计天数")
):
    """获取每日交易统计"""
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # 按日期分组统计
    daily_stats = db.query(
        func.date(Trade.executed_at).label('date'),
        func.count(Trade.id).label('trade_count'),
        func.sum(Trade.amount * Trade.price).label('volume'),
        func.sum(Trade.pnl).label('pnl'),
        func.sum(Trade.fee).label('fees')
    ).filter(
        and_(
            Trade.user_id == current_user.id,
            Trade.executed_at >= start_date
        )
    ).group_by(
        func.date(Trade.executed_at)
    ).order_by(
        func.date(Trade.executed_at)
    ).all()
    
    return [
        {
            "date": stat.date.isoformat() if stat.date else None,
            "trade_count": stat.trade_count or 0,
            "volume": float(stat.volume or 0),
            "pnl": float(stat.pnl or 0),
            "fees": float(stat.fees or 0)
        }
        for stat in daily_stats
    ]


@router.get("/stats/symbols")
async def get_symbol_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    days: int = Query(30, description="统计天数")
):
    """获取交易对统计"""
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # 按交易对分组统计
    symbol_stats = db.query(
        Trade.symbol,
        func.count(Trade.id).label('trade_count'),
        func.sum(Trade.amount * Trade.price).label('volume'),
        func.sum(Trade.pnl).label('pnl'),
        func.sum(Trade.fee).label('fees')
    ).filter(
        and_(
            Trade.user_id == current_user.id,
            Trade.executed_at >= start_date
        )
    ).group_by(
        Trade.symbol
    ).order_by(
        func.sum(Trade.amount * Trade.price).desc()
    ).all()
    
    return [
        {
            "symbol": stat.symbol,
            "trade_count": stat.trade_count or 0,
            "volume": float(stat.volume or 0),
            "pnl": float(stat.pnl or 0),
            "fees": float(stat.fees or 0)
        }
        for stat in symbol_stats
    ]
