"""
用户管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List

from ..core.database import get_db
from ..models.user import User
from .auth import get_current_active_user

router = APIRouter()


class UserUpdate(BaseModel):
    """用户更新模型"""
    email: Optional[str] = None
    is_active: Optional[bool] = None


class UserStats(BaseModel):
    """用户统计模型"""
    total_orders: int
    total_trades: int
    total_strategies: int
    total_api_keys: int
    total_profit: float
    total_loss: float


@router.get("/profile")
async def get_profile(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户资料"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "is_active": current_user.is_active,
        "is_superuser": current_user.is_superuser,
        "created_at": current_user.created_at.isoformat() if current_user.created_at else None,
        "last_login": current_user.last_login.isoformat() if current_user.last_login else None
    }


@router.put("/profile")
async def update_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新用户资料"""
    if user_update.email is not None:
        # 检查邮箱是否已被其他用户使用
        existing_user = db.query(User).filter(
            User.email == user_update.email,
            User.id != current_user.id
        ).first()
        
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被其他用户使用"
            )
        
        current_user.email = user_update.email
    
    if user_update.is_active is not None and current_user.is_superuser:
        current_user.is_active = user_update.is_active
    
    db.commit()
    db.refresh(current_user)
    
    return {"message": "用户资料更新成功"}


@router.get("/stats", response_model=UserStats)
async def get_user_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户统计信息"""
    # 统计订单数量
    total_orders = len(current_user.orders)
    
    # 统计交易数量
    total_trades = len(current_user.trades)
    
    # 统计策略数量
    total_strategies = len(current_user.strategies)
    
    # 统计API密钥数量
    total_api_keys = len(current_user.api_keys)
    
    # 计算总盈利和亏损
    total_profit = sum(trade.pnl for trade in current_user.trades if trade.pnl and trade.pnl > 0)
    total_loss = sum(abs(trade.pnl) for trade in current_user.trades if trade.pnl and trade.pnl < 0)
    
    return UserStats(
        total_orders=total_orders,
        total_trades=total_trades,
        total_strategies=total_strategies,
        total_api_keys=total_api_keys,
        total_profit=total_profit,
        total_loss=total_loss
    )


@router.delete("/account")
async def delete_account(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除用户账户"""
    # 检查是否有活跃的策略
    active_strategies = [s for s in current_user.strategies if s.is_running]
    if active_strategies:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请先停止所有运行中的策略"
        )
    
    # 检查是否有未完成的订单
    active_orders = [o for o in current_user.orders if o.is_active]
    if active_orders:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请先取消所有活跃订单"
        )
    
    # 删除用户 (级联删除相关数据)
    db.delete(current_user)
    db.commit()
    
    return {"message": "账户删除成功"}
