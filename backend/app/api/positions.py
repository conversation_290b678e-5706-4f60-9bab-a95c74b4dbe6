"""
持仓管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..core.database import get_db
from ..models.user import User
from ..models.position import Position
from ..services.exchange_service import exchange_service
from .auth import get_current_active_user

router = APIRouter()


class PositionResponse(BaseModel):
    """持仓响应模型"""
    id: int
    exchange: str
    symbol: str
    side: str
    size: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    margin: float
    leverage: float
    liquidation_price: Optional[float]
    created_at: Optional[str]
    updated_at: Optional[str]


@router.get("/", response_model=List[PositionResponse])
async def get_positions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    api_key_id: Optional[int] = Query(None, description="API密钥ID"),
    symbol: Optional[str] = Query(None, description="交易对筛选")
):
    """获取用户持仓列表"""
    if api_key_id:
        # 从交易所获取实时持仓
        try:
            positions = await exchange_service.get_positions(db, api_key_id, current_user.id)
            
            return [
                PositionResponse(
                    id=0,  # 实时数据没有数据库ID
                    exchange="",  # 从API密钥获取
                    symbol=pos['symbol'],
                    side=pos['side'],
                    size=pos['size'],
                    entry_price=pos['entry_price'],
                    mark_price=pos['mark_price'],
                    unrealized_pnl=pos['unrealized_pnl'],
                    margin=pos['margin'],
                    leverage=pos['leverage'],
                    liquidation_price=pos['liquidation_price'],
                    created_at=datetime.utcnow().isoformat(),
                    updated_at=datetime.utcnow().isoformat()
                )
                for pos in positions
            ]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取持仓失败: {str(e)}"
            )
    else:
        # 从数据库获取历史持仓
        query = db.query(Position).filter(Position.user_id == current_user.id)
        
        if symbol:
            query = query.filter(Position.symbol == symbol)
        
        positions = query.order_by(Position.updated_at.desc()).all()
        
        return [
            PositionResponse(
                id=position.id,
                exchange=position.exchange,
                symbol=position.symbol,
                side=position.side,
                size=position.size,
                entry_price=position.entry_price,
                mark_price=position.mark_price,
                unrealized_pnl=position.unrealized_pnl,
                margin=position.margin,
                leverage=position.leverage,
                liquidation_price=position.liquidation_price,
                created_at=position.created_at.isoformat() if position.created_at else None,
                updated_at=position.updated_at.isoformat() if position.updated_at else None
            )
            for position in positions
        ]


@router.get("/{position_id}", response_model=PositionResponse)
async def get_position(
    position_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个持仓"""
    position = db.query(Position).filter(
        Position.id == position_id,
        Position.user_id == current_user.id
    ).first()
    
    if not position:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="持仓不存在"
        )
    
    return PositionResponse(
        id=position.id,
        exchange=position.exchange,
        symbol=position.symbol,
        side=position.side,
        size=position.size,
        entry_price=position.entry_price,
        mark_price=position.mark_price,
        unrealized_pnl=position.unrealized_pnl,
        margin=position.margin,
        leverage=position.leverage,
        liquidation_price=position.liquidation_price,
        created_at=position.created_at.isoformat() if position.created_at else None,
        updated_at=position.updated_at.isoformat() if position.updated_at else None
    )


@router.get("/stats/summary")
async def get_position_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    api_key_id: Optional[int] = Query(None, description="API密钥ID")
):
    """获取持仓统计"""
    if api_key_id:
        # 从交易所获取实时持仓统计
        try:
            positions = await exchange_service.get_positions(db, api_key_id, current_user.id)
            
            total_positions = len(positions)
            total_unrealized_pnl = sum(pos['unrealized_pnl'] for pos in positions)
            total_margin = sum(pos['margin'] for pos in positions)
            
            long_positions = [pos for pos in positions if pos['side'] == 'long']
            short_positions = [pos for pos in positions if pos['side'] == 'short']
            
            return {
                "total_positions": total_positions,
                "long_positions": len(long_positions),
                "short_positions": len(short_positions),
                "total_unrealized_pnl": total_unrealized_pnl,
                "total_margin": total_margin,
                "avg_leverage": sum(pos['leverage'] for pos in positions) / total_positions if total_positions > 0 else 0
            }
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取持仓统计失败: {str(e)}"
            )
    else:
        # 从数据库获取历史统计
        positions = db.query(Position).filter(Position.user_id == current_user.id).all()
        
        total_positions = len(positions)
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in positions)
        total_margin = sum(pos.margin for pos in positions)
        
        long_positions = [pos for pos in positions if pos.side == 'long']
        short_positions = [pos for pos in positions if pos.side == 'short']
        
        return {
            "total_positions": total_positions,
            "long_positions": len(long_positions),
            "short_positions": len(short_positions),
            "total_unrealized_pnl": total_unrealized_pnl,
            "total_margin": total_margin,
            "avg_leverage": sum(pos.leverage for pos in positions) / total_positions if total_positions > 0 else 0
        }


@router.post("/sync")
async def sync_positions(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """同步持仓数据"""
    try:
        # 从交易所获取最新持仓
        positions = await exchange_service.get_positions(db, api_key_id, current_user.id)
        
        # 获取API密钥信息
        from ..models.api_key import APIKey
        api_key = db.query(APIKey).filter(
            APIKey.id == api_key_id,
            APIKey.user_id == current_user.id
        ).first()
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API密钥不存在"
            )
        
        # 更新数据库中的持仓
        updated_count = 0
        for pos_data in positions:
            # 查找现有持仓
            existing_position = db.query(Position).filter(
                Position.user_id == current_user.id,
                Position.exchange == api_key.exchange,
                Position.symbol == pos_data['symbol']
            ).first()
            
            if existing_position:
                # 更新现有持仓
                existing_position.side = pos_data['side']
                existing_position.size = pos_data['size']
                existing_position.entry_price = pos_data['entry_price']
                existing_position.mark_price = pos_data['mark_price']
                existing_position.unrealized_pnl = pos_data['unrealized_pnl']
                existing_position.margin = pos_data['margin']
                existing_position.leverage = pos_data['leverage']
                existing_position.liquidation_price = pos_data['liquidation_price']
                existing_position.updated_at = datetime.utcnow()
                updated_count += 1
            else:
                # 创建新持仓
                new_position = Position(
                    user_id=current_user.id,
                    exchange=api_key.exchange,
                    symbol=pos_data['symbol'],
                    side=pos_data['side'],
                    size=pos_data['size'],
                    entry_price=pos_data['entry_price'],
                    mark_price=pos_data['mark_price'],
                    unrealized_pnl=pos_data['unrealized_pnl'],
                    margin=pos_data['margin'],
                    leverage=pos_data['leverage'],
                    liquidation_price=pos_data['liquidation_price']
                )
                db.add(new_position)
                updated_count += 1
        
        db.commit()
        
        return {
            "message": "持仓同步成功",
            "updated_count": updated_count,
            "total_positions": len(positions)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步持仓失败: {str(e)}"
        )
