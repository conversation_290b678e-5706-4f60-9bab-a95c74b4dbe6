"""
Telegram设置API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional

from ..core.database import get_db
from ..models.user import User
from ..models.telegram_setting import TelegramSetting
from .auth import get_current_active_user

router = APIRouter()


class TelegramSettingCreate(BaseModel):
    """Telegram设置创建模型"""
    chat_id: str
    username: Optional[str] = None
    enabled: bool = True
    notify_orders: bool = True
    notify_trades: bool = True
    notify_strategies: bool = True
    notify_risks: bool = True
    notify_errors: bool = True
    max_notifications_per_hour: int = 60
    quiet_hours_start: Optional[str] = None
    quiet_hours_end: Optional[str] = None


class TelegramSettingUpdate(BaseModel):
    """Telegram设置更新模型"""
    chat_id: Optional[str] = None
    username: Optional[str] = None
    enabled: Optional[bool] = None
    notify_orders: Optional[bool] = None
    notify_trades: Optional[bool] = None
    notify_strategies: Optional[bool] = None
    notify_risks: Optional[bool] = None
    notify_errors: Optional[bool] = None
    max_notifications_per_hour: Optional[int] = None
    quiet_hours_start: Optional[str] = None
    quiet_hours_end: Optional[str] = None


class TelegramSettingResponse(BaseModel):
    """Telegram设置响应模型"""
    id: int
    chat_id: Optional[str]
    username: Optional[str]
    enabled: bool
    notify_orders: bool
    notify_trades: bool
    notify_strategies: bool
    notify_risks: bool
    notify_errors: bool
    max_notifications_per_hour: int
    quiet_hours_start: Optional[str]
    quiet_hours_end: Optional[str]
    is_verified: bool
    notification_count_today: int
    created_at: Optional[str]
    updated_at: Optional[str]


@router.get("/", response_model=Optional[TelegramSettingResponse])
async def get_telegram_setting(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取Telegram设置"""
    setting = db.query(TelegramSetting).filter(
        TelegramSetting.user_id == current_user.id
    ).first()
    
    if not setting:
        return None
    
    return TelegramSettingResponse(
        id=setting.id,
        chat_id=setting.chat_id,
        username=setting.username,
        enabled=setting.enabled,
        notify_orders=setting.notify_orders,
        notify_trades=setting.notify_trades,
        notify_strategies=setting.notify_strategies,
        notify_risks=setting.notify_risks,
        notify_errors=setting.notify_errors,
        max_notifications_per_hour=setting.max_notifications_per_hour,
        quiet_hours_start=setting.quiet_hours_start,
        quiet_hours_end=setting.quiet_hours_end,
        is_verified=setting.is_verified,
        notification_count_today=setting.notification_count_today,
        created_at=setting.created_at.isoformat() if setting.created_at else None,
        updated_at=setting.updated_at.isoformat() if setting.updated_at else None
    )


@router.post("/", response_model=TelegramSettingResponse)
async def create_telegram_setting(
    setting_data: TelegramSettingCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建Telegram设置"""
    # 检查是否已存在设置
    existing_setting = db.query(TelegramSetting).filter(
        TelegramSetting.user_id == current_user.id
    ).first()
    
    if existing_setting:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Telegram设置已存在，请使用更新接口"
        )
    
    # 验证时间格式
    if setting_data.quiet_hours_start:
        try:
            from datetime import datetime
            datetime.strptime(setting_data.quiet_hours_start, "%H:%M")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="免打扰开始时间格式错误，请使用 HH:MM 格式"
            )
    
    if setting_data.quiet_hours_end:
        try:
            from datetime import datetime
            datetime.strptime(setting_data.quiet_hours_end, "%H:%M")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="免打扰结束时间格式错误，请使用 HH:MM 格式"
            )
    
    # 创建设置
    db_setting = TelegramSetting(
        user_id=current_user.id,
        chat_id=setting_data.chat_id,
        username=setting_data.username,
        enabled=setting_data.enabled,
        notify_orders=setting_data.notify_orders,
        notify_trades=setting_data.notify_trades,
        notify_strategies=setting_data.notify_strategies,
        notify_risks=setting_data.notify_risks,
        notify_errors=setting_data.notify_errors,
        max_notifications_per_hour=setting_data.max_notifications_per_hour,
        quiet_hours_start=setting_data.quiet_hours_start,
        quiet_hours_end=setting_data.quiet_hours_end
    )
    
    db.add(db_setting)
    db.commit()
    db.refresh(db_setting)
    
    return TelegramSettingResponse(
        id=db_setting.id,
        chat_id=db_setting.chat_id,
        username=db_setting.username,
        enabled=db_setting.enabled,
        notify_orders=db_setting.notify_orders,
        notify_trades=db_setting.notify_trades,
        notify_strategies=db_setting.notify_strategies,
        notify_risks=db_setting.notify_risks,
        notify_errors=db_setting.notify_errors,
        max_notifications_per_hour=db_setting.max_notifications_per_hour,
        quiet_hours_start=db_setting.quiet_hours_start,
        quiet_hours_end=db_setting.quiet_hours_end,
        is_verified=db_setting.is_verified,
        notification_count_today=db_setting.notification_count_today,
        created_at=db_setting.created_at.isoformat() if db_setting.created_at else None,
        updated_at=db_setting.updated_at.isoformat() if db_setting.updated_at else None
    )


@router.put("/", response_model=TelegramSettingResponse)
async def update_telegram_setting(
    setting_update: TelegramSettingUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新Telegram设置"""
    setting = db.query(TelegramSetting).filter(
        TelegramSetting.user_id == current_user.id
    ).first()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Telegram设置不存在，请先创建"
        )
    
    # 验证时间格式
    if setting_update.quiet_hours_start is not None:
        try:
            from datetime import datetime
            datetime.strptime(setting_update.quiet_hours_start, "%H:%M")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="免打扰开始时间格式错误，请使用 HH:MM 格式"
            )
    
    if setting_update.quiet_hours_end is not None:
        try:
            from datetime import datetime
            datetime.strptime(setting_update.quiet_hours_end, "%H:%M")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="免打扰结束时间格式错误，请使用 HH:MM 格式"
            )
    
    # 更新字段
    update_data = setting_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(setting, field):
            setattr(setting, field, value)
    
    # 如果更改了chat_id，需要重新验证
    if setting_update.chat_id is not None and setting_update.chat_id != setting.chat_id:
        setting.is_verified = False
    
    db.commit()
    db.refresh(setting)
    
    return TelegramSettingResponse(
        id=setting.id,
        chat_id=setting.chat_id,
        username=setting.username,
        enabled=setting.enabled,
        notify_orders=setting.notify_orders,
        notify_trades=setting.notify_trades,
        notify_strategies=setting.notify_strategies,
        notify_risks=setting.notify_risks,
        notify_errors=setting.notify_errors,
        max_notifications_per_hour=setting.max_notifications_per_hour,
        quiet_hours_start=setting.quiet_hours_start,
        quiet_hours_end=setting.quiet_hours_end,
        is_verified=setting.is_verified,
        notification_count_today=setting.notification_count_today,
        created_at=setting.created_at.isoformat() if setting.created_at else None,
        updated_at=setting.updated_at.isoformat() if setting.updated_at else None
    )


@router.delete("/")
async def delete_telegram_setting(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除Telegram设置"""
    setting = db.query(TelegramSetting).filter(
        TelegramSetting.user_id == current_user.id
    ).first()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Telegram设置不存在"
        )
    
    db.delete(setting)
    db.commit()
    
    return {"message": "Telegram设置删除成功"}


@router.post("/verify")
async def verify_telegram(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """验证Telegram设置"""
    setting = db.query(TelegramSetting).filter(
        TelegramSetting.user_id == current_user.id
    ).first()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Telegram设置不存在"
        )
    
    if not setting.chat_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请先设置Chat ID"
        )
    
    # 这里应该发送测试消息到Telegram
    # 暂时直接标记为已验证
    setting.is_verified = True
    db.commit()
    
    return {"message": "Telegram验证成功"}


@router.post("/test")
async def test_telegram_notification(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """测试Telegram通知"""
    setting = db.query(TelegramSetting).filter(
        TelegramSetting.user_id == current_user.id
    ).first()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Telegram设置不存在"
        )
    
    if not setting.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请先验证Telegram设置"
        )
    
    # 这里应该发送测试消息到Telegram
    # 暂时返回成功状态
    
    return {"message": "测试消息发送成功"}
