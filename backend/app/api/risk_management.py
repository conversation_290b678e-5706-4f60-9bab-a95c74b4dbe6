"""
风控管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..core.database import get_db
from ..models.user import User
from ..models.risk_event_log import RiskEventLog, RiskEventType, RiskEventSeverity, RiskEventAction
from .auth import get_current_active_user

router = APIRouter()


class RiskEventResponse(BaseModel):
    """风控事件响应模型"""
    id: int
    event_type: str
    severity: str
    action_taken: str
    trigger_value: Optional[float]
    threshold_value: Optional[float]
    symbol: Optional[str]
    exchange: Optional[str]
    title: str
    description: str
    details: Optional[Dict[str, Any]]
    is_resolved: bool
    resolved_at: Optional[str]
    notification_sent: bool
    occurred_at: Optional[str]


class RiskRuleCreate(BaseModel):
    """风控规则创建模型"""
    rule_type: str
    threshold_value: float
    symbol: Optional[str] = None
    exchange: Optional[str] = None
    action: str = "block"
    enabled: bool = True


class RiskStats(BaseModel):
    """风控统计模型"""
    total_events: int
    critical_events: int
    unresolved_events: int
    events_today: int
    most_common_type: Optional[str]
    avg_events_per_day: float


@router.get("/events", response_model=List[RiskEventResponse])
async def get_risk_events(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    severity: Optional[str] = Query(None, description="严重程度筛选"),
    event_type: Optional[str] = Query(None, description="事件类型筛选"),
    resolved: Optional[bool] = Query(None, description="是否已解决"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取风控事件列表"""
    query = db.query(RiskEventLog).filter(RiskEventLog.user_id == current_user.id)
    
    if severity:
        query = query.filter(RiskEventLog.severity == severity)
    
    if event_type:
        query = query.filter(RiskEventLog.event_type == event_type)
    
    if resolved is not None:
        query = query.filter(RiskEventLog.is_resolved == resolved)
    
    events = query.order_by(RiskEventLog.occurred_at.desc()).limit(limit).all()
    
    return [
        RiskEventResponse(
            id=event.id,
            event_type=event.event_type.value if event.event_type else "",
            severity=event.severity.value if event.severity else "",
            action_taken=event.action_taken.value if event.action_taken else "",
            trigger_value=event.trigger_value,
            threshold_value=event.threshold_value,
            symbol=event.symbol,
            exchange=event.exchange,
            title=event.title,
            description=event.description,
            details=event.details,
            is_resolved=event.is_resolved,
            resolved_at=event.resolved_at.isoformat() if event.resolved_at else None,
            notification_sent=event.notification_sent,
            occurred_at=event.occurred_at.isoformat() if event.occurred_at else None
        )
        for event in events
    ]


@router.get("/events/{event_id}", response_model=RiskEventResponse)
async def get_risk_event(
    event_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个风控事件"""
    event = db.query(RiskEventLog).filter(
        RiskEventLog.id == event_id,
        RiskEventLog.user_id == current_user.id
    ).first()
    
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="风控事件不存在"
        )
    
    return RiskEventResponse(
        id=event.id,
        event_type=event.event_type.value if event.event_type else "",
        severity=event.severity.value if event.severity else "",
        action_taken=event.action_taken.value if event.action_taken else "",
        trigger_value=event.trigger_value,
        threshold_value=event.threshold_value,
        symbol=event.symbol,
        exchange=event.exchange,
        title=event.title,
        description=event.description,
        details=event.details,
        is_resolved=event.is_resolved,
        resolved_at=event.resolved_at.isoformat() if event.resolved_at else None,
        notification_sent=event.notification_sent,
        occurred_at=event.occurred_at.isoformat() if event.occurred_at else None
    )


@router.post("/events/{event_id}/resolve")
async def resolve_risk_event(
    event_id: int,
    resolution_notes: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """解决风控事件"""
    event = db.query(RiskEventLog).filter(
        RiskEventLog.id == event_id,
        RiskEventLog.user_id == current_user.id
    ).first()
    
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="风控事件不存在"
        )
    
    if event.is_resolved:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="事件已经解决"
        )
    
    # 标记为已解决
    event.is_resolved = True
    event.resolved_at = datetime.utcnow()
    event.resolved_by = current_user.username
    event.resolution_notes = resolution_notes
    
    db.commit()
    
    return {"message": "风控事件已解决"}


@router.get("/stats", response_model=RiskStats)
async def get_risk_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    days: int = Query(30, description="统计天数")
):
    """获取风控统计"""
    start_date = datetime.utcnow() - timedelta(days=days)
    today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    
    # 总事件数
    total_events = db.query(RiskEventLog).filter(
        and_(
            RiskEventLog.user_id == current_user.id,
            RiskEventLog.occurred_at >= start_date
        )
    ).count()
    
    # 严重事件数
    critical_events = db.query(RiskEventLog).filter(
        and_(
            RiskEventLog.user_id == current_user.id,
            RiskEventLog.occurred_at >= start_date,
            RiskEventLog.severity == RiskEventSeverity.CRITICAL
        )
    ).count()
    
    # 未解决事件数
    unresolved_events = db.query(RiskEventLog).filter(
        and_(
            RiskEventLog.user_id == current_user.id,
            RiskEventLog.occurred_at >= start_date,
            RiskEventLog.is_resolved == False
        )
    ).count()
    
    # 今日事件数
    events_today = db.query(RiskEventLog).filter(
        and_(
            RiskEventLog.user_id == current_user.id,
            RiskEventLog.occurred_at >= today_start
        )
    ).count()
    
    # 最常见的事件类型
    most_common_result = db.query(
        RiskEventLog.event_type,
        func.count(RiskEventLog.id).label('count')
    ).filter(
        and_(
            RiskEventLog.user_id == current_user.id,
            RiskEventLog.occurred_at >= start_date
        )
    ).group_by(
        RiskEventLog.event_type
    ).order_by(
        func.count(RiskEventLog.id).desc()
    ).first()
    
    most_common_type = most_common_result.event_type.value if most_common_result else None
    
    # 平均每日事件数
    avg_events_per_day = total_events / days if days > 0 else 0
    
    return RiskStats(
        total_events=total_events,
        critical_events=critical_events,
        unresolved_events=unresolved_events,
        events_today=events_today,
        most_common_type=most_common_type,
        avg_events_per_day=avg_events_per_day
    )


@router.get("/rules")
async def get_risk_rules(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取风控规则"""
    # 这里应该从数据库或配置中获取用户的风控规则
    # 暂时返回默认规则
    
    from ..core.config import settings
    
    default_rules = [
        {
            "id": 1,
            "rule_type": "max_order_amount",
            "name": "最大单笔下单金额",
            "threshold_value": settings.default_max_order_amount,
            "action": "block",
            "enabled": True,
            "description": "限制单笔订单的最大金额"
        },
        {
            "id": 2,
            "rule_type": "max_daily_loss",
            "name": "最大日亏损",
            "threshold_value": settings.default_max_daily_loss,
            "action": "pause_strategy",
            "enabled": True,
            "description": "当日亏损达到阈值时暂停策略"
        },
        {
            "id": 3,
            "rule_type": "max_orders_limit",
            "name": "最大挂单数量",
            "threshold_value": settings.default_max_orders_per_strategy,
            "action": "block",
            "enabled": True,
            "description": "限制同时存在的挂单数量"
        },
        {
            "id": 4,
            "rule_type": "order_rate_limit",
            "name": "下单频率限制",
            "threshold_value": settings.default_order_rate_limit,
            "action": "block",
            "enabled": True,
            "description": "限制每分钟的下单次数"
        }
    ]
    
    return default_rules


@router.post("/rules")
async def create_risk_rule(
    rule_data: RiskRuleCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建风控规则"""
    # 验证规则类型
    valid_types = [e.value for e in RiskEventType]
    if rule_data.rule_type not in valid_types:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的规则类型，支持: {', '.join(valid_types)}"
        )
    
    # 验证动作类型
    valid_actions = [e.value for e in RiskEventAction]
    if rule_data.action not in valid_actions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的动作类型，支持: {', '.join(valid_actions)}"
        )
    
    # 这里应该将规则保存到数据库
    # 暂时返回成功状态
    
    return {
        "message": "风控规则创建成功",
        "rule_id": 999,  # 模拟ID
        "rule_type": rule_data.rule_type,
        "threshold_value": rule_data.threshold_value
    }


@router.get("/check")
async def check_risk_status(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """检查当前风险状态"""
    # 检查未解决的严重事件
    critical_events = db.query(RiskEventLog).filter(
        and_(
            RiskEventLog.user_id == current_user.id,
            RiskEventLog.severity == RiskEventSeverity.CRITICAL,
            RiskEventLog.is_resolved == False
        )
    ).count()
    
    # 检查今日事件数
    today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
    events_today = db.query(RiskEventLog).filter(
        and_(
            RiskEventLog.user_id == current_user.id,
            RiskEventLog.occurred_at >= today_start
        )
    ).count()
    
    # 确定风险等级
    risk_level = "low"
    if critical_events > 0:
        risk_level = "critical"
    elif events_today > 10:
        risk_level = "high"
    elif events_today > 5:
        risk_level = "medium"
    
    return {
        "risk_level": risk_level,
        "critical_events": critical_events,
        "events_today": events_today,
        "can_trade": risk_level != "critical",
        "recommendations": [
            "检查未解决的严重风控事件" if critical_events > 0 else None,
            "今日风控事件较多，建议检查策略配置" if events_today > 5 else None
        ]
    }
