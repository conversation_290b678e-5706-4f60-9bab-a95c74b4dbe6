"""
API密钥管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional

from ..core.database import get_db
from ..core.security import encrypt_api_key, decrypt_api_key
from ..models.user import User
from ..models.api_key import APIKey
from ..services.exchange_service import exchange_service
from .auth import get_current_active_user

router = APIRouter()


class APIKeyCreate(BaseModel):
    """API密钥创建模型"""
    name: str
    exchange: str  # binance, okx
    api_key: str
    secret_key: str
    passphrase: Optional[str] = None  # OKX需要
    is_testnet: bool = False
    can_trade: bool = True
    can_read: bool = True


class APIKeyUpdate(BaseModel):
    """API密钥更新模型"""
    name: Optional[str] = None
    is_active: Optional[bool] = None
    can_trade: Optional[bool] = None
    can_read: Optional[bool] = None


class APIKeyResponse(BaseModel):
    """API密钥响应模型"""
    id: int
    name: str
    exchange: str
    is_testnet: bool
    can_trade: bool
    can_read: bool
    is_active: bool
    last_used: Optional[str]
    created_at: Optional[str]


@router.get("/", response_model=List[APIKeyResponse])
async def get_api_keys(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户的API密钥列表"""
    api_keys = db.query(APIKey).filter(APIKey.user_id == current_user.id).all()
    
    return [
        APIKeyResponse(
            id=key.id,
            name=key.name,
            exchange=key.exchange,
            is_testnet=key.is_testnet,
            can_trade=key.can_trade,
            can_read=key.can_read,
            is_active=key.is_active,
            last_used=key.last_used.isoformat() if key.last_used else None,
            created_at=key.created_at.isoformat() if key.created_at else None
        )
        for key in api_keys
    ]


@router.post("/", response_model=APIKeyResponse)
async def create_api_key(
    api_key_data: APIKeyCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建API密钥"""
    # 验证交易所
    if api_key_data.exchange not in ["binance", "okx"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的交易所"
        )
    
    # 检查名称是否重复
    existing_key = db.query(APIKey).filter(
        APIKey.user_id == current_user.id,
        APIKey.name == api_key_data.name
    ).first()
    
    if existing_key:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="API密钥名称已存在"
        )
    
    try:
        # 加密存储密钥
        encrypted_api_key = encrypt_api_key(api_key_data.api_key)
        encrypted_secret_key = encrypt_api_key(api_key_data.secret_key)
        encrypted_passphrase = None
        
        if api_key_data.passphrase:
            encrypted_passphrase = encrypt_api_key(api_key_data.passphrase)
        
        # 创建API密钥记录
        db_api_key = APIKey(
            user_id=current_user.id,
            name=api_key_data.name,
            exchange=api_key_data.exchange,
            encrypted_api_key=encrypted_api_key,
            encrypted_secret_key=encrypted_secret_key,
            encrypted_passphrase=encrypted_passphrase,
            is_testnet=api_key_data.is_testnet,
            can_trade=api_key_data.can_trade,
            can_read=api_key_data.can_read
        )
        
        db.add(db_api_key)
        db.commit()
        db.refresh(db_api_key)
        
        return APIKeyResponse(
            id=db_api_key.id,
            name=db_api_key.name,
            exchange=db_api_key.exchange,
            is_testnet=db_api_key.is_testnet,
            can_trade=db_api_key.can_trade,
            can_read=db_api_key.can_read,
            is_active=db_api_key.is_active,
            last_used=None,
            created_at=db_api_key.created_at.isoformat() if db_api_key.created_at else None
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"加密存储失败: {str(e)}"
        )


@router.get("/{api_key_id}", response_model=APIKeyResponse)
async def get_api_key(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个API密钥"""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )
    
    return APIKeyResponse(
        id=api_key.id,
        name=api_key.name,
        exchange=api_key.exchange,
        is_testnet=api_key.is_testnet,
        can_trade=api_key.can_trade,
        can_read=api_key.can_read,
        is_active=api_key.is_active,
        last_used=api_key.last_used.isoformat() if api_key.last_used else None,
        created_at=api_key.created_at.isoformat() if api_key.created_at else None
    )


@router.put("/{api_key_id}", response_model=APIKeyResponse)
async def update_api_key(
    api_key_id: int,
    api_key_update: APIKeyUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新API密钥"""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )
    
    # 更新字段
    if api_key_update.name is not None:
        # 检查名称是否重复
        existing_key = db.query(APIKey).filter(
            APIKey.user_id == current_user.id,
            APIKey.name == api_key_update.name,
            APIKey.id != api_key_id
        ).first()
        
        if existing_key:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="API密钥名称已存在"
            )
        
        api_key.name = api_key_update.name
    
    if api_key_update.is_active is not None:
        api_key.is_active = api_key_update.is_active
    
    if api_key_update.can_trade is not None:
        api_key.can_trade = api_key_update.can_trade
    
    if api_key_update.can_read is not None:
        api_key.can_read = api_key_update.can_read
    
    db.commit()
    db.refresh(api_key)
    
    return APIKeyResponse(
        id=api_key.id,
        name=api_key.name,
        exchange=api_key.exchange,
        is_testnet=api_key.is_testnet,
        can_trade=api_key.can_trade,
        can_read=api_key.can_read,
        is_active=api_key.is_active,
        last_used=api_key.last_used.isoformat() if api_key.last_used else None,
        created_at=api_key.created_at.isoformat() if api_key.created_at else None
    )


@router.delete("/{api_key_id}")
async def delete_api_key(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除API密钥"""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )
    
    # 检查是否有关联的活跃订单或策略
    active_orders = [o for o in api_key.orders if o.is_active]
    if active_orders:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该API密钥有活跃订单，无法删除"
        )
    
    db.delete(api_key)
    db.commit()
    
    return {"message": "API密钥删除成功"}


@router.post("/{api_key_id}/test")
async def test_api_key(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """测试API密钥连接"""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )
    
    try:
        # 使用交易所服务测试连接
        success = await exchange_service.test_api_key(db, api_key_id, current_user.id)

        if success:
            # 更新最后使用时间
            from datetime import datetime
            api_key.last_used = datetime.utcnow()
            db.commit()

            return {
                "status": "success",
                "message": "API密钥连接测试成功",
                "exchange": api_key.exchange,
                "permissions": {
                    "can_read": api_key.can_read,
                    "can_trade": api_key.can_trade
                }
            }
        else:
            return {
                "status": "error",
                "message": "API密钥连接测试失败",
                "exchange": api_key.exchange
            }

    except Exception as e:
        return {
            "status": "error",
            "message": f"API密钥连接测试失败: {str(e)}",
            "exchange": api_key.exchange
        }


@router.get("/{api_key_id}/balances")
async def get_api_key_balances(
    api_key_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取API密钥对应的账户余额"""
    api_key = db.query(APIKey).filter(
        APIKey.id == api_key_id,
        APIKey.user_id == current_user.id
    ).first()

    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在"
        )

    if not api_key.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="API密钥未激活"
        )

    if not api_key.can_read:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="API密钥没有读取权限"
        )

    try:
        balances = await exchange_service.get_balances(db, api_key_id, current_user.id)

        # 更新最后使用时间
        from datetime import datetime
        api_key.last_used = datetime.utcnow()
        db.commit()

        return {
            "exchange": api_key.exchange,
            "balances": balances
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取余额失败: {str(e)}"
        )
