"""
订单管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from ..core.database import get_db
from ..models.user import User
from ..models.order import Order, OrderType, OrderSide, OrderStatus, MarketType
from ..services.exchange_service import exchange_service
from .auth import get_current_active_user

router = APIRouter()


class OrderCreate(BaseModel):
    """订单创建模型"""
    api_key_id: int
    strategy_id: Optional[int] = None
    symbol: str
    market_type: str = "spot"
    order_type: str
    side: str
    amount: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    take_profit_price: Optional[float] = None
    stop_loss_price: Optional[float] = None
    notes: Optional[str] = None


class OrderResponse(BaseModel):
    """订单响应模型"""
    id: int
    exchange: str
    exchange_order_id: Optional[str]
    symbol: str
    market_type: str
    order_type: str
    side: str
    amount: float
    price: Optional[float]
    filled_amount: float
    avg_price: Optional[float]
    fee: float
    status: str
    created_at: Optional[str]
    executed_at: Optional[str]
    notes: Optional[str]


@router.get("/", response_model=List[OrderResponse])
async def get_orders(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    symbol: Optional[str] = Query(None, description="交易对筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    limit: int = Query(100, description="返回数量限制")
):
    """获取用户订单列表"""
    query = db.query(Order).filter(Order.user_id == current_user.id)
    
    if symbol:
        query = query.filter(Order.symbol == symbol)
    
    if status:
        query = query.filter(Order.status == status)
    
    orders = query.order_by(Order.created_at.desc()).limit(limit).all()
    
    return [
        OrderResponse(
            id=order.id,
            exchange=order.exchange,
            exchange_order_id=order.exchange_order_id,
            symbol=order.symbol,
            market_type=order.market_type.value if order.market_type else "spot",
            order_type=order.order_type.value if order.order_type else "",
            side=order.side.value if order.side else "",
            amount=order.amount,
            price=order.price,
            filled_amount=order.filled_amount,
            avg_price=order.avg_price,
            fee=order.fee,
            status=order.status.value if order.status else "",
            created_at=order.created_at.isoformat() if order.created_at else None,
            executed_at=order.executed_at.isoformat() if order.executed_at else None,
            notes=order.notes
        )
        for order in orders
    ]


@router.post("/", response_model=OrderResponse)
async def create_order(
    order_data: OrderCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建订单"""
    # 验证API密钥是否属于当前用户
    from ..models.api_key import APIKey
    api_key = db.query(APIKey).filter(
        APIKey.id == order_data.api_key_id,
        APIKey.user_id == current_user.id,
        APIKey.is_active == True
    ).first()
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API密钥不存在或未激活"
        )
    
    # 验证策略是否属于当前用户
    if order_data.strategy_id:
        from ..models.strategy import Strategy
        strategy = db.query(Strategy).filter(
            Strategy.id == order_data.strategy_id,
            Strategy.user_id == current_user.id
        ).first()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="策略不存在"
            )
    
    # 验证订单参数
    try:
        order_type = OrderType(order_data.order_type)
        order_side = OrderSide(order_data.side)
        market_type = MarketType(order_data.market_type)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"无效的订单参数: {str(e)}"
        )
    
    # 限价单必须有价格
    if order_type == OrderType.LIMIT and order_data.price is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="限价单必须指定价格"
        )
    
    # 调用交易所API创建订单
    try:
        order_result = await exchange_service.create_order(
            db=db,
            api_key_id=order_data.api_key_id,
            user_id=current_user.id,
            symbol=order_data.symbol,
            side=order_side.value,
            order_type=order_type.value,
            amount=order_data.amount,
            price=order_data.price,
            stop_price=order_data.stop_price,
            market_type=market_type.value,
            strategy_id=order_data.strategy_id,
            notes=order_data.notes
        )

        # 从数据库获取创建的订单
        db_order = db.query(Order).filter(Order.id == order_result['id']).first()

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建订单失败: {str(e)}"
        )

    return OrderResponse(
        id=db_order.id,
        exchange=db_order.exchange,
        exchange_order_id=db_order.exchange_order_id,
        symbol=db_order.symbol,
        market_type=db_order.market_type.value,
        order_type=db_order.order_type.value,
        side=db_order.side.value,
        amount=db_order.amount,
        price=db_order.price,
        filled_amount=db_order.filled_amount,
        avg_price=db_order.avg_price,
        fee=db_order.fee,
        status=db_order.status.value,
        created_at=db_order.created_at.isoformat() if db_order.created_at else None,
        executed_at=db_order.executed_at.isoformat() if db_order.executed_at else None,
        notes=db_order.notes
    )


@router.get("/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个订单"""
    order = db.query(Order).filter(
        Order.id == order_id,
        Order.user_id == current_user.id
    ).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    return OrderResponse(
        id=order.id,
        exchange=order.exchange,
        exchange_order_id=order.exchange_order_id,
        symbol=order.symbol,
        market_type=order.market_type.value if order.market_type else "spot",
        order_type=order.order_type.value if order.order_type else "",
        side=order.side.value if order.side else "",
        amount=order.amount,
        price=order.price,
        filled_amount=order.filled_amount,
        avg_price=order.avg_price,
        fee=order.fee,
        status=order.status.value if order.status else "",
        created_at=order.created_at.isoformat() if order.created_at else None,
        executed_at=order.executed_at.isoformat() if order.executed_at else None,
        notes=order.notes
    )


@router.delete("/{order_id}")
async def cancel_order(
    order_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """取消订单"""
    order = db.query(Order).filter(
        Order.id == order_id,
        Order.user_id == current_user.id
    ).first()
    
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    if not order.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="订单不是活跃状态，无法取消"
        )
    
    # 调用交易所API取消订单
    try:
        success = await exchange_service.cancel_order(
            db=db,
            api_key_id=order.api_key_id,
            user_id=current_user.id,
            order_id=order_id
        )

        if success:
            return {"message": "订单取消成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="订单取消失败"
            )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"订单取消失败: {str(e)}"
        )


@router.get("/active/count")
async def get_active_orders_count(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取活跃订单数量"""
    count = db.query(Order).filter(
        Order.user_id == current_user.id,
        Order.status.in_([OrderStatus.PENDING, OrderStatus.OPEN, OrderStatus.PARTIALLY_FILLED])
    ).count()
    
    return {"active_orders_count": count}
