"""
策略管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..core.database import get_db
from ..models.user import User
from ..models.strategy import Strategy, StrategyType, StrategyStatus
from .auth import get_current_active_user

router = APIRouter()


class StrategyCreate(BaseModel):
    """策略创建模型"""
    name: str
    description: Optional[str] = None
    strategy_type: str
    exchange: str
    symbol: str
    market_type: str = "spot"
    base_amount: float
    max_amount: Optional[float] = None
    parameters: Optional[Dict[str, Any]] = None
    max_loss: Optional[float] = None
    stop_loss_percentage: Optional[float] = None
    take_profit_percentage: Optional[float] = None
    notes: Optional[str] = None


class StrategyUpdate(BaseModel):
    """策略更新模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    base_amount: Optional[float] = None
    max_amount: Optional[float] = None
    parameters: Optional[Dict[str, Any]] = None
    max_loss: Optional[float] = None
    stop_loss_percentage: Optional[float] = None
    take_profit_percentage: Optional[float] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = None


class StrategyResponse(BaseModel):
    """策略响应模型"""
    id: int
    name: str
    description: Optional[str]
    strategy_type: str
    exchange: str
    symbol: str
    market_type: str
    base_amount: float
    max_amount: Optional[float]
    parameters: Optional[Dict[str, Any]]
    status: str
    is_active: bool
    total_orders: int
    total_trades: int
    total_profit: float
    total_loss: float
    win_rate: float
    created_at: Optional[str]
    started_at: Optional[str]
    last_executed_at: Optional[str]
    last_error: Optional[str]


@router.get("/", response_model=List[StrategyResponse])
async def get_strategies(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    status: Optional[str] = Query(None, description="状态筛选"),
    strategy_type: Optional[str] = Query(None, description="策略类型筛选")
):
    """获取用户策略列表"""
    query = db.query(Strategy).filter(Strategy.user_id == current_user.id)
    
    if status:
        query = query.filter(Strategy.status == status)
    
    if strategy_type:
        query = query.filter(Strategy.strategy_type == strategy_type)
    
    strategies = query.order_by(Strategy.created_at.desc()).all()
    
    return [
        StrategyResponse(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            strategy_type=strategy.strategy_type.value if strategy.strategy_type else "",
            exchange=strategy.exchange,
            symbol=strategy.symbol,
            market_type=strategy.market_type,
            base_amount=strategy.base_amount,
            max_amount=strategy.max_amount,
            parameters=strategy.parameters,
            status=strategy.status.value if strategy.status else "",
            is_active=strategy.is_active,
            total_orders=strategy.total_orders,
            total_trades=strategy.total_trades,
            total_profit=strategy.total_profit,
            total_loss=strategy.total_loss,
            win_rate=strategy.win_rate,
            created_at=strategy.created_at.isoformat() if strategy.created_at else None,
            started_at=strategy.started_at.isoformat() if strategy.started_at else None,
            last_executed_at=strategy.last_executed_at.isoformat() if strategy.last_executed_at else None,
            last_error=strategy.last_error
        )
        for strategy in strategies
    ]


@router.post("/", response_model=StrategyResponse)
async def create_strategy(
    strategy_data: StrategyCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建策略"""
    # 验证策略类型
    try:
        strategy_type = StrategyType(strategy_data.strategy_type)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的策略类型"
        )
    
    # 检查策略名称是否重复
    existing_strategy = db.query(Strategy).filter(
        Strategy.user_id == current_user.id,
        Strategy.name == strategy_data.name
    ).first()
    
    if existing_strategy:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略名称已存在"
        )
    
    # 验证参数
    if strategy_data.base_amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="基础投入金额必须大于0"
        )
    
    if strategy_data.max_amount and strategy_data.max_amount < strategy_data.base_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="最大投入金额不能小于基础投入金额"
        )
    
    # 创建策略
    db_strategy = Strategy(
        user_id=current_user.id,
        name=strategy_data.name,
        description=strategy_data.description,
        strategy_type=strategy_type,
        exchange=strategy_data.exchange,
        symbol=strategy_data.symbol,
        market_type=strategy_data.market_type,
        base_amount=strategy_data.base_amount,
        max_amount=strategy_data.max_amount,
        parameters=strategy_data.parameters,
        max_loss=strategy_data.max_loss,
        stop_loss_percentage=strategy_data.stop_loss_percentage,
        take_profit_percentage=strategy_data.take_profit_percentage,
        notes=strategy_data.notes
    )
    
    db.add(db_strategy)
    db.commit()
    db.refresh(db_strategy)
    
    return StrategyResponse(
        id=db_strategy.id,
        name=db_strategy.name,
        description=db_strategy.description,
        strategy_type=db_strategy.strategy_type.value,
        exchange=db_strategy.exchange,
        symbol=db_strategy.symbol,
        market_type=db_strategy.market_type,
        base_amount=db_strategy.base_amount,
        max_amount=db_strategy.max_amount,
        parameters=db_strategy.parameters,
        status=db_strategy.status.value,
        is_active=db_strategy.is_active,
        total_orders=db_strategy.total_orders,
        total_trades=db_strategy.total_trades,
        total_profit=db_strategy.total_profit,
        total_loss=db_strategy.total_loss,
        win_rate=db_strategy.win_rate,
        created_at=db_strategy.created_at.isoformat() if db_strategy.created_at else None,
        started_at=db_strategy.started_at.isoformat() if db_strategy.started_at else None,
        last_executed_at=db_strategy.last_executed_at.isoformat() if db_strategy.last_executed_at else None,
        last_error=db_strategy.last_error
    )


@router.get("/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )
    
    return StrategyResponse(
        id=strategy.id,
        name=strategy.name,
        description=strategy.description,
        strategy_type=strategy.strategy_type.value if strategy.strategy_type else "",
        exchange=strategy.exchange,
        symbol=strategy.symbol,
        market_type=strategy.market_type,
        base_amount=strategy.base_amount,
        max_amount=strategy.max_amount,
        parameters=strategy.parameters,
        status=strategy.status.value if strategy.status else "",
        is_active=strategy.is_active,
        total_orders=strategy.total_orders,
        total_trades=strategy.total_trades,
        total_profit=strategy.total_profit,
        total_loss=strategy.total_loss,
        win_rate=strategy.win_rate,
        created_at=strategy.created_at.isoformat() if strategy.created_at else None,
        started_at=strategy.started_at.isoformat() if strategy.started_at else None,
        last_executed_at=strategy.last_executed_at.isoformat() if strategy.last_executed_at else None,
        last_error=strategy.last_error
    )


@router.put("/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(
    strategy_id: int,
    strategy_update: StrategyUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )
    
    # 如果策略正在运行，限制可修改的字段
    if strategy.is_running:
        allowed_fields = ['notes', 'max_loss', 'stop_loss_percentage', 'take_profit_percentage']
        update_data = strategy_update.dict(exclude_unset=True)
        
        for field in update_data:
            if field not in allowed_fields:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"策略运行中，无法修改字段: {field}"
                )
    
    # 更新字段
    update_data = strategy_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(strategy, field):
            setattr(strategy, field, value)
    
    db.commit()
    db.refresh(strategy)
    
    return StrategyResponse(
        id=strategy.id,
        name=strategy.name,
        description=strategy.description,
        strategy_type=strategy.strategy_type.value if strategy.strategy_type else "",
        exchange=strategy.exchange,
        symbol=strategy.symbol,
        market_type=strategy.market_type,
        base_amount=strategy.base_amount,
        max_amount=strategy.max_amount,
        parameters=strategy.parameters,
        status=strategy.status.value if strategy.status else "",
        is_active=strategy.is_active,
        total_orders=strategy.total_orders,
        total_trades=strategy.total_trades,
        total_profit=strategy.total_profit,
        total_loss=strategy.total_loss,
        win_rate=strategy.win_rate,
        created_at=strategy.created_at.isoformat() if strategy.created_at else None,
        started_at=strategy.started_at.isoformat() if strategy.started_at else None,
        last_executed_at=strategy.last_executed_at.isoformat() if strategy.last_executed_at else None,
        last_error=strategy.last_error
    )


@router.post("/{strategy_id}/start")
async def start_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """启动策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )
    
    if strategy.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略已在运行中"
        )
    
    if not strategy.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略已被禁用"
        )
    
    # 启动策略
    strategy.status = StrategyStatus.RUNNING
    strategy.started_at = datetime.utcnow()
    strategy.last_error = None
    
    db.commit()
    
    return {"message": "策略启动成功"}


@router.post("/{strategy_id}/stop")
async def stop_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """停止策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )
    
    if not strategy.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略未在运行中"
        )
    
    # 停止策略
    strategy.status = StrategyStatus.STOPPED
    strategy.stopped_at = datetime.utcnow()
    
    db.commit()
    
    return {"message": "策略停止成功"}


@router.delete("/{strategy_id}")
async def delete_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()
    
    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )
    
    if strategy.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请先停止策略再删除"
        )
    
    db.delete(strategy)
    db.commit()
    
    return {"message": "策略删除成功"}
