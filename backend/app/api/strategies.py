"""
策略管理API
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..core.database import get_db
from ..models.user import User
from ..models.strategy import Strategy, StrategyType, StrategyStatus
from ..services.strategy_service import (
    StrategyFactory, BacktestService, StrategyValidationService
)
from .auth import get_current_active_user

router = APIRouter()


class StrategyCreate(BaseModel):
    """策略创建模型"""
    name: str
    description: Optional[str] = None
    strategy_type: str
    exchange: str
    symbol: str
    market_type: str = "spot"
    base_amount: float
    max_amount: Optional[float] = None
    parameters: Optional[Dict[str, Any]] = None
    max_loss: Optional[float] = None
    stop_loss_percentage: Optional[float] = None
    take_profit_percentage: Optional[float] = None
    notes: Optional[str] = None


class StrategyUpdate(BaseModel):
    """策略更新模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    base_amount: Optional[float] = None
    max_amount: Optional[float] = None
    parameters: Optional[Dict[str, Any]] = None
    max_loss: Optional[float] = None
    stop_loss_percentage: Optional[float] = None
    take_profit_percentage: Optional[float] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = None


class StrategyResponse(BaseModel):
    """策略响应模型"""
    id: int
    name: str
    description: Optional[str]
    strategy_type: str
    exchange: str
    symbol: str
    market_type: str
    base_amount: float
    max_amount: Optional[float]
    parameters: Optional[Dict[str, Any]]
    status: str
    is_active: bool
    total_orders: int
    total_trades: int
    total_profit: float
    total_loss: float
    win_rate: float
    created_at: Optional[str]
    started_at: Optional[str]
    last_executed_at: Optional[str]
    last_error: Optional[str]


class BacktestRequest(BaseModel):
    """回测请求模型"""
    strategy_type: str
    symbol: str
    initial_capital: float
    parameters: Dict[str, Any]
    start_date: datetime
    end_date: datetime
    interval: str = "1h"
    fee_rate: float = 0.001


class ParameterOptimizationRequest(BaseModel):
    """参数优化请求模型"""
    strategy_type: str
    symbol: str
    initial_capital: float
    parameter_ranges: Dict[str, List]
    start_date: datetime
    end_date: datetime
    optimization_target: str = "sharpe_ratio"


@router.get("/", response_model=List[StrategyResponse])
async def get_strategies(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    status: Optional[str] = Query(None, description="状态筛选"),
    strategy_type: Optional[str] = Query(None, description="策略类型筛选")
):
    """获取用户策略列表"""
    query = db.query(Strategy).filter(Strategy.user_id == current_user.id)

    if status:
        query = query.filter(Strategy.status == status)

    if strategy_type:
        query = query.filter(Strategy.strategy_type == strategy_type)

    strategies = query.order_by(Strategy.created_at.desc()).all()

    return [
        StrategyResponse(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            strategy_type=strategy.strategy_type.value if strategy.strategy_type else "",
            exchange=strategy.exchange,
            symbol=strategy.symbol,
            market_type=strategy.market_type,
            base_amount=strategy.base_amount,
            max_amount=strategy.max_amount,
            parameters=strategy.parameters,
            status=strategy.status.value if strategy.status else "",
            is_active=strategy.is_active,
            total_orders=strategy.total_orders,
            total_trades=strategy.total_trades,
            total_profit=strategy.total_profit,
            total_loss=strategy.total_loss,
            win_rate=strategy.win_rate,
            created_at=strategy.created_at.isoformat() if strategy.created_at else None,
            started_at=strategy.started_at.isoformat() if strategy.started_at else None,
            last_executed_at=strategy.last_executed_at.isoformat() if strategy.last_executed_at else None,
            last_error=strategy.last_error
        )
        for strategy in strategies
    ]


# 策略类型和验证相关的路由（必须在参数路由之前）
@router.get("/types")
async def get_strategy_types(
    current_user: User = Depends(get_current_active_user)
):
    """获取可用策略类型"""
    return StrategyFactory.get_available_strategies()


class StrategyValidationRequest(BaseModel):
    """策略验证请求模型"""
    strategy_type: str
    parameters: Dict[str, Any]


@router.post("/validate")
async def validate_strategy(
    request: StrategyValidationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """验证策略配置"""
    validation_result = StrategyValidationService.validate_strategy_config(
        request.strategy_type, request.parameters
    )

    if validation_result['valid']:
        # 估算资金需求
        capital_requirements = StrategyValidationService.estimate_capital_requirements(
            request.strategy_type, request.parameters
        )
        validation_result['capital_requirements'] = capital_requirements

    return validation_result


@router.post("/backtest")
async def run_backtest(
    request: BacktestRequest,
    current_user: User = Depends(get_current_active_user)
):
    """运行策略回测"""
    try:
        backtest_service = BacktestService()
        result = backtest_service.run_backtest(
            strategy_type=request.strategy_type,
            symbol=request.symbol,
            initial_capital=request.initial_capital,
            parameters=request.parameters,
            start_date=request.start_date,
            end_date=request.end_date,
            interval=request.interval,
            fee_rate=request.fee_rate
        )

        return {
            "success": True,
            "result": result.to_dict()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"回测失败: {str(e)}"
        )


@router.post("/optimize")
async def optimize_parameters(
    request: ParameterOptimizationRequest,
    current_user: User = Depends(get_current_active_user)
):
    """参数优化"""
    try:
        backtest_service = BacktestService()
        result = backtest_service.optimize_parameters(
            strategy_type=request.strategy_type,
            symbol=request.symbol,
            initial_capital=request.initial_capital,
            parameter_ranges=request.parameter_ranges,
            start_date=request.start_date,
            end_date=request.end_date,
            optimization_target=request.optimization_target
        )

        return {
            "success": True,
            "result": result
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"参数优化失败: {str(e)}"
        )


@router.post("/", response_model=StrategyResponse)
async def create_strategy(
    strategy_data: StrategyCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建策略"""
    # 验证策略类型
    try:
        strategy_type = StrategyType(strategy_data.strategy_type)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的策略类型"
        )

    # 检查策略名称是否重复
    existing_strategy = db.query(Strategy).filter(
        Strategy.user_id == current_user.id,
        Strategy.name == strategy_data.name
    ).first()

    if existing_strategy:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略名称已存在"
        )

    # 验证参数
    if strategy_data.base_amount <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="基础投入金额必须大于0"
        )

    if strategy_data.max_amount and strategy_data.max_amount < strategy_data.base_amount:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="最大投入金额不能小于基础投入金额"
        )

    # 创建策略
    db_strategy = Strategy(
        user_id=current_user.id,
        name=strategy_data.name,
        description=strategy_data.description,
        strategy_type=strategy_type,
        exchange=strategy_data.exchange,
        symbol=strategy_data.symbol,
        market_type=strategy_data.market_type,
        base_amount=strategy_data.base_amount,
        max_amount=strategy_data.max_amount,
        parameters=strategy_data.parameters,
        max_loss=strategy_data.max_loss,
        stop_loss_percentage=strategy_data.stop_loss_percentage,
        take_profit_percentage=strategy_data.take_profit_percentage,
        notes=strategy_data.notes
    )

    db.add(db_strategy)
    db.commit()
    db.refresh(db_strategy)

    return StrategyResponse(
        id=db_strategy.id,
        name=db_strategy.name,
        description=db_strategy.description,
        strategy_type=db_strategy.strategy_type.value,
        exchange=db_strategy.exchange,
        symbol=db_strategy.symbol,
        market_type=db_strategy.market_type,
        base_amount=db_strategy.base_amount,
        max_amount=db_strategy.max_amount,
        parameters=db_strategy.parameters,
        status=db_strategy.status.value,
        is_active=db_strategy.is_active,
        total_orders=db_strategy.total_orders,
        total_trades=db_strategy.total_trades,
        total_profit=db_strategy.total_profit,
        total_loss=db_strategy.total_loss,
        win_rate=db_strategy.win_rate,
        created_at=db_strategy.created_at.isoformat() if db_strategy.created_at else None,
        started_at=db_strategy.started_at.isoformat() if db_strategy.started_at else None,
        last_executed_at=db_strategy.last_executed_at.isoformat() if db_strategy.last_executed_at else None,
        last_error=db_strategy.last_error
    )


@router.get("/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取单个策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()

    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )

    return StrategyResponse(
        id=strategy.id,
        name=strategy.name,
        description=strategy.description,
        strategy_type=strategy.strategy_type.value if strategy.strategy_type else "",
        exchange=strategy.exchange,
        symbol=strategy.symbol,
        market_type=strategy.market_type,
        base_amount=strategy.base_amount,
        max_amount=strategy.max_amount,
        parameters=strategy.parameters,
        status=strategy.status.value if strategy.status else "",
        is_active=strategy.is_active,
        total_orders=strategy.total_orders,
        total_trades=strategy.total_trades,
        total_profit=strategy.total_profit,
        total_loss=strategy.total_loss,
        win_rate=strategy.win_rate,
        created_at=strategy.created_at.isoformat() if strategy.created_at else None,
        started_at=strategy.started_at.isoformat() if strategy.started_at else None,
        last_executed_at=strategy.last_executed_at.isoformat() if strategy.last_executed_at else None,
        last_error=strategy.last_error
    )


@router.put("/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(
    strategy_id: int,
    strategy_update: StrategyUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()

    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )

    # 如果策略正在运行，限制可修改的字段
    if strategy.is_running:
        allowed_fields = ['notes', 'max_loss', 'stop_loss_percentage', 'take_profit_percentage']
        update_data = strategy_update.dict(exclude_unset=True)

        for field in update_data:
            if field not in allowed_fields:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"策略运行中，无法修改字段: {field}"
                )

    # 更新字段
    update_data = strategy_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(strategy, field):
            setattr(strategy, field, value)

    db.commit()
    db.refresh(strategy)

    return StrategyResponse(
        id=strategy.id,
        name=strategy.name,
        description=strategy.description,
        strategy_type=strategy.strategy_type.value if strategy.strategy_type else "",
        exchange=strategy.exchange,
        symbol=strategy.symbol,
        market_type=strategy.market_type,
        base_amount=strategy.base_amount,
        max_amount=strategy.max_amount,
        parameters=strategy.parameters,
        status=strategy.status.value if strategy.status else "",
        is_active=strategy.is_active,
        total_orders=strategy.total_orders,
        total_trades=strategy.total_trades,
        total_profit=strategy.total_profit,
        total_loss=strategy.total_loss,
        win_rate=strategy.win_rate,
        created_at=strategy.created_at.isoformat() if strategy.created_at else None,
        started_at=strategy.started_at.isoformat() if strategy.started_at else None,
        last_executed_at=strategy.last_executed_at.isoformat() if strategy.last_executed_at else None,
        last_error=strategy.last_error
    )


@router.post("/{strategy_id}/start")
async def start_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """启动策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()

    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )

    if strategy.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略已在运行中"
        )

    if not strategy.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略已被禁用"
        )

    # 启动策略
    strategy.status = StrategyStatus.RUNNING
    strategy.started_at = datetime.utcnow()
    strategy.last_error = None

    db.commit()

    return {"message": "策略启动成功"}


@router.post("/{strategy_id}/stop")
async def stop_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """停止策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()

    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )

    if not strategy.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="策略未在运行中"
        )

    # 停止策略
    strategy.status = StrategyStatus.STOPPED
    strategy.stopped_at = datetime.utcnow()

    db.commit()

    return {"message": "策略停止成功"}


@router.delete("/{strategy_id}")
async def delete_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除策略"""
    strategy = db.query(Strategy).filter(
        Strategy.id == strategy_id,
        Strategy.user_id == current_user.id
    ).first()

    if not strategy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="策略不存在"
        )

    if strategy.is_running:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请先停止策略再删除"
        )

    db.delete(strategy)
    db.commit()

    return {"message": "策略删除成功"}
