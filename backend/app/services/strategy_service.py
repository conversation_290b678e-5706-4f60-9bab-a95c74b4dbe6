"""
策略服务
"""
from typing import Dict, List, Optional, Any, Type
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from sqlalchemy.orm import Session

from ..core.strategy_engine import BaseStrategy, MarketData, BacktestResult
from ..strategies.grid_strategy import GridStrategy
from ..strategies.martingale_strategy import MartingaleStrategy
from ..strategies.dca_strategy import DCAStrategy
from ..strategies.ma_strategy import MAStrategy
from ..models.strategy import Strategy
from ..core.database import get_db
import logging

logger = logging.getLogger(__name__)


class StrategyFactory:
    """策略工厂"""

    _strategies: Dict[str, Type[BaseStrategy]] = {
        'grid': GridStrategy,
        'martingale': MartingaleStrategy,
        'dca': DCAStrategy,
        'ma': MAStrategy,
    }

    @classmethod
    def create_strategy(cls, strategy_type: str, symbol: str, initial_capital: float,
                       parameters: Dict[str, Any]) -> BaseStrategy:
        """创建策略实例"""
        if strategy_type not in cls._strategies:
            raise ValueError(f"Unsupported strategy type: {strategy_type}")

        strategy_class = cls._strategies[strategy_type]
        return strategy_class(symbol, initial_capital, parameters)

    @classmethod
    def get_available_strategies(cls) -> Dict[str, Dict[str, Any]]:
        """获取可用策略列表"""
        strategies = {}
        for strategy_type, strategy_class in cls._strategies.items():
            # 创建临时实例获取策略信息
            temp_instance = strategy_class("BTCUSDT", 1000, {})
            strategies[strategy_type] = temp_instance.get_strategy_info()
        return strategies

    @classmethod
    def register_strategy(cls, strategy_type: str, strategy_class: Type[BaseStrategy]):
        """注册新策略"""
        cls._strategies[strategy_type] = strategy_class


class MarketDataService:
    """市场数据服务"""

    @staticmethod
    def generate_mock_data(symbol: str, start_date: datetime, end_date: datetime,
                          interval: str = '1h') -> List[MarketData]:
        """生成模拟市场数据"""
        # 这里应该从真实数据源获取数据，暂时生成模拟数据

        # 计算时间间隔
        if interval == '1m':
            delta = timedelta(minutes=1)
        elif interval == '5m':
            delta = timedelta(minutes=5)
        elif interval == '15m':
            delta = timedelta(minutes=15)
        elif interval == '1h':
            delta = timedelta(hours=1)
        elif interval == '4h':
            delta = timedelta(hours=4)
        elif interval == '1d':
            delta = timedelta(days=1)
        else:
            delta = timedelta(hours=1)

        data = []
        current_time = start_date
        base_price = 50000.0  # BTC基础价格

        # 生成随机游走价格
        np.random.seed(42)  # 固定种子确保可重复性

        while current_time <= end_date:
            # 生成OHLCV数据
            price_change = np.random.normal(0, 0.02)  # 2%标准差
            base_price *= (1 + price_change)

            # 确保价格为正
            base_price = max(base_price, 1000)

            # 生成OHLC
            volatility = np.random.uniform(0.005, 0.02)  # 0.5%-2%波动
            high = base_price * (1 + volatility)
            low = base_price * (1 - volatility)
            open_price = base_price + np.random.uniform(-volatility/2, volatility/2) * base_price
            close_price = base_price

            volume = np.random.uniform(100, 1000)

            market_data = MarketData(
                timestamp=current_time,
                open=open_price,
                high=high,
                low=low,
                close=close_price,
                volume=volume
            )

            data.append(market_data)
            current_time += delta

        return data

    @staticmethod
    def get_historical_data(symbol: str, start_date: datetime, end_date: datetime,
                           interval: str = '1h') -> List[MarketData]:
        """获取历史数据"""
        # TODO: 实现真实的数据获取逻辑
        # 这里可以集成币安、OKX等交易所的API
        return MarketDataService.generate_mock_data(symbol, start_date, end_date, interval)


class BacktestService:
    """回测服务"""

    def __init__(self):
        self.market_data_service = MarketDataService()

    def run_backtest(self, strategy_type: str, symbol: str, initial_capital: float,
                    parameters: Dict[str, Any], start_date: datetime, end_date: datetime,
                    interval: str = '1h', fee_rate: float = 0.001) -> BacktestResult:
        """运行回测"""
        try:
            # 创建策略实例
            strategy = StrategyFactory.create_strategy(
                strategy_type, symbol, initial_capital, parameters
            )

            # 验证参数
            is_valid, error_msg = strategy.validate_parameters()
            if not is_valid:
                raise ValueError(f"策略参数验证失败: {error_msg}")

            # 获取历史数据
            market_data = self.market_data_service.get_historical_data(
                symbol, start_date, end_date, interval
            )

            if not market_data:
                raise ValueError("无法获取市场数据")

            # 执行回测
            result = strategy.backtest(market_data, fee_rate)

            logger.info(f"回测完成: {strategy_type} - {symbol}, 总收益: {result.total_return:.2%}")

            return result

        except Exception as e:
            logger.error(f"回测失败: {e}")
            raise

    def optimize_parameters(self, strategy_type: str, symbol: str, initial_capital: float,
                           parameter_ranges: Dict[str, List], start_date: datetime,
                           end_date: datetime, optimization_target: str = 'sharpe_ratio') -> Dict[str, Any]:
        """参数优化"""
        best_params = None
        best_score = float('-inf')
        results = []

        # 生成参数组合
        param_combinations = self._generate_parameter_combinations(parameter_ranges)

        for params in param_combinations[:50]:  # 限制组合数量
            try:
                result = self.run_backtest(
                    strategy_type, symbol, initial_capital, params,
                    start_date, end_date
                )

                # 计算优化目标分数
                score = self._calculate_optimization_score(result, optimization_target)

                results.append({
                    'parameters': params,
                    'result': result.to_dict(),
                    'score': score
                })

                if score > best_score:
                    best_score = score
                    best_params = params

            except Exception as e:
                logger.warning(f"参数组合 {params} 回测失败: {e}")
                continue

        return {
            'best_parameters': best_params,
            'best_score': best_score,
            'all_results': sorted(results, key=lambda x: x['score'], reverse=True)
        }

    def _generate_parameter_combinations(self, parameter_ranges: Dict[str, List]) -> List[Dict[str, Any]]:
        """生成参数组合"""
        import itertools

        keys = list(parameter_ranges.keys())
        values = list(parameter_ranges.values())

        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)

        return combinations

    def _calculate_optimization_score(self, result: BacktestResult, target: str) -> float:
        """计算优化目标分数"""
        if target == 'total_return':
            return result.total_return
        elif target == 'sharpe_ratio':
            return result.sharpe_ratio
        elif target == 'profit_factor':
            return result.profit_factor
        elif target == 'win_rate':
            return result.win_rate
        elif target == 'risk_adjusted_return':
            # 风险调整收益 = 总收益 / 最大回撤
            return result.total_return / max(result.max_drawdown, 0.01)
        else:
            return result.total_return


class StrategyValidationService:
    """策略验证服务"""

    @staticmethod
    def validate_strategy_config(strategy_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """验证策略配置"""
        try:
            # 创建临时策略实例进行验证
            temp_strategy = StrategyFactory.create_strategy(
                strategy_type, "BTCUSDT", 10000, parameters
            )

            is_valid, error_msg = temp_strategy.validate_parameters()

            if is_valid:
                # 计算风险指标
                risk_metrics = temp_strategy.calculate_risk_metrics(50000)  # 假设当前价格

                return {
                    'valid': True,
                    'message': '策略配置验证通过',
                    'risk_metrics': risk_metrics
                }
            else:
                return {
                    'valid': False,
                    'message': error_msg,
                    'risk_metrics': None
                }

        except Exception as e:
            return {
                'valid': False,
                'message': f'验证过程中发生错误: {str(e)}',
                'risk_metrics': None
            }

    @staticmethod
    def estimate_capital_requirements(strategy_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """估算资金需求"""
        try:
            strategy_info = StrategyFactory.get_available_strategies()[strategy_type]
            min_capital = strategy_info.get('min_capital', 1000)

            if strategy_type == 'grid':
                # 网格策略资金需求
                grid_count = parameters.get('grid_count', 10)
                investment_per_grid = parameters.get('investment_per_grid', 100)
                recommended_capital = grid_count * investment_per_grid * 1.2  # 20%缓冲

            elif strategy_type == 'martingale':
                # 马丁格尔策略资金需求
                base_amount = parameters.get('base_amount', 100)
                multiplier = parameters.get('multiplier', 2.0)
                max_levels = parameters.get('max_levels', 5)
                max_investment = base_amount * sum(multiplier ** i for i in range(max_levels + 1))
                recommended_capital = max_investment * 1.5  # 50%缓冲

            else:
                recommended_capital = min_capital

            return {
                'min_capital': min_capital,
                'recommended_capital': recommended_capital,
                'capital_utilization': min(recommended_capital / max(min_capital, 1), 1.0)
            }

        except Exception as e:
            logger.error(f"估算资金需求失败: {e}")
            return {
                'min_capital': 1000,
                'recommended_capital': 5000,
                'capital_utilization': 0.5
            }
