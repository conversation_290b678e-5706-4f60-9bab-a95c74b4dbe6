"""
WebSocket管理器
"""
import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any, Set
from collections import defaultdict

from ..exchanges.binance_websocket import BinanceWebSocket, BinanceKlineWebSocket, BinanceDepthWebSocket
from ..exchanges.okx_websocket import OKXWebSocket, OKXKlineWebSocket, OKXDepthWebSocket

logger = logging.getLogger(__name__)

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 交易所WebSocket客户端
        self.exchange_clients: Dict[str, Dict[str, Any]] = {
            'binance': {
                'ticker': None,
                'kline': None,
                'depth': None
            },
            'okx': {
                'ticker': None,
                'kline': None,
                'depth': None
            }
        }
        
        # 订阅管理
        self.subscriptions: Dict[str, Set[str]] = defaultdict(set)  # exchange_type -> symbols
        self.callbacks: Dict[str, List[Callable]] = defaultdict(list)  # channel -> callbacks
        
        # 运行状态
        self.is_running = False
        self._tasks: List[asyncio.Task] = []
    
    async def start(self):
        """启动WebSocket管理器"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("WebSocket管理器启动")
        
        # 启动所有交易所的WebSocket连接
        await self._start_exchange_clients()
    
    async def stop(self):
        """停止WebSocket管理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("WebSocket管理器停止")
        
        # 停止所有任务
        for task in self._tasks:
            if not task.done():
                task.cancel()
        
        # 停止所有WebSocket客户端
        await self._stop_exchange_clients()
    
    async def _start_exchange_clients(self):
        """启动交易所WebSocket客户端"""
        # 启动币安WebSocket
        if not self.exchange_clients['binance']['ticker']:
            self.exchange_clients['binance']['ticker'] = BinanceWebSocket()
            await self.exchange_clients['binance']['ticker'].start()
        
        if not self.exchange_clients['binance']['kline']:
            self.exchange_clients['binance']['kline'] = BinanceKlineWebSocket()
            await self.exchange_clients['binance']['kline'].start()
        
        if not self.exchange_clients['binance']['depth']:
            self.exchange_clients['binance']['depth'] = BinanceDepthWebSocket()
            await self.exchange_clients['binance']['depth'].start()
        
        # 启动OKX WebSocket
        if not self.exchange_clients['okx']['ticker']:
            self.exchange_clients['okx']['ticker'] = OKXWebSocket()
            await self.exchange_clients['okx']['ticker'].start()
        
        if not self.exchange_clients['okx']['kline']:
            self.exchange_clients['okx']['kline'] = OKXKlineWebSocket()
            await self.exchange_clients['okx']['kline'].start()
        
        if not self.exchange_clients['okx']['depth']:
            self.exchange_clients['okx']['depth'] = OKXDepthWebSocket()
            await self.exchange_clients['okx']['depth'].start()
    
    async def _stop_exchange_clients(self):
        """停止交易所WebSocket客户端"""
        for exchange in self.exchange_clients:
            for data_type in self.exchange_clients[exchange]:
                client = self.exchange_clients[exchange][data_type]
                if client:
                    await client.stop()
                    self.exchange_clients[exchange][data_type] = None
    
    async def subscribe_ticker(self, exchange: str, symbols: List[str], callback: Callable[[Dict[str, Any]], None]):
        """订阅行情数据"""
        if exchange not in self.exchange_clients:
            raise ValueError(f"不支持的交易所: {exchange}")
        
        client = self.exchange_clients[exchange]['ticker']
        if not client:
            logger.error(f"{exchange} ticker WebSocket客户端未启动")
            return
        
        # 添加回调
        channel = f"{exchange}:ticker"
        self.callbacks[channel].append(callback)
        
        # 订阅符号
        await client.subscribe(symbols, self._create_callback_wrapper(channel))
        
        # 记录订阅
        key = f"{exchange}:ticker"
        self.subscriptions[key].update(symbols)
        
        logger.info(f"订阅 {exchange} ticker: {symbols}")
    
    async def subscribe_kline(self, exchange: str, symbols: List[str], interval: str, callback: Callable[[Dict[str, Any]], None]):
        """订阅K线数据"""
        if exchange not in self.exchange_clients:
            raise ValueError(f"不支持的交易所: {exchange}")
        
        # 重新创建K线客户端（因为间隔可能不同）
        if exchange == 'binance':
            client = BinanceKlineWebSocket(interval=interval)
        elif exchange == 'okx':
            client = OKXKlineWebSocket(interval=interval)
        else:
            raise ValueError(f"不支持的交易所: {exchange}")
        
        await client.start()
        
        # 添加回调
        channel = f"{exchange}:kline:{interval}"
        self.callbacks[channel].append(callback)
        
        # 订阅符号
        await client.subscribe(symbols, self._create_callback_wrapper(channel))
        
        # 记录订阅
        key = f"{exchange}:kline:{interval}"
        self.subscriptions[key].update(symbols)
        
        logger.info(f"订阅 {exchange} {interval} K线: {symbols}")
    
    async def subscribe_depth(self, exchange: str, symbols: List[str], callback: Callable[[Dict[str, Any]], None]):
        """订阅深度数据"""
        if exchange not in self.exchange_clients:
            raise ValueError(f"不支持的交易所: {exchange}")
        
        client = self.exchange_clients[exchange]['depth']
        if not client:
            logger.error(f"{exchange} depth WebSocket客户端未启动")
            return
        
        # 添加回调
        channel = f"{exchange}:depth"
        self.callbacks[channel].append(callback)
        
        # 订阅符号
        await client.subscribe(symbols, self._create_callback_wrapper(channel))
        
        # 记录订阅
        key = f"{exchange}:depth"
        self.subscriptions[key].update(symbols)
        
        logger.info(f"订阅 {exchange} depth: {symbols}")
    
    async def unsubscribe(self, exchange: str, data_type: str, symbols: List[str], interval: Optional[str] = None):
        """取消订阅"""
        if exchange not in self.exchange_clients:
            raise ValueError(f"不支持的交易所: {exchange}")
        
        if data_type == 'kline' and interval:
            key = f"{exchange}:{data_type}:{interval}"
        else:
            key = f"{exchange}:{data_type}"
        
        # 从订阅记录中移除
        if key in self.subscriptions:
            self.subscriptions[key] -= set(symbols)
        
        # 从WebSocket客户端取消订阅
        client = self.exchange_clients[exchange].get(data_type)
        if client:
            await client.unsubscribe(symbols)
        
        logger.info(f"取消订阅 {exchange} {data_type}: {symbols}")
    
    def _create_callback_wrapper(self, channel: str) -> Callable[[Dict[str, Any]], None]:
        """创建回调包装器"""
        async def wrapper(data: Dict[str, Any]):
            # 通知所有订阅者
            for callback in self.callbacks[channel]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {e}")
        
        return wrapper
    
    def get_subscriptions(self) -> Dict[str, Set[str]]:
        """获取当前订阅状态"""
        return dict(self.subscriptions)
    
    def get_connection_status(self) -> Dict[str, Dict[str, bool]]:
        """获取连接状态"""
        status = {}
        for exchange in self.exchange_clients:
            status[exchange] = {}
            for data_type in self.exchange_clients[exchange]:
                client = self.exchange_clients[exchange][data_type]
                status[exchange][data_type] = client.is_alive() if client else False
        return status
    
    async def reconnect_all(self):
        """重连所有WebSocket"""
        logger.info("重连所有WebSocket连接")
        await self._stop_exchange_clients()
        await self._start_exchange_clients()
        
        # 重新订阅之前的数据
        for key, symbols in self.subscriptions.items():
            if not symbols:
                continue
            
            parts = key.split(':')
            exchange = parts[0]
            data_type = parts[1]
            
            try:
                if data_type == 'ticker':
                    client = self.exchange_clients[exchange]['ticker']
                    if client:
                        await client.subscribe(list(symbols), self._create_callback_wrapper(key))
                
                elif data_type == 'kline' and len(parts) > 2:
                    interval = parts[2]
                    # 重新创建K线客户端
                    if exchange == 'binance':
                        client = BinanceKlineWebSocket(interval=interval)
                    elif exchange == 'okx':
                        client = OKXKlineWebSocket(interval=interval)
                    else:
                        continue
                    
                    await client.start()
                    await client.subscribe(list(symbols), self._create_callback_wrapper(key))
                
                elif data_type == 'depth':
                    client = self.exchange_clients[exchange]['depth']
                    if client:
                        await client.subscribe(list(symbols), self._create_callback_wrapper(key))
                
            except Exception as e:
                logger.error(f"重新订阅失败 {key}: {e}")

# 全局WebSocket管理器实例
ws_manager = WebSocketManager()
