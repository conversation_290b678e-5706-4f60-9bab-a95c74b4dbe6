"""
交易所服务层
"""
import asyncio
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
import logging

from ..models.api_key import APIKey
from ..models.order import Order, OrderType as DBOrderType, OrderSide as DBOrderSide, OrderStatus as DBOrderStatus
from ..models.trade import Trade
from ..models.position import Position
from ..exchanges.factory import ExchangeFactory
from ..exchanges.base import BaseExchange, OrderType, OrderSide, OrderStatus, MarketType
from ..core.security import decrypt_api_key

logger = logging.getLogger(__name__)

class ExchangeService:
    """交易所服务类"""
    
    def __init__(self):
        self._exchange_cache: Dict[int, BaseExchange] = {}
    
    async def get_exchange(self, db: Session, api_key_id: int, user_id: int) -> BaseExchange:
        """获取交易所实例"""
        # 检查缓存
        if api_key_id in self._exchange_cache:
            return self._exchange_cache[api_key_id]
        
        # 从数据库获取API密钥
        api_key = db.query(APIKey).filter(
            APIKey.id == api_key_id,
            APIKey.user_id == user_id,
            APIKey.is_active == True
        ).first()
        
        if not api_key:
            raise ValueError("API密钥不存在或未激活")
        
        # 解密API密钥
        try:
            decrypted_key = decrypt_api_key(api_key.encrypted_key)
            decrypted_secret = decrypt_api_key(api_key.encrypted_secret)
            decrypted_passphrase = decrypt_api_key(api_key.encrypted_passphrase) if api_key.encrypted_passphrase else None
        except Exception as e:
            logger.error(f"解密API密钥失败: {e}")
            raise ValueError("API密钥解密失败")
        
        # 创建交易所实例
        exchange = ExchangeFactory.create_exchange(
            exchange_name=api_key.exchange,
            api_key=decrypted_key,
            secret_key=decrypted_secret,
            passphrase=decrypted_passphrase,
            testnet=api_key.is_testnet
        )
        
        # 缓存实例
        self._exchange_cache[api_key_id] = exchange
        
        return exchange
    
    async def test_api_key(self, db: Session, api_key_id: int, user_id: int) -> bool:
        """测试API密钥连接"""
        try:
            exchange = await self.get_exchange(db, api_key_id, user_id)
            return await exchange.test_connection()
        except Exception as e:
            logger.error(f"测试API密钥失败: {e}")
            return False
    
    async def get_account_info(self, db: Session, api_key_id: int, user_id: int) -> Dict[str, Any]:
        """获取账户信息"""
        exchange = await self.get_exchange(db, api_key_id, user_id)
        return await exchange.get_account_info()
    
    async def get_balances(self, db: Session, api_key_id: int, user_id: int) -> List[Dict[str, Any]]:
        """获取余额信息"""
        exchange = await self.get_exchange(db, api_key_id, user_id)
        balances = await exchange.get_balances()
        
        return [
            {
                'currency': balance.currency,
                'free': balance.free,
                'locked': balance.locked,
                'total': balance.total
            }
            for balance in balances
        ]
    
    async def create_order(self, db: Session, api_key_id: int, user_id: int,
                          symbol: str, side: str, order_type: str, amount: float,
                          price: Optional[float] = None, stop_price: Optional[float] = None,
                          market_type: str = "spot", strategy_id: Optional[int] = None,
                          notes: Optional[str] = None) -> Dict[str, Any]:
        """创建订单"""
        exchange = await self.get_exchange(db, api_key_id, user_id)
        
        # 转换参数
        exchange_side = OrderSide.BUY if side.lower() == 'buy' else OrderSide.SELL
        exchange_order_type = OrderType.LIMIT if order_type.lower() == 'limit' else OrderType.MARKET
        exchange_market_type = MarketType.FUTURES if market_type.lower() == 'futures' else MarketType.SPOT
        
        # 调用交易所API
        result = await exchange.create_order(
            symbol=symbol,
            side=exchange_side,
            order_type=exchange_order_type,
            amount=amount,
            price=price,
            stop_price=stop_price,
            market_type=exchange_market_type
        )
        
        # 保存到数据库
        db_order = Order(
            user_id=user_id,
            api_key_id=api_key_id,
            strategy_id=strategy_id,
            exchange=exchange.name,
            exchange_order_id=result.order_id,
            symbol=symbol,
            market_type=DBOrderType(market_type),
            order_type=DBOrderType(order_type),
            side=DBOrderSide(side),
            amount=amount,
            price=price,
            stop_price=stop_price,
            filled_amount=result.filled_amount,
            avg_price=result.avg_price,
            fee=result.fee,
            status=self._convert_order_status(result.status),
            notes=notes
        )
        
        db.add(db_order)
        db.commit()
        db.refresh(db_order)
        
        return {
            'id': db_order.id,
            'exchange_order_id': result.order_id,
            'symbol': symbol,
            'side': side,
            'order_type': order_type,
            'amount': amount,
            'price': price,
            'status': result.status.value,
            'filled_amount': result.filled_amount,
            'timestamp': result.timestamp.isoformat()
        }
    
    async def cancel_order(self, db: Session, api_key_id: int, user_id: int,
                          order_id: int) -> bool:
        """取消订单"""
        # 获取订单信息
        order = db.query(Order).filter(
            Order.id == order_id,
            Order.user_id == user_id,
            Order.api_key_id == api_key_id
        ).first()
        
        if not order:
            raise ValueError("订单不存在")
        
        if not order.is_active:
            raise ValueError("订单不是活跃状态")
        
        exchange = await self.get_exchange(db, api_key_id, user_id)
        
        # 调用交易所API取消订单
        success = await exchange.cancel_order(
            symbol=order.symbol,
            order_id=order.exchange_order_id,
            market_type=MarketType.FUTURES if order.market_type.value == 'futures' else MarketType.SPOT
        )
        
        if success:
            # 更新数据库状态
            order.status = DBOrderStatus.CANCELLED
            db.commit()
        
        return success
    
    async def sync_orders(self, db: Session, api_key_id: int, user_id: int) -> int:
        """同步订单状态"""
        exchange = await self.get_exchange(db, api_key_id, user_id)
        
        # 获取活跃订单
        active_orders = db.query(Order).filter(
            Order.api_key_id == api_key_id,
            Order.user_id == user_id,
            Order.status.in_([DBOrderStatus.PENDING, DBOrderStatus.OPEN, DBOrderStatus.PARTIALLY_FILLED])
        ).all()
        
        updated_count = 0
        
        for order in active_orders:
            try:
                # 从交易所获取最新状态
                exchange_order = await exchange.get_order(
                    symbol=order.symbol,
                    order_id=order.exchange_order_id,
                    market_type=MarketType.FUTURES if order.market_type.value == 'futures' else MarketType.SPOT
                )
                
                # 更新订单状态
                old_status = order.status
                order.status = self._convert_order_status(exchange_order.status)
                order.filled_amount = exchange_order.filled_amount
                order.avg_price = exchange_order.avg_price
                order.fee = exchange_order.fee
                
                if old_status != order.status:
                    updated_count += 1
                
                # 如果订单完全成交，创建交易记录
                if order.status == DBOrderStatus.FILLED and exchange_order.filled_amount > 0:
                    await self._create_trade_record(db, order, exchange_order)
                
            except Exception as e:
                logger.error(f"同步订单 {order.id} 失败: {e}")
        
        db.commit()
        return updated_count
    
    async def get_positions(self, db: Session, api_key_id: int, user_id: int) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        exchange = await self.get_exchange(db, api_key_id, user_id)
        positions = await exchange.get_positions()
        
        return [
            {
                'symbol': pos.symbol,
                'side': pos.side,
                'size': pos.size,
                'entry_price': pos.entry_price,
                'mark_price': pos.mark_price,
                'unrealized_pnl': pos.unrealized_pnl,
                'margin': pos.margin,
                'leverage': pos.leverage,
                'liquidation_price': pos.liquidation_price
            }
            for pos in positions
        ]
    
    async def get_market_data(self, exchange_name: str, symbol: str) -> Dict[str, Any]:
        """获取市场数据（无需API密钥）"""
        # 创建临时交易所实例（仅用于获取公开数据）
        exchange = ExchangeFactory.create_exchange(
            exchange_name=exchange_name,
            api_key="dummy",
            secret_key="dummy",
            passphrase="dummy" if exchange_name.lower() == 'okx' else None
        )
        
        ticker = await exchange.get_ticker(symbol)
        
        return {
            'symbol': ticker.symbol,
            'price': ticker.price,
            'change_24h': ticker.change_24h,
            'change_24h_percent': ticker.change_24h_percent,
            'volume_24h': ticker.volume_24h,
            'high_24h': ticker.high_24h,
            'low_24h': ticker.low_24h,
            'timestamp': ticker.timestamp.isoformat()
        }
    
    def _convert_order_status(self, exchange_status: OrderStatus) -> DBOrderStatus:
        """转换订单状态"""
        status_map = {
            OrderStatus.PENDING: DBOrderStatus.PENDING,
            OrderStatus.OPEN: DBOrderStatus.OPEN,
            OrderStatus.FILLED: DBOrderStatus.FILLED,
            OrderStatus.PARTIALLY_FILLED: DBOrderStatus.PARTIALLY_FILLED,
            OrderStatus.CANCELLED: DBOrderStatus.CANCELLED,
            OrderStatus.REJECTED: DBOrderStatus.REJECTED,
            OrderStatus.EXPIRED: DBOrderStatus.EXPIRED
        }
        return status_map.get(exchange_status, DBOrderStatus.PENDING)
    
    async def _create_trade_record(self, db: Session, order: Order, exchange_order) -> Trade:
        """创建交易记录"""
        trade = Trade(
            user_id=order.user_id,
            order_id=order.id,
            exchange=order.exchange,
            exchange_trade_id=f"{order.exchange_order_id}_fill",
            symbol=order.symbol,
            side=order.side.value,
            amount=exchange_order.filled_amount,
            price=exchange_order.avg_price or order.price,
            fee=exchange_order.fee or 0,
            fee_currency="USDT",  # 默认手续费币种
            executed_at=exchange_order.timestamp
        )
        
        db.add(trade)
        return trade
    
    def clear_cache(self):
        """清除缓存"""
        self._exchange_cache.clear()

# 全局交易所服务实例
exchange_service = ExchangeService()
