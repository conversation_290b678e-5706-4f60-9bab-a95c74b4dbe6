"""
策略引擎核心模块
"""
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)


@dataclass
class MarketData:
    """市场数据"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float


@dataclass
class Trade:
    """交易记录"""
    timestamp: datetime
    side: str  # 'buy' or 'sell'
    price: float
    amount: float
    fee: float = 0.0
    
    @property
    def value(self) -> float:
        """交易价值"""
        return self.price * self.amount


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    amount: float  # 持仓数量
    avg_price: float  # 平均成本
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


@dataclass
class BacktestResult:
    """回测结果"""
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    profit_factor: float
    trades: List[Trade]
    equity_curve: List[Tuple[datetime, float]]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        import math
        
        def safe_float(value):
            """安全转换浮点数，处理无穷大和NaN值"""
            if math.isnan(value) or math.isinf(value):
                return 0.0
            return float(value)
        
        return {
            'total_return': safe_float(self.total_return),
            'annual_return': safe_float(self.annual_return),
            'max_drawdown': safe_float(self.max_drawdown),
            'sharpe_ratio': safe_float(self.sharpe_ratio),
            'win_rate': safe_float(self.win_rate),
            'total_trades': self.total_trades,
            'profit_factor': safe_float(self.profit_factor),
            'trades': [
                {
                    'timestamp': trade.timestamp.isoformat(),
                    'side': trade.side,
                    'price': trade.price,
                    'amount': trade.amount,
                    'fee': trade.fee,
                    'value': trade.value
                }
                for trade in self.trades
            ],
            'equity_curve': [
                {
                    'timestamp': timestamp.isoformat(),
                    'equity': equity
                }
                for timestamp, equity in self.equity_curve
            ]
        }


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, symbol: str, initial_capital: float, parameters: Dict[str, Any]):
        self.symbol = symbol
        self.initial_capital = initial_capital
        self.parameters = parameters
        self.position = Position(symbol=symbol, amount=0.0, avg_price=0.0)
        self.cash = initial_capital
        self.trades: List[Trade] = []
        self.equity_history: List[Tuple[datetime, float]] = []
        
    @abstractmethod
    def generate_signals(self, data: List[MarketData]) -> List[Tuple[datetime, str, float, float]]:
        """
        生成交易信号
        返回: [(时间戳, 方向, 价格, 数量)]
        """
        pass
    
    def execute_trade(self, timestamp: datetime, side: str, price: float, amount: float, fee_rate: float = 0.001):
        """执行交易"""
        fee = price * amount * fee_rate
        
        if side == 'buy':
            cost = price * amount + fee
            if cost <= self.cash:
                self.cash -= cost
                old_amount = self.position.amount
                old_value = old_amount * self.position.avg_price
                new_amount = old_amount + amount
                new_value = old_value + price * amount
                self.position.amount = new_amount
                self.position.avg_price = new_value / new_amount if new_amount > 0 else 0
                
                trade = Trade(timestamp=timestamp, side=side, price=price, amount=amount, fee=fee)
                self.trades.append(trade)
                return True
        
        elif side == 'sell':
            if amount <= self.position.amount:
                revenue = price * amount - fee
                self.cash += revenue
                
                # 计算已实现盈亏
                cost_basis = self.position.avg_price * amount
                realized_pnl = revenue - cost_basis
                self.position.realized_pnl += realized_pnl
                
                self.position.amount -= amount
                if self.position.amount <= 0:
                    self.position.amount = 0
                    self.position.avg_price = 0
                
                trade = Trade(timestamp=timestamp, side=side, price=price, amount=amount, fee=fee)
                self.trades.append(trade)
                return True
        
        return False
    
    def update_position_value(self, current_price: float):
        """更新持仓价值"""
        if self.position.amount > 0:
            current_value = self.position.amount * current_price
            cost_value = self.position.amount * self.position.avg_price
            self.position.unrealized_pnl = current_value - cost_value
    
    def get_total_equity(self, current_price: float) -> float:
        """获取总权益"""
        self.update_position_value(current_price)
        return self.cash + (self.position.amount * current_price)
    
    def backtest(self, data: List[MarketData], fee_rate: float = 0.001) -> BacktestResult:
        """执行回测"""
        signals = self.generate_signals(data)
        signal_dict = {signal[0]: signal for signal in signals}
        
        for market_data in data:
            # 更新权益曲线
            equity = self.get_total_equity(market_data.close)
            self.equity_history.append((market_data.timestamp, equity))
            
            # 执行交易信号
            if market_data.timestamp in signal_dict:
                _, side, price, amount = signal_dict[market_data.timestamp]
                self.execute_trade(market_data.timestamp, side, price, amount, fee_rate)
        
        return self._calculate_metrics()
    
    def _calculate_metrics(self) -> BacktestResult:
        """计算回测指标"""
        import math
        
        def safe_divide(numerator, denominator, default=0.0):
            """安全除法，避免除零错误"""
            if denominator == 0 or math.isnan(denominator) or math.isinf(denominator):
                return default
            result = numerator / denominator
            if math.isnan(result) or math.isinf(result):
                return default
            return result
        
        if not self.equity_history:
            return BacktestResult(0, 0, 0, 0, 0, 0, 0, [], [])
        
        equity_values = [equity for _, equity in self.equity_history]
        
        # 避免除零错误
        safe_equity_values = [max(v, 0.001) for v in equity_values[:-1]]  # 避免零值
        returns = np.diff(equity_values) / np.array(safe_equity_values)
        
        # 总收益率
        total_return = safe_divide(equity_values[-1] - self.initial_capital, self.initial_capital)
        
        # 年化收益率
        days = (self.equity_history[-1][0] - self.equity_history[0][0]).days
        if days > 0 and total_return > -1:
            try:
                annual_return = (1 + total_return) ** (365 / max(days, 1)) - 1
                if math.isnan(annual_return) or math.isinf(annual_return):
                    annual_return = 0
            except (OverflowError, ValueError):
                annual_return = 0
        else:
            annual_return = 0
        
        # 最大回撤
        peak = np.maximum.accumulate(equity_values)
        # 避免除零错误
        safe_peak = np.maximum(peak, 0.001)
        drawdown = (equity_values - peak) / safe_peak
        max_drawdown = abs(np.min(drawdown)) if len(drawdown) > 0 else 0
        
        # 夏普比率
        if len(returns) > 1:
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            if std_return > 0 and not (math.isnan(mean_return) or math.isnan(std_return)):
                sharpe_ratio = mean_return / std_return * np.sqrt(252)
                if math.isnan(sharpe_ratio) or math.isinf(sharpe_ratio):
                    sharpe_ratio = 0
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0
        
        # 胜率
        profitable_trades = [t for t in self.trades if self._is_profitable_trade(t)]
        win_rate = safe_divide(len(profitable_trades), len(self.trades))
        
        # 盈亏比
        profits = [self._get_trade_pnl(t) for t in self.trades if self._get_trade_pnl(t) > 0]
        losses = [abs(self._get_trade_pnl(t)) for t in self.trades if self._get_trade_pnl(t) < 0]
        profit_factor = safe_divide(sum(profits), sum(losses), 0.0)
        
        return BacktestResult(
            total_return=total_return,
            annual_return=annual_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=len(self.trades),
            profit_factor=profit_factor,
            trades=self.trades,
            equity_curve=self.equity_history
        )
    
    def _is_profitable_trade(self, trade: Trade) -> bool:
        """判断交易是否盈利（简化实现）"""
        return self._get_trade_pnl(trade) > 0
    
    def _get_trade_pnl(self, trade: Trade) -> float:
        """获取交易盈亏（简化实现）"""
        # 这里需要更复杂的逻辑来计算实际盈亏
        # 暂时返回0，在具体策略中实现
        return 0
