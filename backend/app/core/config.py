"""
应用配置模块
"""
from functools import lru_cache
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import validator
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本信息
    app_name: str = "Personal Trading Platform"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置
    database_url: str = "sqlite:///./trading.db"
    
    # 安全配置
    secret_key: str = "your-secret-key-here-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # 主密码 (用于加密API密钥)
    master_password: str = "your-master-password-here-change-in-production"
    
    # Telegram配置
    telegram_bot_token: Optional[str] = None
    
    # CORS配置
    allowed_origins: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 服务端口
    backend_port: int = 8000
    frontend_port: int = 3000
    
    # 风控默认配置
    default_max_order_amount: float = 1000.0
    default_max_daily_loss: float = 500.0
    default_max_orders_per_strategy: int = 10
    default_order_rate_limit: int = 60  # 每分钟最大订单数
    
    # 交易所配置
    binance_base_url: str = "https://api.binance.com"
    binance_ws_url: str = "wss://stream.binance.com:9443"
    okx_base_url: str = "https://www.okx.com"
    okx_ws_url: str = "wss://ws.okx.com:8443"
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/trading.log"
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    
    # 数据库连接池配置
    db_pool_size: int = 10
    db_max_overflow: int = 20
    
    @validator("allowed_origins", pre=True)
    def parse_cors_origins(cls, v):
        """解析CORS origins"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("secret_key")
    def validate_secret_key(cls, v):
        """验证密钥长度"""
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @validator("master_password")
    def validate_master_password(cls, v):
        """验证主密码强度"""
        if len(v) < 8:
            raise ValueError("MASTER_PASSWORD must be at least 8 characters long")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例 (缓存)"""
    return Settings()


# 全局配置实例
settings = get_settings()


# 创建必要的目录
def create_directories():
    """创建必要的目录"""
    directories = [
        "data",
        "logs",
        os.path.dirname(settings.log_file)
    ]
    
    for directory in directories:
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)


# 初始化时创建目录
create_directories()
