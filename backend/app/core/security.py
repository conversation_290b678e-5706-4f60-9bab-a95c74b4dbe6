"""
安全相关工具模块
"""
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

from .config import settings


# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        return payload
    except JWTError:
        return None


class APIKeyEncryption:
    """API密钥加密工具类"""
    
    def __init__(self, master_password: str):
        """初始化加密器"""
        self.master_password = master_password
        self._fernet = None
    
    def _get_fernet(self) -> Fernet:
        """获取Fernet加密器实例"""
        if self._fernet is None:
            # 使用主密码派生加密密钥
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'trading_platform_salt',  # 固定盐值
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.master_password.encode()))
            self._fernet = Fernet(key)
        return self._fernet
    
    def encrypt(self, plaintext: str) -> str:
        """加密字符串"""
        fernet = self._get_fernet()
        encrypted_data = fernet.encrypt(plaintext.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    def decrypt(self, encrypted_text: str) -> str:
        """解密字符串"""
        try:
            fernet = self._get_fernet()
            encrypted_data = base64.urlsafe_b64decode(encrypted_text.encode())
            decrypted_data = fernet.decrypt(encrypted_data)
            return decrypted_data.decode()
        except Exception as e:
            raise ValueError(f"解密失败: {str(e)}")
    
    def test_encryption(self) -> bool:
        """测试加密功能"""
        try:
            test_data = "test_api_key_12345"
            encrypted = self.encrypt(test_data)
            decrypted = self.decrypt(encrypted)
            return test_data == decrypted
        except Exception:
            return False


# 全局加密器实例
_encryption_instance = None


def get_encryption() -> APIKeyEncryption:
    """获取加密器实例"""
    global _encryption_instance
    if _encryption_instance is None:
        _encryption_instance = APIKeyEncryption(settings.master_password)
    return _encryption_instance


def encrypt_api_key(api_key: str) -> str:
    """加密API密钥"""
    encryption = get_encryption()
    return encryption.encrypt(api_key)


def decrypt_api_key(encrypted_key: str) -> str:
    """解密API密钥"""
    encryption = get_encryption()
    return encryption.decrypt(encrypted_key)


def generate_random_string(length: int = 32) -> str:
    """生成随机字符串"""
    return base64.urlsafe_b64encode(os.urandom(length)).decode()[:length]


def validate_master_password() -> bool:
    """验证主密码是否正确"""
    try:
        encryption = get_encryption()
        return encryption.test_encryption()
    except Exception:
        return False
