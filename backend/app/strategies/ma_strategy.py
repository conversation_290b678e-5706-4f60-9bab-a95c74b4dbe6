"""
MA (Moving Average) 均线策略实现
"""
from typing import Dict, List, Tuple, Any
from datetime import datetime
import numpy as np
from ..core.strategy_engine import BaseStrategy, MarketData


class MAStrategy(BaseStrategy):
    """
    均线策略
    基于移动平均线的交叉信号进行交易
    """
    
    def __init__(self, symbol: str, initial_capital: float, parameters: Dict[str, Any]):
        super().__init__(symbol, initial_capital, parameters)
        
        # 均线参数
        self.fast_period = parameters.get('fast_period', 5)  # 快线周期
        self.slow_period = parameters.get('slow_period', 20)  # 慢线周期
        self.ma_type = parameters.get('ma_type', 'sma')  # 均线类型
        self.position_size_pct = parameters.get('position_size_pct', 0.8)  # 仓位比例
        self.stop_loss_pct = parameters.get('stop_loss_pct', 0.05)  # 止损百分比
        self.take_profit_pct = parameters.get('take_profit_pct', 0.1)  # 止盈百分比
        self.enable_trend_filter = parameters.get('enable_trend_filter', True)  # 趋势过滤
        self.trend_period = parameters.get('trend_period', 50)  # 趋势判断周期
        
        # 状态变量
        self.price_history = []
        self.fast_ma_history = []
        self.slow_ma_history = []
        self.trend_ma_history = []
        self.last_signal = None
        self.entry_price = 0.0
        self.in_position = False
    
    def generate_signals(self, data: List[MarketData]) -> List[Tuple[datetime, str, float, float]]:
        """生成均线交易信号"""
        signals = []
        
        for market_data in data:
            current_price = market_data.close
            self.price_history.append(current_price)
            
            # 保持价格历史长度
            max_history = max(self.slow_period, self.trend_period) + 10
            if len(self.price_history) > max_history:
                self.price_history = self.price_history[-max_history:]
            
            # 计算均线
            self._update_moving_averages()
            
            # 需要足够的数据才能生成信号
            if len(self.price_history) < self.slow_period:
                continue
            
            # 检查止损止盈
            if self.in_position:
                stop_loss_signal = self._check_stop_loss(market_data)
                if stop_loss_signal:
                    signals.append(stop_loss_signal)
                    continue
                
                take_profit_signal = self._check_take_profit(market_data)
                if take_profit_signal:
                    signals.append(take_profit_signal)
                    continue
            
            # 生成交易信号
            signal = self._generate_ma_signal(market_data)
            if signal:
                signals.append(signal)
        
        return signals
    
    def _update_moving_averages(self):
        """更新移动平均线"""
        if len(self.price_history) < self.fast_period:
            return
        
        # 计算快线
        fast_ma = self._calculate_ma(self.price_history, self.fast_period)
        self.fast_ma_history.append(fast_ma)
        
        # 计算慢线
        if len(self.price_history) >= self.slow_period:
            slow_ma = self._calculate_ma(self.price_history, self.slow_period)
            self.slow_ma_history.append(slow_ma)
        
        # 计算趋势线
        if len(self.price_history) >= self.trend_period:
            trend_ma = self._calculate_ma(self.price_history, self.trend_period)
            self.trend_ma_history.append(trend_ma)
        
        # 保持历史长度
        max_len = 100
        if len(self.fast_ma_history) > max_len:
            self.fast_ma_history = self.fast_ma_history[-max_len:]
        if len(self.slow_ma_history) > max_len:
            self.slow_ma_history = self.slow_ma_history[-max_len:]
        if len(self.trend_ma_history) > max_len:
            self.trend_ma_history = self.trend_ma_history[-max_len:]
    
    def _calculate_ma(self, prices: List[float], period: int) -> float:
        """计算移动平均线"""
        if len(prices) < period:
            return prices[-1] if prices else 0
        
        recent_prices = prices[-period:]
        
        if self.ma_type == 'sma':
            # 简单移动平均
            return sum(recent_prices) / len(recent_prices)
        elif self.ma_type == 'ema':
            # 指数移动平均
            multiplier = 2 / (period + 1)
            ema = recent_prices[0]
            for price in recent_prices[1:]:
                ema = (price * multiplier) + (ema * (1 - multiplier))
            return ema
        else:
            return sum(recent_prices) / len(recent_prices)
    
    def _generate_ma_signal(self, market_data: MarketData) -> Tuple[datetime, str, float, float] | None:
        """生成均线交叉信号"""
        if len(self.fast_ma_history) < 2 or len(self.slow_ma_history) < 2:
            return None
        
        current_price = market_data.close
        fast_ma_current = self.fast_ma_history[-1]
        fast_ma_previous = self.fast_ma_history[-2]
        slow_ma_current = self.slow_ma_history[-1]
        slow_ma_previous = self.slow_ma_history[-2]
        
        # 检查趋势过滤
        if self.enable_trend_filter and len(self.trend_ma_history) > 0:
            trend_ma = self.trend_ma_history[-1]
            # 只在价格高于趋势线时做多，低于趋势线时做空
            bullish_trend = current_price > trend_ma
        else:
            bullish_trend = True
        
        # 金叉信号：快线上穿慢线
        if (fast_ma_previous <= slow_ma_previous and 
            fast_ma_current > slow_ma_current and 
            not self.in_position and 
            bullish_trend):
            
            amount = self._calculate_position_size(current_price)
            if amount > 0 and self._can_buy(current_price, amount):
                self.in_position = True
                self.entry_price = current_price
                self.last_signal = 'buy'
                return (market_data.timestamp, 'buy', current_price, amount)
        
        # 死叉信号：快线下穿慢线
        elif (fast_ma_previous >= slow_ma_previous and 
              fast_ma_current < slow_ma_current and 
              self.in_position):
            
            amount = self.position.amount
            if amount > 0:
                self.in_position = False
                self.entry_price = 0.0
                self.last_signal = 'sell'
                return (market_data.timestamp, 'sell', current_price, amount)
        
        return None
    
    def _check_stop_loss(self, market_data: MarketData) -> Tuple[datetime, str, float, float] | None:
        """检查止损"""
        if not self.in_position or self.entry_price == 0:
            return None
        
        current_price = market_data.close
        loss_pct = (self.entry_price - current_price) / self.entry_price
        
        if loss_pct >= self.stop_loss_pct:
            amount = self.position.amount
            if amount > 0:
                self.in_position = False
                self.entry_price = 0.0
                return (market_data.timestamp, 'sell', current_price, amount)
        
        return None
    
    def _check_take_profit(self, market_data: MarketData) -> Tuple[datetime, str, float, float] | None:
        """检查止盈"""
        if not self.in_position or self.entry_price == 0:
            return None
        
        current_price = market_data.close
        profit_pct = (current_price - self.entry_price) / self.entry_price
        
        if profit_pct >= self.take_profit_pct:
            amount = self.position.amount
            if amount > 0:
                self.in_position = False
                self.entry_price = 0.0
                return (market_data.timestamp, 'sell', current_price, amount)
        
        return None
    
    def _calculate_position_size(self, current_price: float) -> float:
        """计算仓位大小"""
        available_cash = self.cash * self.position_size_pct
        return available_cash / current_price
    
    def _can_buy(self, price: float, amount: float) -> bool:
        """检查是否可以买入"""
        cost = price * amount * 1.001  # 包含手续费
        return self.cash >= cost
    
    def validate_parameters(self) -> Tuple[bool, str]:
        """验证策略参数"""
        if self.fast_period <= 0 or self.fast_period > 100:
            return False, "快线周期必须在1-100之间"
        
        if self.slow_period <= 0 or self.slow_period > 200:
            return False, "慢线周期必须在1-200之间"
        
        if self.fast_period >= self.slow_period:
            return False, "快线周期必须小于慢线周期"
        
        if self.position_size_pct <= 0 or self.position_size_pct > 1:
            return False, "仓位比例必须在0-100%之间"
        
        if self.stop_loss_pct <= 0 or self.stop_loss_pct > 0.5:
            return False, "止损百分比必须在0-50%之间"
        
        if self.take_profit_pct <= 0 or self.take_profit_pct > 2:
            return False, "止盈百分比必须在0-200%之间"
        
        if self.trend_period <= 0 or self.trend_period > 500:
            return False, "趋势周期必须在1-500之间"
        
        return True, "参数验证通过"
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': '均线策略',
            'description': '基于移动平均线交叉信号的趋势跟踪策略',
            'parameters': {
                'fast_period': {
                    'name': '快线周期',
                    'type': 'int',
                    'min': 1,
                    'max': 100,
                    'default': 5,
                    'description': '快速移动平均线的周期'
                },
                'slow_period': {
                    'name': '慢线周期',
                    'type': 'int',
                    'min': 2,
                    'max': 200,
                    'default': 20,
                    'description': '慢速移动平均线的周期'
                },
                'ma_type': {
                    'name': '均线类型',
                    'type': 'select',
                    'options': [
                        {'value': 'sma', 'label': '简单移动平均(SMA)'},
                        {'value': 'ema', 'label': '指数移动平均(EMA)'}
                    ],
                    'default': 'sma',
                    'description': '移动平均线的计算方法'
                },
                'position_size_pct': {
                    'name': '仓位比例',
                    'type': 'float',
                    'min': 0.1,
                    'max': 1.0,
                    'default': 0.8,
                    'description': '每次开仓使用的资金比例'
                },
                'stop_loss_pct': {
                    'name': '止损百分比',
                    'type': 'float',
                    'min': 0.01,
                    'max': 0.5,
                    'default': 0.05,
                    'description': '止损的百分比'
                },
                'take_profit_pct': {
                    'name': '止盈百分比',
                    'type': 'float',
                    'min': 0.01,
                    'max': 2.0,
                    'default': 0.1,
                    'description': '止盈的百分比'
                },
                'enable_trend_filter': {
                    'name': '启用趋势过滤',
                    'type': 'select',
                    'options': [
                        {'value': False, 'label': '关闭'},
                        {'value': True, 'label': '开启'}
                    ],
                    'default': True,
                    'description': '使用长期均线过滤交易方向'
                },
                'trend_period': {
                    'name': '趋势周期',
                    'type': 'int',
                    'min': 10,
                    'max': 500,
                    'default': 50,
                    'description': '趋势判断的均线周期'
                }
            },
            'risk_level': 'medium',
            'suitable_market': ['trending'],
            'min_capital': 1000
        }
    
    def calculate_risk_metrics(self, current_price: float) -> Dict[str, Any]:
        """计算风险指标"""
        # 计算最大仓位价值
        max_position_value = self.initial_capital * self.position_size_pct
        
        # 计算当前信号强度
        signal_strength = 0
        if len(self.fast_ma_history) >= 2 and len(self.slow_ma_history) >= 2:
            fast_ma = self.fast_ma_history[-1]
            slow_ma = self.slow_ma_history[-1]
            signal_strength = abs(fast_ma - slow_ma) / slow_ma
        
        # 计算趋势强度
        trend_strength = 0
        if len(self.trend_ma_history) >= 1:
            trend_ma = self.trend_ma_history[-1]
            trend_strength = abs(current_price - trend_ma) / trend_ma
        
        return {
            'max_position_value': max_position_value,
            'position_size_pct': self.position_size_pct,
            'signal_strength': signal_strength,
            'trend_strength': trend_strength,
            'in_position': self.in_position,
            'entry_price': self.entry_price,
            'current_price': current_price,
            'risk_level': 'medium' if self.position_size_pct > 0.5 else 'low'
        }
