"""
DCA (Dollar Cost Averaging) 定投策略实现
"""
from typing import Dict, List, Tuple, Any
from datetime import datetime, timedelta
import numpy as np
from ..core.strategy_engine import BaseStrategy, MarketData


class DCAStrategy(BaseStrategy):
    """
    DCA定投策略
    定期定额投资，平摊成本
    """
    
    def __init__(self, symbol: str, initial_capital: float, parameters: Dict[str, Any]):
        super().__init__(symbol, initial_capital, parameters)
        
        # DCA参数
        self.investment_amount = parameters.get('investment_amount', 100.0)  # 每次投资金额
        self.investment_interval = parameters.get('investment_interval', 24)  # 投资间隔(小时)
        self.price_drop_threshold = parameters.get('price_drop_threshold', 0.0)  # 价格下跌阈值
        self.take_profit_pct = parameters.get('take_profit_pct', 0.2)  # 止盈百分比
        self.max_investment_count = parameters.get('max_investment_count', 50)  # 最大投资次数
        self.enable_smart_dca = parameters.get('enable_smart_dca', False)  # 智能DCA
        
        # 状态变量
        self.last_investment_time = None
        self.investment_count = 0
        self.total_invested = 0.0
        self.price_history = []
        self.investment_records = []
    
    def generate_signals(self, data: List[MarketData]) -> List[Tuple[datetime, str, float, float]]:
        """生成DCA交易信号"""
        signals = []
        
        for market_data in data:
            self.price_history.append(market_data.close)
            
            # 保持价格历史长度
            if len(self.price_history) > 100:
                self.price_history = self.price_history[-100:]
            
            current_price = market_data.close
            
            # 检查止盈条件
            if self.position.amount > 0:
                avg_cost = self.position.avg_price
                profit_pct = (current_price - avg_cost) / avg_cost
                
                if profit_pct >= self.take_profit_pct:
                    # 全部卖出止盈
                    amount = self.position.amount
                    signals.append((market_data.timestamp, 'sell', current_price, amount))
                    self._reset_investment_state()
                    continue
            
            # 检查投资条件
            should_invest = self._should_invest(market_data)
            
            if should_invest and self.investment_count < self.max_investment_count:
                amount = self._calculate_investment_amount(current_price)
                if amount > 0 and self._can_buy(current_price, amount):
                    signals.append((market_data.timestamp, 'buy', current_price, amount))
                    self.last_investment_time = market_data.timestamp
                    self.investment_count += 1
                    self.total_invested += current_price * amount
                    self.investment_records.append({
                        'time': market_data.timestamp,
                        'price': current_price,
                        'amount': amount
                    })
        
        return signals
    
    def _should_invest(self, market_data: MarketData) -> bool:
        """判断是否应该投资"""
        current_time = market_data.timestamp
        current_price = market_data.close
        
        # 检查时间间隔
        if self.last_investment_time is not None:
            time_diff = current_time - self.last_investment_time
            if time_diff.total_seconds() < self.investment_interval * 3600:
                return False
        
        # 如果没有价格下跌阈值，直接投资
        if self.price_drop_threshold <= 0:
            return True
        
        # 检查价格下跌条件
        if len(self.price_history) >= 2:
            previous_price = self.price_history[-2]
            price_change = (current_price - previous_price) / previous_price
            
            if price_change <= -self.price_drop_threshold:
                return True
        
        # 智能DCA：根据RSI等指标调整投资时机
        if self.enable_smart_dca and len(self.price_history) >= 14:
            rsi = self._calculate_rsi()
            # RSI低于30时增加投资
            if rsi < 30:
                return True
            # RSI高于70时暂停投资
            elif rsi > 70:
                return False
        
        # 定期投资
        return self.last_investment_time is None or \
               (current_time - self.last_investment_time).total_seconds() >= self.investment_interval * 3600
    
    def _calculate_investment_amount(self, current_price: float) -> float:
        """计算投资金额"""
        if self.enable_smart_dca and len(self.price_history) >= 14:
            # 智能DCA：根据RSI调整投资金额
            rsi = self._calculate_rsi()
            if rsi < 20:
                # 超卖时增加投资
                multiplier = 1.5
            elif rsi < 30:
                multiplier = 1.2
            elif rsi > 70:
                # 超买时减少投资
                multiplier = 0.5
            else:
                multiplier = 1.0
            
            adjusted_amount = self.investment_amount * multiplier
        else:
            adjusted_amount = self.investment_amount
        
        return adjusted_amount / current_price
    
    def _calculate_rsi(self, period: int = 14) -> float:
        """计算RSI指标"""
        if len(self.price_history) < period + 1:
            return 50.0
        
        prices = np.array(self.price_history[-period-1:])
        deltas = np.diff(prices)
        
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _can_buy(self, price: float, amount: float) -> bool:
        """检查是否可以买入"""
        cost = price * amount * 1.001  # 包含手续费
        return self.cash >= cost
    
    def _reset_investment_state(self):
        """重置投资状态"""
        self.investment_count = 0
        self.total_invested = 0.0
        self.investment_records = []
        self.last_investment_time = None
    
    def validate_parameters(self) -> Tuple[bool, str]:
        """验证策略参数"""
        if self.investment_amount <= 0:
            return False, "每次投资金额必须大于0"
        
        if self.investment_interval <= 0:
            return False, "投资间隔必须大于0小时"
        
        if self.price_drop_threshold < 0 or self.price_drop_threshold > 1:
            return False, "价格下跌阈值必须在0-100%之间"
        
        if self.take_profit_pct <= 0 or self.take_profit_pct > 5:
            return False, "止盈百分比必须在0-500%之间"
        
        if self.max_investment_count <= 0 or self.max_investment_count > 1000:
            return False, "最大投资次数必须在1-1000之间"
        
        # 检查总投资金额
        max_total_investment = self.investment_amount * self.max_investment_count
        if max_total_investment > self.initial_capital:
            return False, f"最大总投资({max_total_investment:.2f})超过初始资金({self.initial_capital})"
        
        return True, "参数验证通过"
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': 'DCA定投策略',
            'description': '定期定额投资，通过时间分散降低成本波动风险',
            'parameters': {
                'investment_amount': {
                    'name': '每次投资金额',
                    'type': 'float',
                    'min': 10,
                    'default': 100,
                    'description': '每次定投的金额'
                },
                'investment_interval': {
                    'name': '投资间隔(小时)',
                    'type': 'int',
                    'min': 1,
                    'max': 168,  # 一周
                    'default': 24,
                    'description': '两次投资之间的时间间隔'
                },
                'price_drop_threshold': {
                    'name': '价格下跌阈值',
                    'type': 'float',
                    'min': 0,
                    'max': 1,
                    'default': 0.05,
                    'description': '价格下跌超过此阈值时才投资(0表示定期投资)'
                },
                'take_profit_pct': {
                    'name': '止盈百分比',
                    'type': 'float',
                    'min': 0.1,
                    'max': 5.0,
                    'default': 0.2,
                    'description': '达到此盈利百分比时全部卖出'
                },
                'max_investment_count': {
                    'name': '最大投资次数',
                    'type': 'int',
                    'min': 1,
                    'max': 1000,
                    'default': 50,
                    'description': '最多允许投资的次数'
                },
                'enable_smart_dca': {
                    'name': '启用智能DCA',
                    'type': 'select',
                    'options': [
                        {'value': False, 'label': '关闭'},
                        {'value': True, 'label': '开启'}
                    ],
                    'default': False,
                    'description': '根据RSI指标调整投资时机和金额'
                }
            },
            'risk_level': 'low',
            'suitable_market': ['bear', 'volatile'],
            'min_capital': 500
        }
    
    def calculate_risk_metrics(self, current_price: float) -> Dict[str, Any]:
        """计算风险指标"""
        # 计算最大投资金额
        max_total_investment = self.investment_amount * self.max_investment_count
        
        # 计算资金利用率
        capital_utilization = max_total_investment / self.initial_capital
        
        # 计算平均成本
        if self.investment_records:
            total_cost = sum(record['price'] * record['amount'] for record in self.investment_records)
            total_amount = sum(record['amount'] for record in self.investment_records)
            avg_cost = total_cost / total_amount if total_amount > 0 else current_price
        else:
            avg_cost = current_price
        
        # 计算当前盈亏
        if self.position.amount > 0:
            current_value = self.position.amount * current_price
            cost_value = self.total_invested
            unrealized_pnl = current_value - cost_value
            unrealized_pnl_pct = unrealized_pnl / cost_value if cost_value > 0 else 0
        else:
            unrealized_pnl = 0
            unrealized_pnl_pct = 0
        
        return {
            'max_total_investment': max_total_investment,
            'capital_utilization': capital_utilization,
            'investment_progress': self.investment_count / self.max_investment_count,
            'avg_cost': avg_cost,
            'current_price': current_price,
            'unrealized_pnl': unrealized_pnl,
            'unrealized_pnl_pct': unrealized_pnl_pct,
            'risk_level': 'low' if capital_utilization < 0.8 else 'medium'
        }
