"""
网格策略实现
"""
from typing import Dict, List, Tuple, Any
from datetime import datetime
import numpy as np
from ..core.strategy_engine import BaseStrategy, MarketData


class GridStrategy(BaseStrategy):
    """
    网格策略
    在价格区间内设置买卖网格，低买高卖
    """
    
    def __init__(self, symbol: str, initial_capital: float, parameters: Dict[str, Any]):
        super().__init__(symbol, initial_capital, parameters)
        
        # 网格参数
        self.grid_count = parameters.get('grid_count', 10)  # 网格数量
        self.price_range_lower = parameters.get('price_range_lower', 0.0)  # 价格下限
        self.price_range_upper = parameters.get('price_range_upper', 0.0)  # 价格上限
        self.grid_spacing = parameters.get('grid_spacing', 'arithmetic')  # 网格间距类型
        self.investment_per_grid = parameters.get('investment_per_grid', 0.0)  # 每格投资金额
        
        # 计算网格价格
        self.grid_prices = self._calculate_grid_prices()
        self.grid_orders: Dict[float, Dict] = {}  # 网格订单状态
        
        # 初始化网格订单状态
        for price in self.grid_prices:
            self.grid_orders[price] = {
                'buy_filled': False,
                'sell_filled': False,
                'position': 0.0
            }
    
    def _calculate_grid_prices(self) -> List[float]:
        """计算网格价格"""
        if self.grid_spacing == 'arithmetic':
            # 等差数列
            return np.linspace(
                self.price_range_lower, 
                self.price_range_upper, 
                self.grid_count
            ).tolist()
        elif self.grid_spacing == 'geometric':
            # 等比数列
            ratio = (self.price_range_upper / self.price_range_lower) ** (1 / (self.grid_count - 1))
            prices = []
            for i in range(self.grid_count):
                price = self.price_range_lower * (ratio ** i)
                prices.append(price)
            return prices
        else:
            raise ValueError(f"Unsupported grid spacing: {self.grid_spacing}")
    
    def generate_signals(self, data: List[MarketData]) -> List[Tuple[datetime, str, float, float]]:
        """生成网格交易信号"""
        signals = []
        
        for market_data in data:
            current_price = market_data.close
            
            # 检查每个网格价格
            for grid_price in self.grid_prices:
                grid_order = self.grid_orders[grid_price]
                
                # 买入信号：价格跌破网格价格且该网格未买入
                if (current_price <= grid_price and 
                    not grid_order['buy_filled'] and 
                    self._can_buy(grid_price)):
                    
                    amount = self._calculate_buy_amount(grid_price)
                    if amount > 0:
                        signals.append((market_data.timestamp, 'buy', grid_price, amount))
                        grid_order['buy_filled'] = True
                        grid_order['position'] += amount
                
                # 卖出信号：价格突破网格价格且该网格有持仓
                elif (current_price >= grid_price and 
                      grid_order['position'] > 0 and 
                      not grid_order['sell_filled']):
                    
                    amount = grid_order['position']
                    signals.append((market_data.timestamp, 'sell', grid_price, amount))
                    grid_order['sell_filled'] = True
                    grid_order['position'] = 0
                    
                    # 重置买入状态，允许再次买入
                    grid_order['buy_filled'] = False
        
        return signals
    
    def _can_buy(self, price: float) -> bool:
        """检查是否可以买入"""
        cost = self.investment_per_grid + (price * self.investment_per_grid / price * 0.001)  # 包含手续费
        return self.cash >= cost
    
    def _calculate_buy_amount(self, price: float) -> float:
        """计算买入数量"""
        if self.investment_per_grid > 0:
            # 固定金额模式
            return self.investment_per_grid / price
        else:
            # 等分资金模式
            available_cash = self.cash
            return (available_cash / len(self.grid_prices)) / price
    
    def validate_parameters(self) -> Tuple[bool, str]:
        """验证策略参数"""
        if self.price_range_lower <= 0:
            return False, "价格下限必须大于0"
        
        if self.price_range_upper <= self.price_range_lower:
            return False, "价格上限必须大于价格下限"
        
        if self.grid_count < 2:
            return False, "网格数量必须至少为2"
        
        if self.grid_count > 100:
            return False, "网格数量不能超过100"
        
        if self.investment_per_grid <= 0:
            return False, "每格投资金额必须大于0"
        
        total_investment = self.investment_per_grid * self.grid_count
        if total_investment > self.initial_capital:
            return False, f"总投资金额({total_investment})不能超过初始资金({self.initial_capital})"
        
        return True, "参数验证通过"
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': '网格策略',
            'description': '在价格区间内设置买卖网格，通过低买高卖获取收益',
            'parameters': {
                'grid_count': {
                    'name': '网格数量',
                    'type': 'int',
                    'min': 2,
                    'max': 100,
                    'default': 10,
                    'description': '在价格区间内设置的网格数量'
                },
                'price_range_lower': {
                    'name': '价格下限',
                    'type': 'float',
                    'min': 0.01,
                    'description': '网格价格区间的下限'
                },
                'price_range_upper': {
                    'name': '价格上限',
                    'type': 'float',
                    'min': 0.01,
                    'description': '网格价格区间的上限'
                },
                'grid_spacing': {
                    'name': '网格间距',
                    'type': 'select',
                    'options': [
                        {'value': 'arithmetic', 'label': '等差间距'},
                        {'value': 'geometric', 'label': '等比间距'}
                    ],
                    'default': 'arithmetic',
                    'description': '网格价格的分布方式'
                },
                'investment_per_grid': {
                    'name': '每格投资金额',
                    'type': 'float',
                    'min': 1,
                    'description': '每个网格的投资金额'
                }
            },
            'risk_level': 'medium',
            'suitable_market': ['sideways', 'volatile'],
            'min_capital': 1000
        }
    
    def calculate_risk_metrics(self, current_price: float) -> Dict[str, Any]:
        """计算风险指标"""
        # 计算最大可能亏损
        max_loss = 0
        for grid_price in self.grid_prices:
            if grid_price > current_price:
                # 如果在当前价格之上的网格都买入，最大亏损
                loss = (grid_price - self.price_range_lower) * self.investment_per_grid / grid_price
                max_loss += loss
        
        # 计算资金利用率
        total_investment = self.investment_per_grid * self.grid_count
        capital_utilization = total_investment / self.initial_capital
        
        # 计算价格覆盖范围
        price_range = (self.price_range_upper - self.price_range_lower) / current_price
        
        return {
            'max_potential_loss': max_loss,
            'capital_utilization': capital_utilization,
            'price_coverage': price_range,
            'grid_density': self.grid_count / price_range,
            'risk_level': self._assess_risk_level(capital_utilization, price_range)
        }
    
    def _assess_risk_level(self, capital_utilization: float, price_range: float) -> str:
        """评估风险等级"""
        if capital_utilization > 0.8 or price_range < 0.1:
            return 'high'
        elif capital_utilization > 0.5 or price_range < 0.2:
            return 'medium'
        else:
            return 'low'
