"""
马丁格尔策略实现
"""
from typing import Dict, List, Tuple, Any
from datetime import datetime
import numpy as np
from ..core.strategy_engine import BaseStrategy, MarketData


class MartingaleStrategy(BaseStrategy):
    """
    马丁格尔策略
    亏损时加倍投注，盈利时重置投注金额
    """
    
    def __init__(self, symbol: str, initial_capital: float, parameters: Dict[str, Any]):
        super().__init__(symbol, initial_capital, parameters)
        
        # 马丁格尔参数
        self.base_amount = parameters.get('base_amount', 100.0)  # 基础投注金额
        self.multiplier = parameters.get('multiplier', 2.0)  # 倍数
        self.max_levels = parameters.get('max_levels', 5)  # 最大加倍次数
        self.take_profit_pct = parameters.get('take_profit_pct', 0.02)  # 止盈百分比
        self.stop_loss_pct = parameters.get('stop_loss_pct', 0.1)  # 止损百分比
        self.signal_type = parameters.get('signal_type', 'rsi')  # 信号类型
        
        # 状态变量
        self.current_level = 0  # 当前加倍级别
        self.entry_price = 0.0  # 入场价格
        self.total_invested = 0.0  # 总投资金额
        self.last_signal_time = None
        self.in_position = False
        
        # 技术指标缓存
        self.price_history = []
        self.rsi_period = parameters.get('rsi_period', 14)
        self.rsi_oversold = parameters.get('rsi_oversold', 30)
        self.rsi_overbought = parameters.get('rsi_overbought', 70)
    
    def generate_signals(self, data: List[MarketData]) -> List[Tuple[datetime, str, float, float]]:
        """生成马丁格尔交易信号"""
        signals = []
        
        for market_data in data:
            self.price_history.append(market_data.close)
            
            # 保持价格历史长度
            if len(self.price_history) > max(self.rsi_period * 2, 100):
                self.price_history = self.price_history[-100:]
            
            current_price = market_data.close
            
            # 检查止盈止损
            if self.in_position:
                pnl_pct = (current_price - self.entry_price) / self.entry_price
                
                # 止盈
                if pnl_pct >= self.take_profit_pct:
                    amount = self.position.amount
                    if amount > 0:
                        signals.append((market_data.timestamp, 'sell', current_price, amount))
                        self._reset_position()
                
                # 止损或达到最大级别
                elif pnl_pct <= -self.stop_loss_pct or self.current_level >= self.max_levels:
                    amount = self.position.amount
                    if amount > 0:
                        signals.append((market_data.timestamp, 'sell', current_price, amount))
                        self._reset_position()
                
                # 加倍买入
                elif pnl_pct <= -0.05 * (self.current_level + 1):  # 每5%亏损加倍
                    if self.current_level < self.max_levels:
                        buy_amount = self._calculate_martingale_amount()
                        if self._can_buy(current_price, buy_amount):
                            signals.append((market_data.timestamp, 'buy', current_price, buy_amount))
                            self.current_level += 1
                            self.total_invested += current_price * buy_amount
            
            # 生成入场信号
            else:
                signal = self._generate_entry_signal(market_data)
                if signal:
                    side, price, amount = signal
                    signals.append((market_data.timestamp, side, price, amount))
                    if side == 'buy':
                        self.in_position = True
                        self.entry_price = price
                        self.current_level = 0
                        self.total_invested = price * amount
        
        return signals
    
    def _generate_entry_signal(self, market_data: MarketData) -> Tuple[str, float, float] | None:
        """生成入场信号"""
        if len(self.price_history) < self.rsi_period:
            return None
        
        current_price = market_data.close
        
        if self.signal_type == 'rsi':
            rsi = self._calculate_rsi()
            if rsi < self.rsi_oversold:  # RSI超卖，买入信号
                amount = self.base_amount / current_price
                if self._can_buy(current_price, amount):
                    return ('buy', current_price, amount)
        
        elif self.signal_type == 'price_drop':
            # 价格下跌信号
            if len(self.price_history) >= 2:
                price_change = (current_price - self.price_history[-2]) / self.price_history[-2]
                if price_change <= -0.02:  # 价格下跌2%
                    amount = self.base_amount / current_price
                    if self._can_buy(current_price, amount):
                        return ('buy', current_price, amount)
        
        return None
    
    def _calculate_rsi(self) -> float:
        """计算RSI指标"""
        if len(self.price_history) < self.rsi_period + 1:
            return 50.0
        
        prices = np.array(self.price_history[-self.rsi_period-1:])
        deltas = np.diff(prices)
        
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def _calculate_martingale_amount(self) -> float:
        """计算马丁格尔加倍金额"""
        return (self.base_amount * (self.multiplier ** self.current_level)) / self.price_history[-1]
    
    def _can_buy(self, price: float, amount: float) -> bool:
        """检查是否可以买入"""
        cost = price * amount * 1.001  # 包含手续费
        return self.cash >= cost
    
    def _reset_position(self):
        """重置持仓状态"""
        self.in_position = False
        self.current_level = 0
        self.entry_price = 0.0
        self.total_invested = 0.0
    
    def validate_parameters(self) -> Tuple[bool, str]:
        """验证策略参数"""
        if self.base_amount <= 0:
            return False, "基础投注金额必须大于0"
        
        if self.multiplier <= 1:
            return False, "倍数必须大于1"
        
        if self.max_levels < 1 or self.max_levels > 10:
            return False, "最大加倍次数必须在1-10之间"
        
        if self.take_profit_pct <= 0 or self.take_profit_pct > 1:
            return False, "止盈百分比必须在0-100%之间"
        
        if self.stop_loss_pct <= 0 or self.stop_loss_pct > 1:
            return False, "止损百分比必须在0-100%之间"
        
        # 计算最大可能投资
        max_investment = self.base_amount * sum(self.multiplier ** i for i in range(self.max_levels + 1))
        if max_investment > self.initial_capital * 0.8:
            return False, f"最大可能投资({max_investment:.2f})超过资金的80%"
        
        return True, "参数验证通过"
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': '马丁格尔策略',
            'description': '亏损时加倍投注，通过概率优势获取收益',
            'parameters': {
                'base_amount': {
                    'name': '基础投注金额',
                    'type': 'float',
                    'min': 10,
                    'default': 100,
                    'description': '初始投注金额'
                },
                'multiplier': {
                    'name': '倍数',
                    'type': 'float',
                    'min': 1.1,
                    'max': 5.0,
                    'default': 2.0,
                    'description': '亏损时的加倍倍数'
                },
                'max_levels': {
                    'name': '最大加倍次数',
                    'type': 'int',
                    'min': 1,
                    'max': 10,
                    'default': 5,
                    'description': '最多允许加倍的次数'
                },
                'take_profit_pct': {
                    'name': '止盈百分比',
                    'type': 'float',
                    'min': 0.01,
                    'max': 1.0,
                    'default': 0.02,
                    'description': '达到此盈利百分比时止盈'
                },
                'stop_loss_pct': {
                    'name': '止损百分比',
                    'type': 'float',
                    'min': 0.01,
                    'max': 1.0,
                    'default': 0.1,
                    'description': '达到此亏损百分比时止损'
                },
                'signal_type': {
                    'name': '信号类型',
                    'type': 'select',
                    'options': [
                        {'value': 'rsi', 'label': 'RSI超卖'},
                        {'value': 'price_drop', 'label': '价格下跌'}
                    ],
                    'default': 'rsi',
                    'description': '入场信号的类型'
                }
            },
            'risk_level': 'high',
            'suitable_market': ['trending'],
            'min_capital': 1000
        }
    
    def calculate_risk_metrics(self, current_price: float) -> Dict[str, Any]:
        """计算风险指标"""
        # 计算最大可能亏损
        max_investment = self.base_amount * sum(self.multiplier ** i for i in range(self.max_levels + 1))
        max_loss = max_investment * self.stop_loss_pct
        
        # 计算资金利用率
        capital_utilization = max_investment / self.initial_capital
        
        # 计算连续亏损概率
        consecutive_loss_prob = 0.5 ** (self.max_levels + 1)
        
        return {
            'max_potential_loss': max_loss,
            'max_investment': max_investment,
            'capital_utilization': capital_utilization,
            'consecutive_loss_probability': consecutive_loss_prob,
            'risk_level': 'high' if capital_utilization > 0.6 else 'medium'
        }
