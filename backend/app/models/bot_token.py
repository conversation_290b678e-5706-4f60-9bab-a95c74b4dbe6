"""
Telegram Bot Token模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from sqlalchemy.sql import func
from ..core.database import Base


class BotToken(Base):
    """Telegram Bot Token模型"""
    __tablename__ = "bot_tokens"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 加密存储的Bot Token
    encrypted_token = Column(Text, nullable=False)
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    
    # 验证信息
    bot_username = Column(String(100), nullable=True)  # Bot用户名
    bot_name = Column(String(200), nullable=True)      # Bot显示名称
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_verified_at = Column(DateTime(timezone=True), nullable=True)
    
    # 备注
    notes = Column(Text, nullable=True)
    
    def __repr__(self):
        return f"<BotToken(id={self.id}, bot_username='{self.bot_username}', is_active={self.is_active})>"
    
    def to_dict(self, include_token=False):
        """转换为字典"""
        data = {
            "id": self.id,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "bot_username": self.bot_username,
            "bot_name": self.bot_name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_verified_at": self.last_verified_at.isoformat() if self.last_verified_at else None,
            "notes": self.notes
        }
        
        # 安全考虑，默认不包含加密token
        if include_token:
            data["encrypted_token"] = self.encrypted_token
        
        return data
