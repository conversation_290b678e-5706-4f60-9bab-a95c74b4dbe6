"""
持仓模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float, Boolean, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from ..core.database import Base


class PositionSide(PyEnum):
    """持仓方向"""
    LONG = "long"
    SHORT = "short"
    BOTH = "both"  # 双向持仓


class PositionStatus(PyEnum):
    """持仓状态"""
    OPEN = "open"
    CLOSED = "closed"
    LIQUIDATED = "liquidated"


class Position(Base):
    """持仓模型"""
    __tablename__ = "positions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    api_key_id = Column(Integer, ForeignKey("api_keys.id"), nullable=False)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=True)
    
    # 交易所信息
    exchange = Column(String(20), nullable=False)
    
    # 持仓信息
    symbol = Column(String(20), nullable=False, index=True)
    side = Column(Enum(PositionSide), nullable=False)
    size = Column(Float, nullable=False)  # 持仓数量
    entry_price = Column(Float, nullable=False)  # 开仓价格
    mark_price = Column(Float, nullable=True)  # 标记价格
    
    # 杠杆信息
    leverage = Column(Float, default=1.0)  # 杠杆倍数
    margin = Column(Float, nullable=True)  # 保证金
    
    # 盈亏信息
    unrealized_pnl = Column(Float, default=0.0)  # 未实现盈亏
    realized_pnl = Column(Float, default=0.0)  # 已实现盈亏
    pnl_percentage = Column(Float, default=0.0)  # 盈亏百分比
    
    # 风险信息
    liquidation_price = Column(Float, nullable=True)  # 强平价格
    maintenance_margin = Column(Float, nullable=True)  # 维持保证金
    
    # 状态
    status = Column(Enum(PositionStatus), nullable=False, default=PositionStatus.OPEN)
    is_isolated = Column(Boolean, default=False)  # 是否逐仓模式
    
    # 时间信息
    opened_at = Column(DateTime(timezone=True), server_default=func.now())
    closed_at = Column(DateTime(timezone=True), nullable=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 备注
    notes = Column(Text, nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="positions")
    api_key = relationship("APIKey", back_populates="positions")
    strategy = relationship("Strategy", back_populates="positions")
    
    def __repr__(self):
        return f"<Position(id={self.id}, symbol='{self.symbol}', side='{self.side}', size={self.size})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "api_key_id": self.api_key_id,
            "strategy_id": self.strategy_id,
            "exchange": self.exchange,
            "symbol": self.symbol,
            "side": self.side.value if self.side else None,
            "size": self.size,
            "entry_price": self.entry_price,
            "mark_price": self.mark_price,
            "leverage": self.leverage,
            "margin": self.margin,
            "unrealized_pnl": self.unrealized_pnl,
            "realized_pnl": self.realized_pnl,
            "pnl_percentage": self.pnl_percentage,
            "liquidation_price": self.liquidation_price,
            "maintenance_margin": self.maintenance_margin,
            "status": self.status.value if self.status else None,
            "is_isolated": self.is_isolated,
            "opened_at": self.opened_at.isoformat() if self.opened_at else None,
            "closed_at": self.closed_at.isoformat() if self.closed_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "notes": self.notes
        }
    
    @property
    def is_open(self) -> bool:
        """是否为开仓状态"""
        return self.status == PositionStatus.OPEN
    
    @property
    def position_value(self) -> float:
        """持仓价值"""
        return abs(self.size) * self.entry_price
    
    @property
    def current_value(self) -> float:
        """当前价值"""
        if self.mark_price:
            return abs(self.size) * self.mark_price
        return self.position_value
