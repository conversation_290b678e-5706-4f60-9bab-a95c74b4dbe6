"""
API密钥模型
"""
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base


class APIKey(Base):
    """API密钥模型"""
    __tablename__ = "api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 交易所信息
    exchange = Column(String(20), nullable=False)  # binance, okx
    name = Column(String(100), nullable=False)  # 用户自定义名称
    
    # 加密存储的密钥信息
    encrypted_api_key = Column(Text, nullable=False)
    encrypted_secret_key = Column(Text, nullable=False)
    encrypted_passphrase = Column(Text, nullable=True)  # OKX需要
    
    # 权限设置
    is_testnet = Column(Boolean, default=False)
    can_trade = Column(Boolean, default=True)
    can_read = Column(Boolean, default=True)
    
    # 状态
    is_active = Column(Boolean, default=True)
    last_used = Column(DateTime(timezone=True), nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="api_keys")
    orders = relationship("Order", back_populates="api_key")
    trades = relationship("Trade", back_populates="api_key")
    positions = relationship("Position", back_populates="api_key")
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, exchange='{self.exchange}', name='{self.name}')>"
    
    def to_dict(self, include_keys=False):
        """转换为字典"""
        data = {
            "id": self.id,
            "user_id": self.user_id,
            "exchange": self.exchange,
            "name": self.name,
            "is_testnet": self.is_testnet,
            "can_trade": self.can_trade,
            "can_read": self.can_read,
            "is_active": self.is_active,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
        
        # 安全考虑，默认不包含加密密钥
        if include_keys:
            data.update({
                "encrypted_api_key": self.encrypted_api_key,
                "encrypted_secret_key": self.encrypted_secret_key,
                "encrypted_passphrase": self.encrypted_passphrase
            })
        
        return data
