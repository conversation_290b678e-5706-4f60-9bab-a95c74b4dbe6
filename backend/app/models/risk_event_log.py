"""
风控事件日志模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float, Boolean, Text, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from ..core.database import Base


class RiskEventType(PyEnum):
    """风控事件类型"""
    MAX_ORDER_AMOUNT = "max_order_amount"  # 最大单笔下单金额
    MAX_DAILY_LOSS = "max_daily_loss"  # 最大日亏损
    MAX_ORDERS_LIMIT = "max_orders_limit"  # 最大挂单数量
    ORDER_RATE_LIMIT = "order_rate_limit"  # 下单频率限制
    STRATEGY_CONFLICT = "strategy_conflict"  # 策略冲突
    INSUFFICIENT_BALANCE = "insufficient_balance"  # 余额不足
    POSITION_LIMIT = "position_limit"  # 持仓限制
    CUSTOM_RULE = "custom_rule"  # 自定义规则


class RiskEventSeverity(PyEnum):
    """风控事件严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskEventAction(PyEnum):
    """风控处理动作"""
    BLOCK = "block"  # 阻止操作
    WARN = "warn"  # 警告
    PAUSE_STRATEGY = "pause_strategy"  # 暂停策略
    STOP_ALL = "stop_all"  # 停止所有策略
    LIQUIDATE = "liquidate"  # 强制平仓


class RiskEventLog(Base):
    """风控事件日志模型"""
    __tablename__ = "risk_event_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=True)
    
    # 事件信息
    event_type = Column(Enum(RiskEventType), nullable=False)
    severity = Column(Enum(RiskEventSeverity), nullable=False, default=RiskEventSeverity.MEDIUM)
    action_taken = Column(Enum(RiskEventAction), nullable=False)
    
    # 触发信息
    trigger_value = Column(Float, nullable=True)  # 触发值
    threshold_value = Column(Float, nullable=True)  # 阈值
    symbol = Column(String(20), nullable=True)  # 相关交易对
    exchange = Column(String(20), nullable=True)  # 相关交易所
    
    # 详细信息
    title = Column(String(200), nullable=False)  # 事件标题
    description = Column(Text, nullable=False)  # 事件描述
    details = Column(JSON, nullable=True)  # 详细数据 (JSON格式)
    
    # 处理状态
    is_resolved = Column(Boolean, default=False)  # 是否已解决
    resolved_at = Column(DateTime(timezone=True), nullable=True)  # 解决时间
    resolved_by = Column(String(100), nullable=True)  # 解决人
    resolution_notes = Column(Text, nullable=True)  # 解决备注
    
    # 通知状态
    notification_sent = Column(Boolean, default=False)  # 是否已发送通知
    notification_sent_at = Column(DateTime(timezone=True), nullable=True)
    
    # 时间信息
    occurred_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    user = relationship("User", back_populates="risk_event_logs")
    strategy = relationship("Strategy")
    
    def __repr__(self):
        return f"<RiskEventLog(id={self.id}, type='{self.event_type}', severity='{self.severity}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "strategy_id": self.strategy_id,
            "event_type": self.event_type.value if self.event_type else None,
            "severity": self.severity.value if self.severity else None,
            "action_taken": self.action_taken.value if self.action_taken else None,
            "trigger_value": self.trigger_value,
            "threshold_value": self.threshold_value,
            "symbol": self.symbol,
            "exchange": self.exchange,
            "title": self.title,
            "description": self.description,
            "details": self.details,
            "is_resolved": self.is_resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "resolved_by": self.resolved_by,
            "resolution_notes": self.resolution_notes,
            "notification_sent": self.notification_sent,
            "notification_sent_at": self.notification_sent_at.isoformat() if self.notification_sent_at else None,
            "occurred_at": self.occurred_at.isoformat() if self.occurred_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
    
    @property
    def is_critical(self) -> bool:
        """是否为严重事件"""
        return self.severity == RiskEventSeverity.CRITICAL
    
    @property
    def needs_immediate_attention(self) -> bool:
        """是否需要立即处理"""
        return (
            self.severity in [RiskEventSeverity.HIGH, RiskEventSeverity.CRITICAL] 
            and not self.is_resolved
        )
