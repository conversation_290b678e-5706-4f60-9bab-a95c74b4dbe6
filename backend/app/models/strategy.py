"""
策略模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float, Boolean, Text, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from ..core.database import Base


class StrategyType(PyEnum):
    """策略类型"""
    MANUAL = "manual"
    GRID = "grid"
    MARTINGALE = "martingale"
    OFFSET_ORDER = "offset_order"
    DCA = "dca"  # 定投策略
    CUSTOM = "custom"


class StrategyStatus(PyEnum):
    """策略状态"""
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    COMPLETED = "completed"


class Strategy(Base):
    """策略模型"""
    __tablename__ = "strategies"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 策略基本信息
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    strategy_type = Column(Enum(StrategyType), nullable=False)
    
    # 交易配置
    exchange = Column(String(20), nullable=False)
    symbol = Column(String(20), nullable=False, index=True)
    market_type = Column(String(20), default="spot")  # spot, futures
    
    # 资金配置
    base_amount = Column(Float, nullable=False)  # 基础投入金额
    max_amount = Column(Float, nullable=True)  # 最大投入金额
    
    # 策略参数 (JSON格式存储)
    parameters = Column(JSON, nullable=True)
    
    # 风控参数
    max_loss = Column(Float, nullable=True)  # 最大亏损
    stop_loss_percentage = Column(Float, nullable=True)  # 止损百分比
    take_profit_percentage = Column(Float, nullable=True)  # 止盈百分比
    
    # 状态信息
    status = Column(Enum(StrategyStatus), nullable=False, default=StrategyStatus.STOPPED)
    is_active = Column(Boolean, default=True)
    
    # 统计信息
    total_orders = Column(Integer, default=0)  # 总订单数
    total_trades = Column(Integer, default=0)  # 总成交数
    total_profit = Column(Float, default=0.0)  # 总盈利
    total_loss = Column(Float, default=0.0)  # 总亏损
    win_rate = Column(Float, default=0.0)  # 胜率
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    stopped_at = Column(DateTime(timezone=True), nullable=True)
    last_executed_at = Column(DateTime(timezone=True), nullable=True)
    
    # 错误信息
    last_error = Column(Text, nullable=True)
    error_count = Column(Integer, default=0)
    
    # 备注
    notes = Column(Text, nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="strategies")
    orders = relationship("Order", back_populates="strategy")
    trades = relationship("Trade", back_populates="strategy")
    positions = relationship("Position", back_populates="strategy")
    
    def __repr__(self):
        return f"<Strategy(id={self.id}, name='{self.name}', type='{self.strategy_type}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.name,
            "description": self.description,
            "strategy_type": self.strategy_type.value if self.strategy_type else None,
            "exchange": self.exchange,
            "symbol": self.symbol,
            "market_type": self.market_type,
            "base_amount": self.base_amount,
            "max_amount": self.max_amount,
            "parameters": self.parameters,
            "max_loss": self.max_loss,
            "stop_loss_percentage": self.stop_loss_percentage,
            "take_profit_percentage": self.take_profit_percentage,
            "status": self.status.value if self.status else None,
            "is_active": self.is_active,
            "total_orders": self.total_orders,
            "total_trades": self.total_trades,
            "total_profit": self.total_profit,
            "total_loss": self.total_loss,
            "win_rate": self.win_rate,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "stopped_at": self.stopped_at.isoformat() if self.stopped_at else None,
            "last_executed_at": self.last_executed_at.isoformat() if self.last_executed_at else None,
            "last_error": self.last_error,
            "error_count": self.error_count,
            "notes": self.notes
        }
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self.status == StrategyStatus.RUNNING
    
    @property
    def net_profit(self) -> float:
        """净盈利"""
        return self.total_profit - self.total_loss
    
    @property
    def profit_factor(self) -> float:
        """盈利因子"""
        if self.total_loss > 0:
            return self.total_profit / self.total_loss
        return 0.0
