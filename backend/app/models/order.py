"""
订单模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Float, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from ..core.database import Base


class OrderType(PyEnum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(PyEnum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(PyEnum):
    """订单状态"""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class MarketType(PyEnum):
    """市场类型"""
    SPOT = "spot"
    FUTURES = "futures"
    MARGIN = "margin"


class Order(Base):
    """订单模型"""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    api_key_id = Column(Integer, ForeignKey("api_keys.id"), nullable=False)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=True)
    
    # 交易所信息
    exchange = Column(String(20), nullable=False)
    exchange_order_id = Column(String(100), nullable=True, index=True)
    
    # 交易对信息
    symbol = Column(String(20), nullable=False, index=True)
    market_type = Column(Enum(MarketType), nullable=False, default=MarketType.SPOT)
    
    # 订单基本信息
    order_type = Column(Enum(OrderType), nullable=False)
    side = Column(Enum(OrderSide), nullable=False)
    amount = Column(Float, nullable=False)  # 数量
    price = Column(Float, nullable=True)  # 价格 (市价单为空)
    
    # 止盈止损
    stop_price = Column(Float, nullable=True)
    take_profit_price = Column(Float, nullable=True)
    stop_loss_price = Column(Float, nullable=True)
    
    # 执行信息
    filled_amount = Column(Float, default=0.0)  # 已成交数量
    avg_price = Column(Float, nullable=True)  # 平均成交价格
    fee = Column(Float, default=0.0)  # 手续费
    fee_currency = Column(String(10), nullable=True)  # 手续费币种
    
    # 状态
    status = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING)
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    executed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    
    # 备注
    notes = Column(Text, nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="orders")
    api_key = relationship("APIKey", back_populates="orders")
    strategy = relationship("Strategy", back_populates="orders")
    trades = relationship("Trade", back_populates="order")
    
    def __repr__(self):
        return f"<Order(id={self.id}, symbol='{self.symbol}', side='{self.side}', amount={self.amount})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "api_key_id": self.api_key_id,
            "strategy_id": self.strategy_id,
            "exchange": self.exchange,
            "exchange_order_id": self.exchange_order_id,
            "symbol": self.symbol,
            "market_type": self.market_type.value if self.market_type else None,
            "order_type": self.order_type.value if self.order_type else None,
            "side": self.side.value if self.side else None,
            "amount": self.amount,
            "price": self.price,
            "stop_price": self.stop_price,
            "take_profit_price": self.take_profit_price,
            "stop_loss_price": self.stop_loss_price,
            "filled_amount": self.filled_amount,
            "avg_price": self.avg_price,
            "fee": self.fee,
            "fee_currency": self.fee_currency,
            "status": self.status.value if self.status else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "executed_at": self.executed_at.isoformat() if self.executed_at else None,
            "cancelled_at": self.cancelled_at.isoformat() if self.cancelled_at else None,
            "notes": self.notes
        }
    
    @property
    def is_filled(self) -> bool:
        """是否完全成交"""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_active(self) -> bool:
        """是否为活跃订单"""
        return self.status in [OrderStatus.PENDING, OrderStatus.OPEN, OrderStatus.PARTIALLY_FILLED]
    
    @property
    def remaining_amount(self) -> float:
        """剩余数量"""
        return self.amount - self.filled_amount
