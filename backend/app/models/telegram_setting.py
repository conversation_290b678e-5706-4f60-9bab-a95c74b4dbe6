"""
Telegram设置模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base


class TelegramSetting(Base):
    """Telegram设置模型"""
    __tablename__ = "telegram_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    
    # Telegram配置
    chat_id = Column(String(50), nullable=True)  # Telegram Chat ID
    username = Column(String(100), nullable=True)  # Telegram用户名
    
    # 通知开关
    enabled = Column(Boolean, default=False)  # 是否启用通知
    notify_orders = Column(Boolean, default=True)  # 订单通知
    notify_trades = Column(Boolean, default=True)  # 成交通知
    notify_strategies = Column(Boolean, default=True)  # 策略通知
    notify_risks = Column(Boolean, default=True)  # 风控通知
    notify_errors = Column(<PERSON><PERSON><PERSON>, default=True)  # 错误通知
    
    # 通知频率控制
    max_notifications_per_hour = Column(Integer, default=60)  # 每小时最大通知数
    quiet_hours_start = Column(String(5), nullable=True)  # 免打扰开始时间 (HH:MM)
    quiet_hours_end = Column(String(5), nullable=True)  # 免打扰结束时间 (HH:MM)
    
    # 状态信息
    is_verified = Column(Boolean, default=False)  # 是否已验证
    last_notification_at = Column(DateTime(timezone=True), nullable=True)
    notification_count_today = Column(Integer, default=0)
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 备注
    notes = Column(Text, nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="telegram_setting")
    
    def __repr__(self):
        return f"<TelegramSetting(id={self.id}, user_id={self.user_id}, chat_id='{self.chat_id}')>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "chat_id": self.chat_id,
            "username": self.username,
            "enabled": self.enabled,
            "notify_orders": self.notify_orders,
            "notify_trades": self.notify_trades,
            "notify_strategies": self.notify_strategies,
            "notify_risks": self.notify_risks,
            "notify_errors": self.notify_errors,
            "max_notifications_per_hour": self.max_notifications_per_hour,
            "quiet_hours_start": self.quiet_hours_start,
            "quiet_hours_end": self.quiet_hours_end,
            "is_verified": self.is_verified,
            "last_notification_at": self.last_notification_at.isoformat() if self.last_notification_at else None,
            "notification_count_today": self.notification_count_today,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "notes": self.notes
        }
    
    @property
    def can_send_notification(self) -> bool:
        """是否可以发送通知"""
        if not self.enabled or not self.is_verified:
            return False
        
        # 检查通知频率限制
        if self.notification_count_today >= self.max_notifications_per_hour:
            return False
        
        # 检查免打扰时间
        if self.quiet_hours_start and self.quiet_hours_end:
            from datetime import datetime
            now = datetime.now().time()
            start_time = datetime.strptime(self.quiet_hours_start, "%H:%M").time()
            end_time = datetime.strptime(self.quiet_hours_end, "%H:%M").time()
            
            if start_time <= end_time:
                # 同一天内的时间段
                if start_time <= now <= end_time:
                    return False
            else:
                # 跨天的时间段
                if now >= start_time or now <= end_time:
                    return False
        
        return True
