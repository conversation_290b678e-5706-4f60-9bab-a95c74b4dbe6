"""
成交记录模型
"""
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base


class Trade(Base):
    """成交记录模型"""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    api_key_id = Column(Integer, ForeignKey("api_keys.id"), nullable=False)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=True)
    
    # 交易所信息
    exchange = Column(String(20), nullable=False)
    exchange_trade_id = Column(String(100), nullable=True, index=True)
    
    # 交易信息
    symbol = Column(String(20), nullable=False, index=True)
    side = Column(String(10), nullable=False)  # buy, sell
    amount = Column(Float, nullable=False)  # 成交数量
    price = Column(Float, nullable=False)  # 成交价格
    
    # 费用信息
    fee = Column(Float, default=0.0)  # 手续费
    fee_currency = Column(String(10), nullable=True)  # 手续费币种
    
    # 盈亏信息
    pnl = Column(Float, nullable=True)  # 盈亏 (USDT)
    pnl_percentage = Column(Float, nullable=True)  # 盈亏百分比
    
    # 时间信息
    executed_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 备注
    notes = Column(Text, nullable=True)
    
    # 关联关系
    user = relationship("User", back_populates="trades")
    api_key = relationship("APIKey", back_populates="trades")
    order = relationship("Order", back_populates="trades")
    strategy = relationship("Strategy", back_populates="trades")
    
    def __repr__(self):
        return f"<Trade(id={self.id}, symbol='{self.symbol}', side='{self.side}', amount={self.amount}, price={self.price})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "api_key_id": self.api_key_id,
            "order_id": self.order_id,
            "strategy_id": self.strategy_id,
            "exchange": self.exchange,
            "exchange_trade_id": self.exchange_trade_id,
            "symbol": self.symbol,
            "side": self.side,
            "amount": self.amount,
            "price": self.price,
            "fee": self.fee,
            "fee_currency": self.fee_currency,
            "pnl": self.pnl,
            "pnl_percentage": self.pnl_percentage,
            "executed_at": self.executed_at.isoformat() if self.executed_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "notes": self.notes
        }
    
    @property
    def total_value(self) -> float:
        """成交总价值"""
        return self.amount * self.price
    
    @property
    def net_value(self) -> float:
        """净价值 (扣除手续费)"""
        return self.total_value - (self.fee or 0)
