"""
FastAPI主应用
"""
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings
from app.core.database import init_database, db_manager
from app.core.security import validate_master_password
from app.api import auth, users, api_keys, orders, trades, strategies, market_data, telegram_settings, risk_management, positions

# 导入所有模型以确保SQLAlchemy能够识别它们
from app.models import user, api_key, order, trade, strategy, position, telegram_setting, risk_event_log, bot_token

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.log_file),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动个人量化交易平台...")

    try:
        # 验证主密码
        if not validate_master_password():
            logger.error("❌ 主密码验证失败，请检查MASTER_PASSWORD环境变量")
            raise RuntimeError("主密码验证失败")

        logger.info("✅ 主密码验证成功")

        # 初始化数据库
        init_database()
        logger.info("✅ 数据库初始化完成")

        # 测试数据库连接
        if db_manager.test_connection():
            logger.info("✅ 数据库连接测试成功")
        else:
            logger.error("❌ 数据库连接测试失败")
            raise RuntimeError("数据库连接失败")

        logger.info("🎉 应用启动完成")

    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        raise

    yield

    # 关闭时执行
    logger.info("👋 关闭个人量化交易平台...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    description="""
    🧠 个人量化交易平台

    一个安全、轻量、可部署的个人量化交易平台，支持币安与OKX交易所。

    ## 核心特性
    - 🔐 安全加密存储API密钥
    - 🤖 多策略自动化交易
    - 📊 实时行情与数据分析
    - 🛡️ 多层风险控制机制
    - 🔔 Telegram智能通知
    - 📱 响应式Web界面

    ## 支持的交易所
    - Binance (币安)
    - OKX (欧易)

    ## 支持的策略
    - 网格交易
    - 马丁格尔策略
    - 偏移挂单
    - 手动交易
    """,
    version=settings.app_version,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加可信主机中间件 (生产环境)
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "0.0.0.0"]
    )


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "内部服务器错误",
            "message": "服务器遇到了一个错误，请稍后重试",
            "detail": str(exc) if settings.debug else None
        }
    )


# 健康检查端点
@app.get("/health", tags=["系统"])
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        db_status = db_manager.test_connection()

        # 检查主密码
        master_password_status = validate_master_password()

        status_code = status.HTTP_200_OK
        if not db_status or not master_password_status:
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE

        return JSONResponse(
            status_code=status_code,
            content={
                "status": "healthy" if status_code == 200 else "unhealthy",
                "timestamp": "2024-01-01T00:00:00Z",  # 实际应用中使用真实时间戳
                "version": settings.app_version,
                "checks": {
                    "database": "ok" if db_status else "error",
                    "master_password": "ok" if master_password_status else "error"
                }
            }
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "error": str(e)
            }
        )


# 根路径
@app.get("/", tags=["系统"])
async def root():
    """根路径"""
    return {
        "message": "欢迎使用个人量化交易平台",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "docs_url": "/docs",
        "health_check": "/health"
    }


# 系统信息
@app.get("/info", tags=["系统"])
async def system_info():
    """系统信息"""
    table_info = db_manager.get_table_info()

    return {
        "app_name": settings.app_name,
        "version": settings.app_version,
        "debug": settings.debug,
        "database": {
            "url": settings.database_url.split("://")[0] + "://***",  # 隐藏敏感信息
            "tables": table_info["total_tables"]
        },
        "supported_exchanges": ["binance", "okx"],
        "supported_strategies": ["grid", "martingale", "offset_order", "manual"]
    }


# 注册路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(users.router, prefix="/api/v1/users", tags=["用户"])
app.include_router(api_keys.router, prefix="/api/v1/api-keys", tags=["API密钥"])
app.include_router(orders.router, prefix="/api/v1/orders", tags=["订单"])
app.include_router(trades.router, prefix="/api/v1/trades", tags=["交易"])
app.include_router(strategies.router, prefix="/api/v1/strategies", tags=["策略"])
app.include_router(positions.router, prefix="/api/v1/positions", tags=["持仓"])
app.include_router(market_data.router, prefix="/api/v1/market", tags=["行情"])
app.include_router(telegram_settings.router, prefix="/api/v1/telegram", tags=["Telegram"])
app.include_router(risk_management.router, prefix="/api/v1/risk", tags=["风控"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.backend_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
