2025-05-29 08:48:27,373 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:48:27,399 - main - INFO - ✅ 主密码验证成功
2025-05-29 08:48:27,416 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:48:27,416 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:48:27,416 - main - INFO - ✅ 数据库初始化完成
2025-05-29 08:48:27,416 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:48:27,416 - main - INFO - 🎉 应用启动完成
2025-05-29 08:48:35,689 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 08:49:05,765 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 08:54:59,948 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 08:55:42,163 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:55:42,183 - main - INFO - ✅ 主密码验证成功
2025-05-29 08:55:42,185 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:55:42,185 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:55:42,185 - main - INFO - ✅ 数据库初始化完成
2025-05-29 08:55:42,185 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:55:42,185 - main - INFO - 🎉 应用启动完成
2025-05-29 09:04:25,605 - app.core.database - ERROR - 数据库会话错误: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 09:04:25,606 - main - ERROR - 未处理的异常: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlite3.OperationalError: cannot commit - no transaction is active

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 264, in app
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 563, in solve_dependencies
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 592, in solve_dependencies
    solved = await call(**sub_values)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/app/api/auth.py", line 106, in get_current_user
    db.commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1969, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1263, in commit
    trans.commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2619, in commit
    self._do_commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2724, in _do_commit
    self._connection_commit_impl()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2695, in _connection_commit_impl
    self.connection._commit_impl()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1138, in _commit_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 09:51:02,169 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 09:51:02,173 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 09:51:08,420 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 10:37:14,286 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 10:37:14,289 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 10:41:05,574 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:06,151 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:06,174 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:06,175 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:06,175 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:06,175 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:06,175 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:06,175 - main - INFO - 🎉 应用启动完成
2025-05-29 10:41:19,715 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:20,135 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:20,155 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:20,157 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:20,157 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:20,157 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:20,158 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:20,158 - main - INFO - 🎉 应用启动完成
2025-05-29 10:41:35,817 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:36,304 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:36,327 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:36,329 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:36,329 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:36,329 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:36,329 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:36,329 - main - INFO - 🎉 应用启动完成
