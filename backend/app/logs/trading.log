2025-05-29 08:48:27,373 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:48:27,399 - main - INFO - ✅ 主密码验证成功
2025-05-29 08:48:27,416 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:48:27,416 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:48:27,416 - main - INFO - ✅ 数据库初始化完成
2025-05-29 08:48:27,416 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:48:27,416 - main - INFO - 🎉 应用启动完成
2025-05-29 08:48:35,689 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 08:49:05,765 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 08:54:59,948 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 08:55:42,163 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:55:42,183 - main - INFO - ✅ 主密码验证成功
2025-05-29 08:55:42,185 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:55:42,185 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:55:42,185 - main - INFO - ✅ 数据库初始化完成
2025-05-29 08:55:42,185 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:55:42,185 - main - INFO - 🎉 应用启动完成
2025-05-29 09:04:25,605 - app.core.database - ERROR - 数据库会话错误: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 09:04:25,606 - main - ERROR - 未处理的异常: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlite3.OperationalError: cannot commit - no transaction is active

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 264, in app
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 563, in solve_dependencies
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 592, in solve_dependencies
    solved = await call(**sub_values)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/app/api/auth.py", line 106, in get_current_user
    db.commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1969, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1263, in commit
    trans.commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2619, in commit
    self._do_commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2724, in _do_commit
    self._connection_commit_impl()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2695, in _connection_commit_impl
    self.connection._commit_impl()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1138, in _commit_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 09:51:02,169 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 09:51:02,173 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 09:51:08,420 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 10:37:14,286 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 10:37:14,289 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 10:41:05,574 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:06,151 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:06,174 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:06,175 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:06,175 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:06,175 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:06,175 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:06,175 - main - INFO - 🎉 应用启动完成
2025-05-29 10:41:19,715 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:20,135 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:20,155 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:20,157 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:20,157 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:20,157 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:20,158 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:20,158 - main - INFO - 🎉 应用启动完成
2025-05-29 10:41:35,817 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:36,304 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:36,327 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:36,329 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:36,329 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:36,329 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:36,329 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:36,329 - main - INFO - 🎉 应用启动完成
2025-05-29 10:47:31,199 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 10:53:51,603 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:53:51,624 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:53:51,625 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:53:51,625 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:53:51,625 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:53:51,625 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:53:51,625 - main - INFO - 🎉 应用启动完成
2025-05-29 10:53:59,745 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:05:02,954 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:05:30,464 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:07:18,115 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:08:29,566 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:08:30,107 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:08:30,129 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:08:30,130 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:08:30,130 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:08:30,130 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:08:30,130 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:08:30,130 - main - INFO - 🎉 应用启动完成
2025-05-29 11:08:58,960 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:09:47,533 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,368 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,368 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,369 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,373 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,373 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,373 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:13:05,825 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:13:06,016 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:14:37,430 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:14:37,432 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:15:06,474 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:16:08,905 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:16:23,336 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:17:12,105 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,560 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,561 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,561 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,566 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,566 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,566 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:28:09,117 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:28:09,621 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:28:09,643 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:28:09,645 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:28:09,645 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:28:09,645 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:28:09,645 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:28:09,645 - main - INFO - 🎉 应用启动完成
2025-05-29 11:28:29,138 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:28:29,531 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:28:29,550 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:28:29,552 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:28:29,552 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:28:29,552 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:28:29,552 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:28:29,552 - main - INFO - 🎉 应用启动完成
2025-05-29 11:28:42,683 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:28:43,135 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:28:43,155 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:28:43,156 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:28:43,156 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:28:43,156 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:28:43,157 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:28:43,157 - main - INFO - 🎉 应用启动完成
2025-05-29 11:29:28,648 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:28,649 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,632 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,642 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,643 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,629 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,630 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,641 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,642 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,639 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,641 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,642 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,656 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,658 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,627 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,628 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,628 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,643 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,662 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,092 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,092 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,093 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,098 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,098 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,098 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:59,110 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:33:10,675 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:33:10,676 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:33:16,475 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:35:20,287 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:20,779 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:20,803 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:20,805 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:20,805 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:20,805 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:20,805 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:20,805 - main - INFO - 🎉 应用启动完成
2025-05-29 11:35:32,622 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:33,130 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:33,153 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:33,155 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:33,155 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:33,155 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:33,155 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:33,155 - main - INFO - 🎉 应用启动完成
2025-05-29 11:35:50,126 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:50,494 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:50,516 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:50,518 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:50,518 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:50,518 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:50,518 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:50,518 - main - INFO - 🎉 应用启动完成
2025-05-29 11:36:08,597 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:08,958 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:08,978 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:08,979 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:08,979 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:08,979 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:08,980 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:08,980 - main - INFO - 🎉 应用启动完成
2025-05-29 11:36:24,327 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:24,798 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:24,820 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:24,822 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:24,822 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:24,822 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:24,822 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:24,822 - main - INFO - 🎉 应用启动完成
2025-05-29 11:36:59,043 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:59,461 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:59,483 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:59,484 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:59,484 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:59,484 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:59,485 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:59,485 - main - INFO - 🎉 应用启动完成
2025-05-29 11:37:13,654 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,419 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,419 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,419 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,426 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,426 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,427 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,163 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,164 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,165 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,165 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:35,034 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:36,492 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:36,493 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:39:53,249 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:39:54,135 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:39:54,157 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:39:54,159 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:39:54,159 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:39:54,159 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:39:54,159 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:39:54,159 - main - INFO - 🎉 应用启动完成
2025-05-29 11:40:04,051 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:40:14,214 - app.core.database - ERROR - 数据库会话错误: cannot access local variable 'requests' where it is not associated with a value
2025-05-29 11:40:14,214 - main - ERROR - 未处理的异常: cannot access local variable 'requests' where it is not associated with a value
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/app/api/telegram_settings.py", line 452, in verify_bot_token
    import requests
ModuleNotFoundError: No module named 'requests'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 274, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/app/api/telegram_settings.py", line 491, in verify_bot_token
    except requests.RequestException as e:
           ^^^^^^^^
UnboundLocalError: cannot access local variable 'requests' where it is not associated with a value
2025-05-29 11:40:59,478 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:40:59,481 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,443 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,443 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,443 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,446 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,446 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,446 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:42:58,919 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:43:16,844 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:32:15,209 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:32:16,200 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:32:16,221 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:32:16,222 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:32:16,222 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:32:16,222 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:32:16,222 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:32:16,222 - main - INFO - 🎉 应用启动完成
2025-05-29 13:39:59,688 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:40:08,138 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:40:08,161 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:40:08,163 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:40:08,163 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:40:08,163 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:40:08,163 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:40:08,163 - main - INFO - 🎉 应用启动完成
2025-05-29 13:40:19,692 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:40:20,437 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:40:20,499 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:40:20,503 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:40:20,503 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:40:20,503 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:40:20,503 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:40:20,503 - main - INFO - 🎉 应用启动完成
2025-05-29 13:40:53,744 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:40:54,489 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:40:54,518 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:40:54,519 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:40:54,519 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:40:54,519 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:40:54,519 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:40:54,519 - main - INFO - 🎉 应用启动完成
2025-05-29 13:45:47,960 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:47:57,676 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:47:57,678 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:48:15,815 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:48:15,825 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:48:23,490 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:48:23,529 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:48:23,530 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:48:23,531 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:48:23,531 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:48:23,531 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:48:23,531 - main - INFO - 🎉 应用启动完成
2025-05-29 13:48:27,675 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:48:27,683 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:49:23,423 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:49:24,208 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:49:24,230 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:49:24,231 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:49:24,231 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:49:24,231 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:49:24,232 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:49:24,232 - main - INFO - 🎉 应用启动完成
2025-05-29 13:49:35,554 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:49:35,564 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:51:44,987 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:51:45,847 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:51:45,872 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:51:45,875 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:51:45,875 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:51:45,875 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:51:45,876 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:51:45,876 - main - INFO - 🎉 应用启动完成
2025-05-29 13:51:56,682 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:51:57,552 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:51:57,587 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:51:57,588 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:51:57,588 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:51:57,588 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:51:57,588 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:51:57,588 - main - INFO - 🎉 应用启动完成
2025-05-29 13:52:17,840 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:52:17,842 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:53:24,560 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:54:22,816 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:54:23,906 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:54:23,939 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:54:23,940 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:54:23,941 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:54:23,941 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:54:23,941 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:54:23,941 - main - INFO - 🎉 应用启动完成
2025-05-29 13:54:29,201 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:54:29,207 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:54:52,834 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:54:52,836 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:54:54,685 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 13:54:59,996 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:55:00,006 - app.core.database - ERROR - 数据库会话错误: [{'type': 'int_parsing', 'loc': ('path', 'strategy_id'), 'msg': 'Input should be a valid integer, unable to parse string as an integer', 'input': 'types', 'url': 'https://errors.pydantic.dev/2.11/v/int_parsing'}]
2025-05-29 13:56:17,026 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:56:17,848 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:56:17,874 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:56:17,875 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:56:17,875 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:56:17,875 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:56:17,875 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:56:17,875 - main - INFO - 🎉 应用启动完成
2025-05-29 13:56:40,816 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:56:41,478 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:56:41,515 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:56:41,517 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:56:41,517 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:56:41,517 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:56:41,517 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:56:41,517 - main - INFO - 🎉 应用启动完成
2025-05-29 13:56:52,075 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:57:51,412 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:58:20,525 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:58:20,562 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:58:20,564 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:58:20,564 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:58:20,564 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:58:20,564 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:58:20,564 - main - INFO - 🎉 应用启动完成
2025-05-29 13:59:42,343 - app.services.strategy_service - ERROR - 回测失败: 策略参数验证失败: 最大可能投资(6300.00)超过资金的80%
2025-05-29 13:59:42,343 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:03,425 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:03,426 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:03,426 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:03,429 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:03,429 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:03,429 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:05,123 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:05,129 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:05,129 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:05,129 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:05,131 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:02:05,131 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:23:33,690 - app.services.strategy_service - ERROR - 回测失败: 策略参数验证失败: 总投资金额(2000)不能超过初始资金(1000.0)
2025-05-29 14:23:33,691 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:23:42,034 - app.services.strategy_service - INFO - 回测完成: grid - BTCUSDT, 总收益: 0.00%
2025-05-29 14:23:42,041 - app.core.database - ERROR - 数据库会话错误: Out of range float values are not JSON compliant
2025-05-29 14:23:42,041 - main - ERROR - 未处理的异常: Out of range float values are not JSON compliant
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 303, in app
    response = actual_response_class(content, **response_args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
ValueError: Out of range float values are not JSON compliant
2025-05-29 14:23:45,453 - app.services.strategy_service - INFO - 回测完成: grid - BTCUSDT, 总收益: 0.00%
2025-05-29 14:23:45,463 - app.core.database - ERROR - 数据库会话错误: Out of range float values are not JSON compliant
2025-05-29 14:23:45,463 - main - ERROR - 未处理的异常: Out of range float values are not JSON compliant
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 303, in app
    response = actual_response_class(content, **response_args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
ValueError: Out of range float values are not JSON compliant
2025-05-29 14:23:46,532 - app.services.strategy_service - INFO - 回测完成: grid - BTCUSDT, 总收益: 0.00%
2025-05-29 14:23:46,542 - app.core.database - ERROR - 数据库会话错误: Out of range float values are not JSON compliant
2025-05-29 14:23:46,542 - main - ERROR - 未处理的异常: Out of range float values are not JSON compliant
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 303, in app
    response = actual_response_class(content, **response_args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
ValueError: Out of range float values are not JSON compliant
2025-05-29 14:24:09,506 - app.services.strategy_service - INFO - 回测完成: grid - BTCUSDT, 总收益: 0.00%
2025-05-29 14:24:09,513 - app.core.database - ERROR - 数据库会话错误: Out of range float values are not JSON compliant
2025-05-29 14:24:09,513 - main - ERROR - 未处理的异常: Out of range float values are not JSON compliant
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 303, in app
    response = actual_response_class(content, **response_args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 196, in __init__
    super().__init__(content, status_code, headers, media_type, background)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 55, in __init__
    self.body = self.render(content)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/responses.py", line 199, in render
    return json.dumps(
           ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
          ^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/json/encoder.py", line 258, in iterencode
    return _iterencode(o, 0)
           ^^^^^^^^^^^^^^^^^
ValueError: Out of range float values are not JSON compliant
2025-05-29 14:25:00,443 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:25:01,599 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:25:01,625 - main - INFO - ✅ 主密码验证成功
2025-05-29 14:25:01,627 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:25:01,627 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:25:01,627 - main - INFO - ✅ 数据库初始化完成
2025-05-29 14:25:01,627 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:25:01,627 - main - INFO - 🎉 应用启动完成
2025-05-29 14:25:34,046 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:25:34,838 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:25:34,874 - main - INFO - ✅ 主密码验证成功
2025-05-29 14:25:34,875 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:25:34,875 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:25:34,875 - main - INFO - ✅ 数据库初始化完成
2025-05-29 14:25:34,876 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:25:34,876 - main - INFO - 🎉 应用启动完成
2025-05-29 14:25:50,432 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:25:51,260 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:25:51,304 - main - INFO - ✅ 主密码验证成功
2025-05-29 14:25:51,306 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:25:51,306 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:25:51,306 - main - INFO - ✅ 数据库初始化完成
2025-05-29 14:25:51,307 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:25:51,307 - main - INFO - 🎉 应用启动完成
2025-05-29 14:26:11,708 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:26:12,483 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:26:12,530 - main - INFO - ✅ 主密码验证成功
2025-05-29 14:26:12,532 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:26:12,532 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:26:12,532 - main - INFO - ✅ 数据库初始化完成
2025-05-29 14:26:12,533 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:26:12,533 - main - INFO - 🎉 应用启动完成
2025-05-29 14:26:19,702 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:26:20,412 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:26:20,442 - main - INFO - ✅ 主密码验证成功
2025-05-29 14:26:20,444 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:26:20,444 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:26:20,444 - main - INFO - ✅ 数据库初始化完成
2025-05-29 14:26:20,444 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:26:20,444 - main - INFO - 🎉 应用启动完成
2025-05-29 14:27:40,509 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:27:40,533 - main - INFO - ✅ 主密码验证成功
2025-05-29 14:27:40,534 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:27:40,534 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:27:40,534 - main - INFO - ✅ 数据库初始化完成
2025-05-29 14:27:40,535 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:27:40,535 - main - INFO - 🎉 应用启动完成
2025-05-29 14:27:44,545 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:27:44,546 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:27:44,549 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:27:44,551 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:27:47,099 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:27:47,101 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 14:27:48,682 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 14:28:11,459 - app.services.strategy_service - INFO - 回测完成: grid - BTCUSDT, 总收益: 0.00%
2025-05-29 14:28:36,723 - app.core.database - ERROR - 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}]
2025-05-29 15:19:41,553 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:41,557 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:41,561 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:41,574 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:52,881 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:52,892 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:57,489 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:57,491 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:58,338 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:19:58,340 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:20:20,231 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:20:20,242 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:20:36,610 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:20:36,611 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:21:17,496 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:21:17,500 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:24:23,472 - app.core.database - ERROR - 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}]
2025-05-29 15:25:24,322 - app.core.database - ERROR - 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}]
2025-05-29 15:25:42,513 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 15:25:42,542 - main - INFO - ✅ 主密码验证成功
2025-05-29 15:25:42,546 - app.core.database - INFO - 数据库表创建成功
2025-05-29 15:25:42,546 - app.core.database - INFO - 数据库初始化完成
2025-05-29 15:25:42,546 - main - INFO - ✅ 数据库初始化完成
2025-05-29 15:25:42,546 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 15:25:42,546 - main - INFO - 🎉 应用启动完成
2025-05-29 15:25:45,628 - app.core.database - ERROR - 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.11/v/missing'}]
2025-05-29 15:26:37,934 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:26:37,938 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:26:38,349 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:26:38,350 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 15:26:48,211 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 15:26:49,578 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 15:26:49,609 - main - INFO - ✅ 主密码验证成功
2025-05-29 15:26:49,611 - app.core.database - INFO - 数据库表创建成功
2025-05-29 15:26:49,611 - app.core.database - INFO - 数据库初始化完成
2025-05-29 15:26:49,611 - main - INFO - ✅ 数据库初始化完成
2025-05-29 15:26:49,611 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 15:26:49,611 - main - INFO - 🎉 应用启动完成
