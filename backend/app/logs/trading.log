2025-05-29 08:48:27,373 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:48:27,399 - main - INFO - ✅ 主密码验证成功
2025-05-29 08:48:27,416 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:48:27,416 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:48:27,416 - main - INFO - ✅ 数据库初始化完成
2025-05-29 08:48:27,416 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:48:27,416 - main - INFO - 🎉 应用启动完成
2025-05-29 08:48:35,689 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 08:49:05,765 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 08:54:59,948 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 08:55:42,163 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:55:42,183 - main - INFO - ✅ 主密码验证成功
2025-05-29 08:55:42,185 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:55:42,185 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:55:42,185 - main - INFO - ✅ 数据库初始化完成
2025-05-29 08:55:42,185 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:55:42,185 - main - INFO - 🎉 应用启动完成
2025-05-29 09:04:25,605 - app.core.database - ERROR - 数据库会话错误: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 09:04:25,606 - main - ERROR - 未处理的异常: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlite3.OperationalError: cannot commit - no transaction is active

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 264, in app
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 563, in solve_dependencies
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 592, in solve_dependencies
    solved = await call(**sub_values)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/app/api/auth.py", line 106, in get_current_user
    db.commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1969, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1263, in commit
    trans.commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2619, in commit
    self._do_commit()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2724, in _do_commit
    self._connection_commit_impl()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2695, in _connection_commit_impl
    self.connection._commit_impl()
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1138, in _commit_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 09:51:02,169 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 09:51:02,173 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 09:51:08,420 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 10:37:14,286 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 10:37:14,289 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 10:41:05,574 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:06,151 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:06,174 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:06,175 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:06,175 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:06,175 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:06,175 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:06,175 - main - INFO - 🎉 应用启动完成
2025-05-29 10:41:19,715 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:20,135 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:20,155 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:20,157 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:20,157 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:20,157 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:20,158 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:20,158 - main - INFO - 🎉 应用启动完成
2025-05-29 10:41:35,817 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 10:41:36,304 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:41:36,327 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:41:36,329 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:41:36,329 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:41:36,329 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:41:36,329 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:41:36,329 - main - INFO - 🎉 应用启动完成
2025-05-29 10:47:31,199 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 10:53:51,603 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 10:53:51,624 - main - INFO - ✅ 主密码验证成功
2025-05-29 10:53:51,625 - app.core.database - INFO - 数据库表创建成功
2025-05-29 10:53:51,625 - app.core.database - INFO - 数据库初始化完成
2025-05-29 10:53:51,625 - main - INFO - ✅ 数据库初始化完成
2025-05-29 10:53:51,625 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 10:53:51,625 - main - INFO - 🎉 应用启动完成
2025-05-29 10:53:59,745 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:05:02,954 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:05:30,464 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:07:18,115 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:08:29,566 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:08:30,107 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:08:30,129 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:08:30,130 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:08:30,130 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:08:30,130 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:08:30,130 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:08:30,130 - main - INFO - 🎉 应用启动完成
2025-05-29 11:08:58,960 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:09:47,533 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,368 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,368 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,369 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,373 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,373 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:12:23,373 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:13:05,825 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:13:06,016 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:14:37,430 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:14:37,432 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:15:06,474 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:16:08,905 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:16:23,336 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:17:12,105 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,560 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,561 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,561 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,566 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,566 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:22:07,566 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:28:09,117 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:28:09,621 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:28:09,643 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:28:09,645 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:28:09,645 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:28:09,645 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:28:09,645 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:28:09,645 - main - INFO - 🎉 应用启动完成
2025-05-29 11:28:29,138 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:28:29,531 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:28:29,550 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:28:29,552 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:28:29,552 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:28:29,552 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:28:29,552 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:28:29,552 - main - INFO - 🎉 应用启动完成
2025-05-29 11:28:42,683 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:28:43,135 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:28:43,155 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:28:43,156 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:28:43,156 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:28:43,156 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:28:43,157 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:28:43,157 - main - INFO - 🎉 应用启动完成
2025-05-29 11:29:28,648 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:28,649 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,632 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,642 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:40,643 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,629 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,630 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,631 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,641 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:29:58,642 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,639 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,641 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,642 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,656 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:17,658 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,627 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,628 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,628 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,643 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:30:48,662 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,092 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,092 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,093 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,098 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,098 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:53,098 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:31:59,110 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:33:10,675 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:33:10,676 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:33:16,475 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:35:20,287 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:20,779 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:20,803 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:20,805 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:20,805 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:20,805 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:20,805 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:20,805 - main - INFO - 🎉 应用启动完成
2025-05-29 11:35:32,622 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:33,130 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:33,153 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:33,155 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:33,155 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:33,155 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:33,155 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:33,155 - main - INFO - 🎉 应用启动完成
2025-05-29 11:35:50,126 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:50,494 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:50,516 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:50,518 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:50,518 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:50,518 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:50,518 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:50,518 - main - INFO - 🎉 应用启动完成
2025-05-29 11:36:08,597 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:08,958 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:08,978 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:08,979 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:08,979 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:08,979 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:08,980 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:08,980 - main - INFO - 🎉 应用启动完成
2025-05-29 11:36:24,327 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:24,798 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:24,820 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:24,822 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:24,822 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:24,822 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:24,822 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:24,822 - main - INFO - 🎉 应用启动完成
2025-05-29 11:36:59,043 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:59,461 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:59,483 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:59,484 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:59,484 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:59,484 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:59,485 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:59,485 - main - INFO - 🎉 应用启动完成
2025-05-29 11:37:13,654 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,419 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,419 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,419 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,426 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,426 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:27,427 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,163 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,164 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,165 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:34,165 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:35,034 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:36,492 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:38:36,493 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:39:53,249 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:39:54,135 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:39:54,157 - main - INFO - ✅ 主密码验证成功
2025-05-29 11:39:54,159 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:39:54,159 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:39:54,159 - main - INFO - ✅ 数据库初始化完成
2025-05-29 11:39:54,159 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:39:54,159 - main - INFO - 🎉 应用启动完成
2025-05-29 11:40:04,051 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 11:40:14,214 - app.core.database - ERROR - 数据库会话错误: cannot access local variable 'requests' where it is not associated with a value
2025-05-29 11:40:14,214 - main - ERROR - 未处理的异常: cannot access local variable 'requests' where it is not associated with a value
Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/app/api/telegram_settings.py", line 452, in verify_bot_token
    import requests
ModuleNotFoundError: No module named 'requests'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 274, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/sofeware/lazy/backend/app/api/telegram_settings.py", line 491, in verify_bot_token
    except requests.RequestException as e:
           ^^^^^^^^
UnboundLocalError: cannot access local variable 'requests' where it is not associated with a value
2025-05-29 11:40:59,478 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:40:59,481 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,443 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,443 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,443 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,446 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,446 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:41:38,446 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:42:58,919 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 11:43:16,844 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 13:32:15,209 - main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:32:16,200 - main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:32:16,221 - main - INFO - ✅ 主密码验证成功
2025-05-29 13:32:16,222 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:32:16,222 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:32:16,222 - main - INFO - ✅ 数据库初始化完成
2025-05-29 13:32:16,222 - main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:32:16,222 - main - INFO - 🎉 应用启动完成
