"""
OKX交易所实现
"""
import asyncio
import base64
import hashlib
import hmac
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp
import json
import logging

from .base import (
    BaseExchange, OrderType, OrderSide, OrderStatus, MarketType,
    TickerData, OrderBookData, KlineData, OrderResult, TradeResult,
    BalanceInfo, PositionInfo
)

logger = logging.getLogger(__name__)

class OKXExchange(BaseExchange):
    """OKX交易所实现"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: Optional[str] = None, 
                 testnet: bool = False):
        super().__init__(api_key, secret_key, passphrase, testnet)
        
        if testnet:
            self.base_url = "https://www.okx.com"  # OKX没有公开的测试网
        else:
            self.base_url = "https://www.okx.com"
        
        if not passphrase:
            raise ValueError("OKX需要passphrase参数")
        
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'Content-Type': 'application/json'}
            )
        return self.session
    
    def _generate_signature(self, timestamp: str, method: str, request_path: str, body: str = '') -> str:
        """生成签名"""
        message = timestamp + method.upper() + request_path + body
        signature = base64.b64encode(
            hmac.new(
                self.secret_key.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        return signature
    
    async def _request(self, method: str, endpoint: str, params: Optional[Dict] = None,
                      signed: bool = False) -> Dict[str, Any]:
        """发送HTTP请求"""
        session = await self._get_session()
        url = f"{self.base_url}{endpoint}"
        
        headers = {}
        body = ''
        
        if signed:
            timestamp = str(time.time())
            
            if method.upper() == 'GET' and params:
                query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
                url += f"?{query_string}"
                request_path = endpoint + f"?{query_string}"
            else:
                request_path = endpoint
                if params:
                    body = json.dumps(params)
            
            signature = self._generate_signature(timestamp, method, request_path, body)
            
            headers.update({
                'OK-ACCESS-KEY': self.api_key,
                'OK-ACCESS-SIGN': signature,
                'OK-ACCESS-TIMESTAMP': timestamp,
                'OK-ACCESS-PASSPHRASE': self.passphrase
            })
        
        try:
            if method.upper() == 'GET':
                if not signed and params:
                    async with session.get(url, params=params, headers=headers) as response:
                        data = await response.json()
                else:
                    async with session.get(url, headers=headers) as response:
                        data = await response.json()
            elif method.upper() == 'POST':
                async with session.post(url, data=body, headers=headers) as response:
                    data = await response.json()
            elif method.upper() == 'DELETE':
                async with session.delete(url, data=body, headers=headers) as response:
                    data = await response.json()
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            if data.get('code') != '0':
                logger.error(f"OKX API错误: {data}")
                raise Exception(f"OKX API错误: {data.get('msg', 'Unknown error')}")
            
            return data
            
        except aiohttp.ClientError as e:
            logger.error(f"OKX网络请求失败: {e}")
            raise Exception(f"网络请求失败: {e}")
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            await self._request('GET', '/api/v5/public/time')
            return True
        except Exception as e:
            logger.error(f"OKX连接测试失败: {e}")
            return False
    
    async def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        return await self._request('GET', '/api/v5/account/config', signed=True)
    
    async def get_balances(self) -> List[BalanceInfo]:
        """获取余额信息"""
        data = await self._request('GET', '/api/v5/account/balance', signed=True)
        balances = []
        
        for account in data.get('data', []):
            for balance in account.get('details', []):
                free = float(balance['availBal'])
                locked = float(balance['frozenBal'])
                total = free + locked
                
                if total > 0:  # 只返回有余额的币种
                    balances.append(BalanceInfo(
                        currency=balance['ccy'],
                        free=free,
                        locked=locked,
                        total=total
                    ))
        
        return balances
    
    async def get_ticker(self, symbol: str) -> TickerData:
        """获取单个交易对行情"""
        symbol = self.normalize_symbol(symbol)
        # OKX使用-分隔，如BTC-USDT
        # 简单的转换逻辑：BTCUSDT -> BTC-USDT
        if 'USDT' in symbol:
            okx_symbol = symbol.replace('USDT', '-USDT')
        elif 'BTC' in symbol and symbol != 'BTC':
            okx_symbol = symbol.replace('BTC', '-BTC')
        elif 'ETH' in symbol and symbol != 'ETH':
            okx_symbol = symbol.replace('ETH', '-ETH')
        else:
            # 默认假设最后4位是计价币种
            if len(symbol) > 4:
                okx_symbol = symbol[:-4] + '-' + symbol[-4:]
            else:
                okx_symbol = symbol
        
        data = await self._request('GET', '/api/v5/market/ticker', {'instId': okx_symbol})
        
        ticker = data['data'][0]
        return TickerData(
            symbol=symbol,
            price=float(ticker['last']),
            change_24h=float(ticker['open24h']) - float(ticker['last']),
            volume_24h=float(ticker['vol24h']),
            high_24h=float(ticker['high24h']),
            low_24h=float(ticker['low24h']),
            timestamp=datetime.utcnow()
        )
    
    async def get_all_tickers(self) -> List[TickerData]:
        """获取所有交易对行情"""
        data = await self._request('GET', '/api/v5/market/tickers', {'instType': 'SPOT'})
        tickers = []
        
        for ticker in data.get('data', []):
            symbol = ticker['instId'].replace('-', '')  # 转换为标准格式
            tickers.append(TickerData(
                symbol=symbol,
                price=float(ticker['last']),
                change_24h=float(ticker['open24h']) - float(ticker['last']),
                volume_24h=float(ticker['vol24h']),
                high_24h=float(ticker['high24h']),
                low_24h=float(ticker['low24h']),
                timestamp=datetime.utcnow()
            ))
        
        return tickers
    
    async def get_orderbook(self, symbol: str, limit: int = 20) -> OrderBookData:
        """获取订单簿"""
        symbol = self.normalize_symbol(symbol)
        okx_symbol = symbol[:-4] + '-' + symbol[-4:]  # 转换为OKX格式
        
        data = await self._request('GET', '/api/v5/market/books', {
            'instId': okx_symbol,
            'sz': str(limit)
        })
        
        book = data['data'][0]
        bids = [[float(bid[0]), float(bid[1])] for bid in book['bids']]
        asks = [[float(ask[0]), float(ask[1])] for ask in book['asks']]
        
        return OrderBookData(
            symbol=symbol,
            bids=bids,
            asks=asks,
            timestamp=datetime.utcnow()
        )
    
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> List[KlineData]:
        """获取K线数据"""
        symbol = self.normalize_symbol(symbol)
        okx_symbol = symbol[:-4] + '-' + symbol[-4:]  # 转换为OKX格式
        
        # 转换时间间隔格式
        interval_map = {
            '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
            '1h': '1H', '4h': '4H', '1d': '1D', '1w': '1W'
        }
        okx_interval = interval_map.get(interval, '1H')
        
        data = await self._request('GET', '/api/v5/market/candles', {
            'instId': okx_symbol,
            'bar': okx_interval,
            'limit': str(limit)
        })
        
        klines = []
        for kline in data.get('data', []):
            klines.append(KlineData(
                symbol=symbol,
                interval=interval,
                open_time=int(kline[0]),
                close_time=int(kline[0]) + 60000,  # 假设1分钟间隔
                open_price=float(kline[1]),
                high_price=float(kline[2]),
                low_price=float(kline[3]),
                close_price=float(kline[4]),
                volume=float(kline[5])
            ))
        
        return klines
    
    def _convert_order_type(self, order_type: OrderType) -> str:
        """转换订单类型"""
        type_map = {
            OrderType.MARKET: 'market',
            OrderType.LIMIT: 'limit',
            OrderType.STOP: 'conditional',
            OrderType.STOP_LIMIT: 'conditional'
        }
        return type_map[order_type]
    
    def _convert_order_side(self, side: OrderSide) -> str:
        """转换订单方向"""
        return side.value
    
    def _parse_order_status(self, status: str) -> OrderStatus:
        """解析订单状态"""
        status_map = {
            'live': OrderStatus.OPEN,
            'partially_filled': OrderStatus.PARTIALLY_FILLED,
            'filled': OrderStatus.FILLED,
            'canceled': OrderStatus.CANCELLED,
            'rejected': OrderStatus.REJECTED
        }
        return status_map.get(status, OrderStatus.PENDING)

    async def create_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                          amount: float, price: Optional[float] = None,
                          stop_price: Optional[float] = None,
                          market_type: MarketType = MarketType.SPOT,
                          **kwargs) -> OrderResult:
        """创建订单"""
        symbol = self.normalize_symbol(symbol)
        okx_symbol = symbol[:-4] + '-' + symbol[-4:]  # 转换为OKX格式

        params = {
            'instId': okx_symbol,
            'tdMode': 'cash',  # 现金模式
            'side': self._convert_order_side(side),
            'ordType': self._convert_order_type(order_type),
            'sz': str(amount)
        }

        if order_type == OrderType.LIMIT:
            if price is None:
                raise ValueError("限价单必须指定价格")
            params['px'] = str(price)

        if market_type == MarketType.FUTURES:
            params['tdMode'] = 'isolated'  # 逐仓模式

        data = await self._request('POST', '/api/v5/trade/order', params, signed=True)

        order_data = data['data'][0]
        return OrderResult(
            order_id=order_data['ordId'],
            symbol=symbol,
            side=side,
            order_type=order_type,
            amount=amount,
            price=price,
            status=self._parse_order_status(order_data.get('state', 'live')),
            filled_amount=0,
            timestamp=datetime.utcnow()
        )

    async def cancel_order(self, symbol: str, order_id: str,
                          market_type: MarketType = MarketType.SPOT) -> bool:
        """取消订单"""
        symbol = self.normalize_symbol(symbol)
        okx_symbol = symbol[:-4] + '-' + symbol[-4:]  # 转换为OKX格式

        params = {
            'instId': okx_symbol,
            'ordId': order_id
        }

        try:
            await self._request('POST', '/api/v5/trade/cancel-order', params, signed=True)
            return True
        except Exception as e:
            logger.error(f"取消订单失败: {e}")
            return False

    async def get_order(self, symbol: str, order_id: str,
                       market_type: MarketType = MarketType.SPOT) -> OrderResult:
        """获取订单信息"""
        symbol = self.normalize_symbol(symbol)
        okx_symbol = symbol[:-4] + '-' + symbol[-4:]  # 转换为OKX格式

        params = {
            'instId': okx_symbol,
            'ordId': order_id
        }

        data = await self._request('GET', '/api/v5/trade/order', params, signed=True)

        order = data['data'][0]
        return OrderResult(
            order_id=order['ordId'],
            symbol=symbol,
            side=OrderSide(order['side']),
            order_type=OrderType.LIMIT if order['ordType'] == 'limit' else OrderType.MARKET,
            amount=float(order['sz']),
            price=float(order['px']) if order.get('px') else None,
            status=self._parse_order_status(order['state']),
            filled_amount=float(order.get('fillSz', 0)),
            avg_price=float(order.get('avgPx', 0)) if order.get('avgPx') else None,
            timestamp=datetime.fromtimestamp(int(order['cTime']) / 1000)
        )

    async def get_open_orders(self, symbol: Optional[str] = None,
                             market_type: MarketType = MarketType.SPOT) -> List[OrderResult]:
        """获取活跃订单"""
        params = {}
        if symbol:
            symbol = self.normalize_symbol(symbol)
            okx_symbol = symbol[:-4] + '-' + symbol[-4:]
            params['instId'] = okx_symbol

        data = await self._request('GET', '/api/v5/trade/orders-pending', params, signed=True)

        orders = []
        for order in data.get('data', []):
            symbol_std = order['instId'].replace('-', '')  # 转换回标准格式
            orders.append(OrderResult(
                order_id=order['ordId'],
                symbol=symbol_std,
                side=OrderSide(order['side']),
                order_type=OrderType.LIMIT if order['ordType'] == 'limit' else OrderType.MARKET,
                amount=float(order['sz']),
                price=float(order['px']) if order.get('px') else None,
                status=self._parse_order_status(order['state']),
                filled_amount=float(order.get('fillSz', 0)),
                timestamp=datetime.fromtimestamp(int(order['cTime']) / 1000)
            ))

        return orders

    async def get_order_history(self, symbol: Optional[str] = None, limit: int = 100,
                               market_type: MarketType = MarketType.SPOT) -> List[OrderResult]:
        """获取历史订单"""
        params = {'limit': str(limit)}
        if symbol:
            symbol = self.normalize_symbol(symbol)
            okx_symbol = symbol[:-4] + '-' + symbol[-4:]
            params['instId'] = okx_symbol

        data = await self._request('GET', '/api/v5/trade/orders-history', params, signed=True)

        orders = []
        for order in data.get('data', []):
            symbol_std = order['instId'].replace('-', '')  # 转换回标准格式
            orders.append(OrderResult(
                order_id=order['ordId'],
                symbol=symbol_std,
                side=OrderSide(order['side']),
                order_type=OrderType.LIMIT if order['ordType'] == 'limit' else OrderType.MARKET,
                amount=float(order['sz']),
                price=float(order['px']) if order.get('px') else None,
                status=self._parse_order_status(order['state']),
                filled_amount=float(order.get('fillSz', 0)),
                timestamp=datetime.fromtimestamp(int(order['cTime']) / 1000)
            ))

        return orders

    async def get_trade_history(self, symbol: Optional[str] = None, limit: int = 100,
                               market_type: MarketType = MarketType.SPOT) -> List[TradeResult]:
        """获取成交历史"""
        params = {'limit': str(limit)}
        if symbol:
            symbol = self.normalize_symbol(symbol)
            okx_symbol = symbol[:-4] + '-' + symbol[-4:]
            params['instId'] = okx_symbol

        data = await self._request('GET', '/api/v5/trade/fills', params, signed=True)

        trades = []
        for trade in data.get('data', []):
            symbol_std = trade['instId'].replace('-', '')  # 转换回标准格式
            trades.append(TradeResult(
                trade_id=trade['tradeId'],
                order_id=trade['ordId'],
                symbol=symbol_std,
                side=OrderSide(trade['side']),
                amount=float(trade['fillSz']),
                price=float(trade['fillPx']),
                fee=float(trade['fee']),
                fee_currency=trade['feeCcy'],
                timestamp=datetime.fromtimestamp(int(trade['ts']) / 1000)
            ))

        return trades

    async def get_positions(self, symbol: Optional[str] = None) -> List[PositionInfo]:
        """获取持仓信息 (期货)"""
        params = {}
        if symbol:
            symbol = self.normalize_symbol(symbol)
            okx_symbol = symbol[:-4] + '-' + symbol[-4:] + '-SWAP'  # 永续合约格式
            params['instId'] = okx_symbol

        data = await self._request('GET', '/api/v5/account/positions', params, signed=True)

        positions = []
        for pos in data.get('data', []):
            size = float(pos['pos'])
            if size != 0:  # 只返回有持仓的
                positions.append(PositionInfo(
                    symbol=pos['instId'].replace('-SWAP', '').replace('-', ''),
                    side='long' if size > 0 else 'short',
                    size=abs(size),
                    entry_price=float(pos['avgPx']),
                    mark_price=float(pos['markPx']),
                    unrealized_pnl=float(pos['upl']),
                    margin=float(pos['margin']),
                    leverage=float(pos['lever']),
                    liquidation_price=float(pos['liqPx']) if pos['liqPx'] else None
                ))

        return positions

    async def get_symbols(self, market_type: MarketType = MarketType.SPOT) -> List[Dict[str, Any]]:
        """获取交易对信息"""
        inst_type = 'SPOT' if market_type == MarketType.SPOT else 'SWAP'
        data = await self._request('GET', '/api/v5/public/instruments', {'instType': inst_type})

        symbols = []
        for symbol_info in data.get('data', []):
            if symbol_info['state'] == 'live':
                symbols.append({
                    'symbol': symbol_info['instId'].replace('-', ''),
                    'base_asset': symbol_info['baseCcy'],
                    'quote_asset': symbol_info['quoteCcy'],
                    'status': symbol_info['state'],
                    'min_qty': float(symbol_info['minSz']),
                    'max_qty': float(symbol_info.get('maxMktSz', '999999999')),
                    'step_size': float(symbol_info['lotSz']),
                    'tick_size': float(symbol_info['tickSz'])
                })

        return symbols

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
