"""
币安WebSocket实现
"""
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .websocket_base import ExchangeWebSocketBase

logger = logging.getLogger(__name__)

class BinanceWebSocket(ExchangeWebSocketBase):
    """币安WebSocket客户端"""
    
    def __init__(self, testnet: bool = False):
        super().__init__()
        self.testnet = testnet
        self._stream_names: List[str] = []
    
    @property
    def ws_url(self) -> str:
        """WebSocket连接URL"""
        if self.testnet:
            return "wss://testnet.binance.vision/ws-api/v3"
        return "wss://stream.binance.com:9443/ws"
    
    def _symbol_to_stream(self, symbol: str) -> str:
        """将交易对转换为流名称"""
        return f"{symbol.lower()}@ticker"
    
    async def _subscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成订阅消息"""
        streams = [self._symbol_to_stream(symbol) for symbol in symbols]
        self._stream_names.extend(streams)
        
        return {
            "method": "SUBSCRIBE",
            "params": streams,
            "id": 1
        }
    
    async def _unsubscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成取消订阅消息"""
        streams = [self._symbol_to_stream(symbol) for symbol in symbols]
        
        # 从已订阅流中移除
        for stream in streams:
            if stream in self._stream_names:
                self._stream_names.remove(stream)
        
        return {
            "method": "UNSUBSCRIBE",
            "params": streams,
            "id": 2
        }
    
    async def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        try:
            # 处理订阅确认消息
            if "result" in message:
                if message.get("result") is None:
                    logger.info("币安订阅确认")
                return None
            
            # 处理错误消息
            if "error" in message:
                logger.error(f"币安WebSocket错误: {message['error']}")
                return None
            
            # 处理行情数据
            if "stream" in message and "data" in message:
                stream = message["stream"]
                data = message["data"]
                
                # 解析ticker数据
                if "@ticker" in stream:
                    return await self._parse_ticker_data(data)
            
            # 处理直接的ticker数据（单流连接）
            elif "e" in message and message["e"] == "24hrTicker":
                return await self._parse_ticker_data(message)
            
            return None
            
        except Exception as e:
            logger.error(f"解析币安消息失败: {e}, 原始消息: {message}")
            return None
    
    async def _parse_ticker_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """解析ticker数据"""
        try:
            symbol = data["s"]
            price = float(data["c"])
            change_24h = float(data["P"])
            volume_24h = float(data["v"])
            high_24h = float(data["h"])
            low_24h = float(data["l"])
            
            return {
                "type": "ticker",
                "exchange": "binance",
                "symbol": symbol,
                "price": price,
                "change_24h_percent": change_24h,
                "volume_24h": volume_24h,
                "high_24h": high_24h,
                "low_24h": low_24h,
                "timestamp": datetime.utcnow().isoformat()
            }
        except (KeyError, ValueError) as e:
            logger.error(f"解析币安ticker数据失败: {e}")
            return None

class BinanceKlineWebSocket(ExchangeWebSocketBase):
    """币安K线WebSocket客户端"""
    
    def __init__(self, interval: str = "1m", testnet: bool = False):
        super().__init__()
        self.interval = interval
        self.testnet = testnet
    
    @property
    def ws_url(self) -> str:
        """WebSocket连接URL"""
        if self.testnet:
            return "wss://testnet.binance.vision/ws-api/v3"
        return "wss://stream.binance.com:9443/ws"
    
    def _symbol_to_stream(self, symbol: str) -> str:
        """将交易对转换为K线流名称"""
        return f"{symbol.lower()}@kline_{self.interval}"
    
    async def _subscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成订阅消息"""
        streams = [self._symbol_to_stream(symbol) for symbol in symbols]
        
        return {
            "method": "SUBSCRIBE",
            "params": streams,
            "id": 1
        }
    
    async def _unsubscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成取消订阅消息"""
        streams = [self._symbol_to_stream(symbol) for symbol in symbols]
        
        return {
            "method": "UNSUBSCRIBE",
            "params": streams,
            "id": 2
        }
    
    async def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        try:
            # 处理订阅确认消息
            if "result" in message:
                return None
            
            # 处理错误消息
            if "error" in message:
                logger.error(f"币安K线WebSocket错误: {message['error']}")
                return None
            
            # 处理K线数据
            if "stream" in message and "data" in message:
                stream = message["stream"]
                data = message["data"]
                
                if "@kline_" in stream:
                    return await self._parse_kline_data(data)
            
            # 处理直接的K线数据
            elif "e" in message and message["e"] == "kline":
                return await self._parse_kline_data(message)
            
            return None
            
        except Exception as e:
            logger.error(f"解析币安K线消息失败: {e}")
            return None
    
    async def _parse_kline_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """解析K线数据"""
        try:
            if "k" in data:
                kline = data["k"]
            else:
                kline = data
            
            symbol = kline["s"]
            open_time = int(kline["t"])
            close_time = int(kline["T"])
            open_price = float(kline["o"])
            high_price = float(kline["h"])
            low_price = float(kline["l"])
            close_price = float(kline["c"])
            volume = float(kline["v"])
            is_closed = kline.get("x", False)  # K线是否已关闭
            
            return {
                "type": "kline",
                "exchange": "binance",
                "symbol": symbol,
                "interval": self.interval,
                "open_time": open_time,
                "close_time": close_time,
                "open_price": open_price,
                "high_price": high_price,
                "low_price": low_price,
                "close_price": close_price,
                "volume": volume,
                "is_closed": is_closed,
                "timestamp": datetime.utcnow().isoformat()
            }
        except (KeyError, ValueError) as e:
            logger.error(f"解析币安K线数据失败: {e}")
            return None

class BinanceDepthWebSocket(ExchangeWebSocketBase):
    """币安深度WebSocket客户端"""
    
    def __init__(self, levels: str = "5", update_speed: str = "1000ms", testnet: bool = False):
        super().__init__()
        self.levels = levels  # 5, 10, 20
        self.update_speed = update_speed  # 1000ms, 100ms
        self.testnet = testnet
    
    @property
    def ws_url(self) -> str:
        """WebSocket连接URL"""
        if self.testnet:
            return "wss://testnet.binance.vision/ws-api/v3"
        return "wss://stream.binance.com:9443/ws"
    
    def _symbol_to_stream(self, symbol: str) -> str:
        """将交易对转换为深度流名称"""
        return f"{symbol.lower()}@depth{self.levels}@{self.update_speed}"
    
    async def _subscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成订阅消息"""
        streams = [self._symbol_to_stream(symbol) for symbol in symbols]
        
        return {
            "method": "SUBSCRIBE",
            "params": streams,
            "id": 1
        }
    
    async def _unsubscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成取消订阅消息"""
        streams = [self._symbol_to_stream(symbol) for symbol in symbols]
        
        return {
            "method": "UNSUBSCRIBE",
            "params": streams,
            "id": 2
        }
    
    async def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        try:
            # 处理订阅确认消息
            if "result" in message:
                return None
            
            # 处理错误消息
            if "error" in message:
                logger.error(f"币安深度WebSocket错误: {message['error']}")
                return None
            
            # 处理深度数据
            if "stream" in message and "data" in message:
                stream = message["stream"]
                data = message["data"]
                
                if "@depth" in stream:
                    return await self._parse_depth_data(data, stream)
            
            return None
            
        except Exception as e:
            logger.error(f"解析币安深度消息失败: {e}")
            return None
    
    async def _parse_depth_data(self, data: Dict[str, Any], stream: str) -> Dict[str, Any]:
        """解析深度数据"""
        try:
            # 从流名称中提取symbol
            symbol = stream.split("@")[0].upper()
            
            bids = [[float(bid[0]), float(bid[1])] for bid in data.get("bids", [])]
            asks = [[float(ask[0]), float(ask[1])] for ask in data.get("asks", [])]
            
            return {
                "type": "orderbook",
                "exchange": "binance",
                "symbol": symbol,
                "bids": bids,
                "asks": asks,
                "timestamp": datetime.utcnow().isoformat()
            }
        except (KeyError, ValueError) as e:
            logger.error(f"解析币安深度数据失败: {e}")
            return None
