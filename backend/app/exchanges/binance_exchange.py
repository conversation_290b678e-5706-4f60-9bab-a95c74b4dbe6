"""
币安交易所实现
"""
import asyncio
import hashlib
import hmac
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
import aiohttp
import json
import logging

from .base import (
    BaseExchange, OrderType, OrderSide, OrderStatus, MarketType,
    TickerData, OrderBookData, KlineData, OrderResult, TradeResult,
    BalanceInfo, PositionInfo
)

logger = logging.getLogger(__name__)

class BinanceExchange(BaseExchange):
    """币安交易所实现"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: Optional[str] = None, 
                 testnet: bool = False):
        super().__init__(api_key, secret_key, passphrase, testnet)
        
        if testnet:
            self.base_url = "https://testnet.binance.vision"
            self.futures_url = "https://testnet.binancefuture.com"
        else:
            self.base_url = "https://api.binance.com"
            self.futures_url = "https://fapi.binance.com"
        
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'X-MBX-APIKEY': self.api_key,
                    'Content-Type': 'application/json'
                }
            )
        return self.session
    
    def _generate_signature(self, query_string: str) -> str:
        """生成签名"""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    async def _request(self, method: str, endpoint: str, params: Optional[Dict] = None,
                      signed: bool = False, futures: bool = False) -> Dict[str, Any]:
        """发送HTTP请求"""
        session = await self._get_session()
        base_url = self.futures_url if futures else self.base_url
        url = f"{base_url}{endpoint}"
        
        if params is None:
            params = {}
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            params['signature'] = self._generate_signature(query_string)
        
        try:
            if method.upper() == 'GET':
                async with session.get(url, params=params) as response:
                    data = await response.json()
            elif method.upper() == 'POST':
                async with session.post(url, data=params) as response:
                    data = await response.json()
            elif method.upper() == 'DELETE':
                async with session.delete(url, params=params) as response:
                    data = await response.json()
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            if response.status != 200:
                logger.error(f"Binance API错误: {response.status} - {data}")
                raise Exception(f"Binance API错误: {data.get('msg', 'Unknown error')}")
            
            return data
            
        except aiohttp.ClientError as e:
            logger.error(f"Binance网络请求失败: {e}")
            raise Exception(f"网络请求失败: {e}")
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            await self._request('GET', '/api/v3/ping')
            return True
        except Exception as e:
            logger.error(f"Binance连接测试失败: {e}")
            return False
    
    async def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        return await self._request('GET', '/api/v3/account', signed=True)
    
    async def get_balances(self) -> List[BalanceInfo]:
        """获取余额信息"""
        account_info = await self.get_account_info()
        balances = []
        
        for balance in account_info.get('balances', []):
            free = float(balance['free'])
            locked = float(balance['locked'])
            total = free + locked
            
            if total > 0:  # 只返回有余额的币种
                balances.append(BalanceInfo(
                    currency=balance['asset'],
                    free=free,
                    locked=locked,
                    total=total
                ))
        
        return balances
    
    async def get_ticker(self, symbol: str) -> TickerData:
        """获取单个交易对行情"""
        symbol = self.normalize_symbol(symbol)
        data = await self._request('GET', '/api/v3/ticker/24hr', {'symbol': symbol})
        
        return TickerData(
            symbol=data['symbol'],
            price=float(data['lastPrice']),
            change_24h=float(data['priceChange']),
            volume_24h=float(data['volume']),
            high_24h=float(data['highPrice']),
            low_24h=float(data['lowPrice']),
            timestamp=datetime.utcnow()
        )
    
    async def get_all_tickers(self) -> List[TickerData]:
        """获取所有交易对行情"""
        data = await self._request('GET', '/api/v3/ticker/24hr')
        tickers = []
        
        for ticker in data:
            tickers.append(TickerData(
                symbol=ticker['symbol'],
                price=float(ticker['lastPrice']),
                change_24h=float(ticker['priceChange']),
                volume_24h=float(ticker['volume']),
                high_24h=float(ticker['highPrice']),
                low_24h=float(ticker['lowPrice']),
                timestamp=datetime.utcnow()
            ))
        
        return tickers
    
    async def get_orderbook(self, symbol: str, limit: int = 20) -> OrderBookData:
        """获取订单簿"""
        symbol = self.normalize_symbol(symbol)
        data = await self._request('GET', '/api/v3/depth', {
            'symbol': symbol,
            'limit': limit
        })
        
        bids = [[float(bid[0]), float(bid[1])] for bid in data['bids']]
        asks = [[float(ask[0]), float(ask[1])] for ask in data['asks']]
        
        return OrderBookData(
            symbol=symbol,
            bids=bids,
            asks=asks,
            timestamp=datetime.utcnow()
        )
    
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> List[KlineData]:
        """获取K线数据"""
        symbol = self.normalize_symbol(symbol)
        
        # 转换时间间隔格式
        interval_map = {
            '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
            '1h': '1h', '4h': '4h', '1d': '1d', '1w': '1w'
        }
        binance_interval = interval_map.get(interval, '1h')
        
        data = await self._request('GET', '/api/v3/klines', {
            'symbol': symbol,
            'interval': binance_interval,
            'limit': limit
        })
        
        klines = []
        for kline in data:
            klines.append(KlineData(
                symbol=symbol,
                interval=interval,
                open_time=int(kline[0]),
                close_time=int(kline[6]),
                open_price=float(kline[1]),
                high_price=float(kline[2]),
                low_price=float(kline[3]),
                close_price=float(kline[4]),
                volume=float(kline[5])
            ))
        
        return klines
    
    def _convert_order_type(self, order_type: OrderType) -> str:
        """转换订单类型"""
        type_map = {
            OrderType.MARKET: 'MARKET',
            OrderType.LIMIT: 'LIMIT',
            OrderType.STOP: 'STOP_LOSS',
            OrderType.STOP_LIMIT: 'STOP_LOSS_LIMIT'
        }
        return type_map[order_type]
    
    def _convert_order_side(self, side: OrderSide) -> str:
        """转换订单方向"""
        return side.value.upper()
    
    def _parse_order_status(self, status: str) -> OrderStatus:
        """解析订单状态"""
        status_map = {
            'NEW': OrderStatus.OPEN,
            'PARTIALLY_FILLED': OrderStatus.PARTIALLY_FILLED,
            'FILLED': OrderStatus.FILLED,
            'CANCELED': OrderStatus.CANCELLED,
            'REJECTED': OrderStatus.REJECTED,
            'EXPIRED': OrderStatus.EXPIRED
        }
        return status_map.get(status, OrderStatus.PENDING)
    
    async def create_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                          amount: float, price: Optional[float] = None,
                          stop_price: Optional[float] = None,
                          market_type: MarketType = MarketType.SPOT,
                          **kwargs) -> OrderResult:
        """创建订单"""
        symbol = self.normalize_symbol(symbol)
        
        params = {
            'symbol': symbol,
            'side': self._convert_order_side(side),
            'type': self._convert_order_type(order_type),
            'quantity': self.format_quantity(amount, symbol)
        }
        
        if order_type == OrderType.LIMIT:
            if price is None:
                raise ValueError("限价单必须指定价格")
            params['price'] = self.format_price(price, symbol)
            params['timeInForce'] = 'GTC'  # Good Till Cancelled
        
        if stop_price is not None:
            params['stopPrice'] = self.format_price(stop_price, symbol)
        
        # 根据市场类型选择端点
        if market_type == MarketType.FUTURES:
            endpoint = '/fapi/v1/order'
            futures = True
        else:
            endpoint = '/api/v3/order'
            futures = False
        
        data = await self._request('POST', endpoint, params, signed=True, futures=futures)
        
        return OrderResult(
            order_id=str(data['orderId']),
            symbol=data['symbol'],
            side=OrderSide(data['side'].lower()),
            order_type=order_type,
            amount=float(data['origQty']),
            price=float(data['price']) if data.get('price') else None,
            status=self._parse_order_status(data['status']),
            filled_amount=float(data.get('executedQty', 0)),
            timestamp=datetime.fromtimestamp(data['transactTime'] / 1000)
        )

    async def cancel_order(self, symbol: str, order_id: str,
                          market_type: MarketType = MarketType.SPOT) -> bool:
        """取消订单"""
        symbol = self.normalize_symbol(symbol)

        params = {
            'symbol': symbol,
            'orderId': order_id
        }

        if market_type == MarketType.FUTURES:
            endpoint = '/fapi/v1/order'
            futures = True
        else:
            endpoint = '/api/v3/order'
            futures = False

        try:
            await self._request('DELETE', endpoint, params, signed=True, futures=futures)
            return True
        except Exception as e:
            logger.error(f"取消订单失败: {e}")
            return False

    async def get_order(self, symbol: str, order_id: str,
                       market_type: MarketType = MarketType.SPOT) -> OrderResult:
        """获取订单信息"""
        symbol = self.normalize_symbol(symbol)

        params = {
            'symbol': symbol,
            'orderId': order_id
        }

        if market_type == MarketType.FUTURES:
            endpoint = '/fapi/v1/order'
            futures = True
        else:
            endpoint = '/api/v3/order'
            futures = False

        data = await self._request('GET', endpoint, params, signed=True, futures=futures)

        return OrderResult(
            order_id=str(data['orderId']),
            symbol=data['symbol'],
            side=OrderSide(data['side'].lower()),
            order_type=OrderType.LIMIT if data['type'] == 'LIMIT' else OrderType.MARKET,
            amount=float(data['origQty']),
            price=float(data['price']) if data.get('price') else None,
            status=self._parse_order_status(data['status']),
            filled_amount=float(data.get('executedQty', 0)),
            avg_price=float(data.get('avgPrice', 0)) if data.get('avgPrice') else None,
            timestamp=datetime.fromtimestamp(data['time'] / 1000)
        )

    async def get_open_orders(self, symbol: Optional[str] = None,
                             market_type: MarketType = MarketType.SPOT) -> List[OrderResult]:
        """获取活跃订单"""
        params = {}
        if symbol:
            params['symbol'] = self.normalize_symbol(symbol)

        if market_type == MarketType.FUTURES:
            endpoint = '/fapi/v1/openOrders'
            futures = True
        else:
            endpoint = '/api/v3/openOrders'
            futures = False

        data = await self._request('GET', endpoint, params, signed=True, futures=futures)

        orders = []
        for order in data:
            orders.append(OrderResult(
                order_id=str(order['orderId']),
                symbol=order['symbol'],
                side=OrderSide(order['side'].lower()),
                order_type=OrderType.LIMIT if order['type'] == 'LIMIT' else OrderType.MARKET,
                amount=float(order['origQty']),
                price=float(order['price']) if order.get('price') else None,
                status=self._parse_order_status(order['status']),
                filled_amount=float(order.get('executedQty', 0)),
                timestamp=datetime.fromtimestamp(order['time'] / 1000)
            ))

        return orders

    async def get_order_history(self, symbol: Optional[str] = None, limit: int = 100,
                               market_type: MarketType = MarketType.SPOT) -> List[OrderResult]:
        """获取历史订单"""
        params = {'limit': limit}
        if symbol:
            params['symbol'] = self.normalize_symbol(symbol)

        if market_type == MarketType.FUTURES:
            endpoint = '/fapi/v1/allOrders'
            futures = True
        else:
            endpoint = '/api/v3/allOrders'
            futures = False

        data = await self._request('GET', endpoint, params, signed=True, futures=futures)

        orders = []
        for order in data:
            orders.append(OrderResult(
                order_id=str(order['orderId']),
                symbol=order['symbol'],
                side=OrderSide(order['side'].lower()),
                order_type=OrderType.LIMIT if order['type'] == 'LIMIT' else OrderType.MARKET,
                amount=float(order['origQty']),
                price=float(order['price']) if order.get('price') else None,
                status=self._parse_order_status(order['status']),
                filled_amount=float(order.get('executedQty', 0)),
                timestamp=datetime.fromtimestamp(order['time'] / 1000)
            ))

        return orders

    async def get_trade_history(self, symbol: Optional[str] = None, limit: int = 100,
                               market_type: MarketType = MarketType.SPOT) -> List[TradeResult]:
        """获取成交历史"""
        params = {'limit': limit}
        if symbol:
            params['symbol'] = self.normalize_symbol(symbol)

        if market_type == MarketType.FUTURES:
            endpoint = '/fapi/v1/userTrades'
            futures = True
        else:
            endpoint = '/api/v3/myTrades'
            futures = False

        data = await self._request('GET', endpoint, params, signed=True, futures=futures)

        trades = []
        for trade in data:
            trades.append(TradeResult(
                trade_id=str(trade['id']),
                order_id=str(trade['orderId']),
                symbol=trade['symbol'],
                side=OrderSide.BUY if trade['isBuyer'] else OrderSide.SELL,
                amount=float(trade['qty']),
                price=float(trade['price']),
                fee=float(trade['commission']),
                fee_currency=trade['commissionAsset'],
                timestamp=datetime.fromtimestamp(trade['time'] / 1000)
            ))

        return trades

    async def get_positions(self, symbol: Optional[str] = None) -> List[PositionInfo]:
        """获取持仓信息 (期货)"""
        params = {}
        if symbol:
            params['symbol'] = self.normalize_symbol(symbol)

        data = await self._request('GET', '/fapi/v2/positionRisk', params, signed=True, futures=True)

        positions = []
        for pos in data:
            size = float(pos['positionAmt'])
            if size != 0:  # 只返回有持仓的
                positions.append(PositionInfo(
                    symbol=pos['symbol'],
                    side='long' if size > 0 else 'short',
                    size=abs(size),
                    entry_price=float(pos['entryPrice']),
                    mark_price=float(pos['markPrice']),
                    unrealized_pnl=float(pos['unRealizedProfit']),
                    margin=float(pos['isolatedMargin']),
                    leverage=float(pos['leverage']),
                    liquidation_price=float(pos['liquidationPrice']) if pos['liquidationPrice'] != '0' else None
                ))

        return positions

    async def get_symbols(self, market_type: MarketType = MarketType.SPOT) -> List[Dict[str, Any]]:
        """获取交易对信息"""
        if market_type == MarketType.FUTURES:
            data = await self._request('GET', '/fapi/v1/exchangeInfo', futures=True)
        else:
            data = await self._request('GET', '/api/v3/exchangeInfo')

        symbols = []
        for symbol_info in data['symbols']:
            if symbol_info['status'] == 'TRADING':
                symbols.append({
                    'symbol': symbol_info['symbol'],
                    'base_asset': symbol_info['baseAsset'],
                    'quote_asset': symbol_info['quoteAsset'],
                    'status': symbol_info['status'],
                    'min_qty': float(next(f['minQty'] for f in symbol_info['filters'] if f['filterType'] == 'LOT_SIZE')),
                    'max_qty': float(next(f['maxQty'] for f in symbol_info['filters'] if f['filterType'] == 'LOT_SIZE')),
                    'step_size': float(next(f['stepSize'] for f in symbol_info['filters'] if f['filterType'] == 'LOT_SIZE')),
                    'tick_size': float(next(f['tickSize'] for f in symbol_info['filters'] if f['filterType'] == 'PRICE_FILTER'))
                })

        return symbols

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
