"""
交易所WebSocket基础类
"""
import asyncio
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Callable, Any
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

logger = logging.getLogger(__name__)

class ExchangeWebSocketBase(ABC):
    """交易所WebSocket基础抽象类"""
    
    def __init__(self):
        self.ws = None
        self.is_connected = False
        self.subscriptions: Dict[str, List[Callable]] = {}
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5
        self._running = False
        self._tasks: List[asyncio.Task] = []
    
    @property
    @abstractmethod
    def ws_url(self) -> str:
        """WebSocket连接URL"""
        pass
    
    @abstractmethod
    async def _subscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成订阅消息"""
        pass
    
    @abstractmethod
    async def _unsubscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成取消订阅消息"""
        pass
    
    @abstractmethod
    async def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        pass
    
    async def connect(self):
        """建立WebSocket连接"""
        try:
            logger.info(f"连接到 {self.ws_url}")
            self.ws = await websockets.connect(
                self.ws_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )
            self.is_connected = True
            self.reconnect_attempts = 0
            logger.info("WebSocket连接建立成功")
            
            # 启动消息监听任务
            task = asyncio.create_task(self._listen_messages())
            self._tasks.append(task)
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            self.is_connected = False
            await self._handle_reconnect()
    
    async def disconnect(self):
        """断开WebSocket连接"""
        self._running = False
        self.is_connected = False
        
        # 取消所有任务
        for task in self._tasks:
            if not task.done():
                task.cancel()
        
        if self.ws:
            try:
                await self.ws.close()
            except Exception as e:
                logger.error(f"关闭WebSocket连接时出错: {e}")
            finally:
                self.ws = None
        
        logger.info("WebSocket连接已断开")
    
    async def _listen_messages(self):
        """监听WebSocket消息"""
        try:
            async for message in self.ws:
                try:
                    data = json.loads(message)
                    parsed_data = await self._parse_message(data)
                    
                    if parsed_data:
                        await self._notify_subscribers(parsed_data)
                        
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}, 原始消息: {message}")
                except Exception as e:
                    logger.error(f"处理消息时出错: {e}")
                    
        except ConnectionClosed:
            logger.warning("WebSocket连接被关闭")
            self.is_connected = False
            await self._handle_reconnect()
        except WebSocketException as e:
            logger.error(f"WebSocket异常: {e}")
            self.is_connected = False
            await self._handle_reconnect()
        except Exception as e:
            logger.error(f"监听消息时出错: {e}")
            self.is_connected = False
            await self._handle_reconnect()
    
    async def _handle_reconnect(self):
        """处理重连逻辑"""
        if not self._running:
            return
            
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            delay = self.reconnect_delay * self.reconnect_attempts
            
            logger.info(f"尝试重连 ({self.reconnect_attempts}/{self.max_reconnect_attempts})，{delay}秒后重试")
            await asyncio.sleep(delay)
            
            if self._running:  # 检查是否仍需要重连
                await self.connect()
        else:
            logger.error("达到最大重连次数，停止重连")
            self._running = False
    
    async def subscribe(self, symbols: List[str], callback: Callable[[Dict[str, Any]], None]):
        """订阅行情数据"""
        for symbol in symbols:
            if symbol not in self.subscriptions:
                self.subscriptions[symbol] = []
            self.subscriptions[symbol].append(callback)
        
        if self.is_connected and self.ws:
            try:
                message = await self._subscribe_message(symbols)
                await self.ws.send(json.dumps(message))
                logger.info(f"订阅成功: {symbols}")
            except Exception as e:
                logger.error(f"发送订阅消息失败: {e}")
        else:
            logger.warning("WebSocket未连接，订阅将在连接建立后自动发送")
    
    async def unsubscribe(self, symbols: List[str]):
        """取消订阅"""
        for symbol in symbols:
            if symbol in self.subscriptions:
                del self.subscriptions[symbol]
        
        if self.is_connected and self.ws:
            try:
                message = await self._unsubscribe_message(symbols)
                await self.ws.send(json.dumps(message))
                logger.info(f"取消订阅成功: {symbols}")
            except Exception as e:
                logger.error(f"发送取消订阅消息失败: {e}")
    
    async def _notify_subscribers(self, data: Dict[str, Any]):
        """通知订阅者"""
        symbol = data.get('symbol')
        if symbol and symbol in self.subscriptions:
            for callback in self.subscriptions[symbol]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {e}")
    
    async def start(self):
        """启动WebSocket客户端"""
        self._running = True
        await self.connect()
        
        # 重新订阅之前的symbols
        if self.subscriptions:
            symbols = list(self.subscriptions.keys())
            try:
                message = await self._subscribe_message(symbols)
                if self.ws:
                    await self.ws.send(json.dumps(message))
                    logger.info(f"重新订阅: {symbols}")
            except Exception as e:
                logger.error(f"重新订阅失败: {e}")
    
    async def stop(self):
        """停止WebSocket客户端"""
        await self.disconnect()
    
    def is_alive(self) -> bool:
        """检查连接是否活跃"""
        return self.is_connected and self.ws is not None and not self.ws.closed
