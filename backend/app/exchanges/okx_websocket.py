"""
OKX WebSocket实现
"""
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .websocket_base import ExchangeWebSocketBase

logger = logging.getLogger(__name__)

class OKXWebSocket(ExchangeWebSocketBase):
    """OKX WebSocket客户端"""
    
    def __init__(self, testnet: bool = False):
        super().__init__()
        self.testnet = testnet
    
    @property
    def ws_url(self) -> str:
        """WebSocket连接URL"""
        # OKX没有公开的测试网WebSocket
        return "wss://ws.okx.com:8443/ws/v5/public"
    
    def _symbol_to_instid(self, symbol: str) -> str:
        """将交易对转换为OKX格式"""
        # BTCUSDT -> BTC-USDT
        if 'USDT' in symbol:
            return symbol.replace('USDT', '-USDT')
        elif 'BTC' in symbol and symbol != 'BTC':
            return symbol.replace('BTC', '-BTC')
        elif 'ETH' in symbol and symbol != 'ETH':
            return symbol.replace('ETH', '-ETH')
        else:
            # 默认假设最后4位是计价币种
            if len(symbol) > 4:
                return symbol[:-4] + '-' + symbol[-4:]
            else:
                return symbol
    
    async def _subscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成订阅消息"""
        args = []
        for symbol in symbols:
            inst_id = self._symbol_to_instid(symbol)
            args.append({
                "channel": "tickers",
                "instId": inst_id
            })
        
        return {
            "op": "subscribe",
            "args": args
        }
    
    async def _unsubscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成取消订阅消息"""
        args = []
        for symbol in symbols:
            inst_id = self._symbol_to_instid(symbol)
            args.append({
                "channel": "tickers",
                "instId": inst_id
            })
        
        return {
            "op": "unsubscribe",
            "args": args
        }
    
    async def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        try:
            # 处理订阅确认消息
            if message.get("event") == "subscribe":
                logger.info(f"OKX订阅确认: {message}")
                return None
            
            # 处理取消订阅确认
            if message.get("event") == "unsubscribe":
                logger.info(f"OKX取消订阅确认: {message}")
                return None
            
            # 处理错误消息
            if message.get("event") == "error":
                logger.error(f"OKX WebSocket错误: {message}")
                return None
            
            # 处理数据推送
            if "data" in message and "arg" in message:
                arg = message["arg"]
                data_list = message["data"]
                
                if arg.get("channel") == "tickers" and data_list:
                    return await self._parse_ticker_data(data_list[0], arg.get("instId"))
            
            return None
            
        except Exception as e:
            logger.error(f"解析OKX消息失败: {e}, 原始消息: {message}")
            return None
    
    async def _parse_ticker_data(self, data: Dict[str, Any], inst_id: str) -> Dict[str, Any]:
        """解析ticker数据"""
        try:
            # 将OKX格式转换回标准格式
            symbol = inst_id.replace('-', '') if inst_id else data.get("instId", "").replace('-', '')
            
            price = float(data["last"])
            change_24h_percent = float(data["sodUtc8"])  # OKX返回的已经是百分比形式
            volume_24h = float(data["vol24h"])
            high_24h = float(data["high24h"])
            low_24h = float(data["low24h"])
            
            return {
                "type": "ticker",
                "exchange": "okx",
                "symbol": symbol,
                "price": price,
                "change_24h_percent": change_24h_percent,
                "volume_24h": volume_24h,
                "high_24h": high_24h,
                "low_24h": low_24h,
                "timestamp": datetime.utcnow().isoformat()
            }
        except (KeyError, ValueError) as e:
            logger.error(f"解析OKX ticker数据失败: {e}")
            return None

class OKXKlineWebSocket(ExchangeWebSocketBase):
    """OKX K线WebSocket客户端"""
    
    def __init__(self, interval: str = "1m", testnet: bool = False):
        super().__init__()
        self.interval = interval
        self.testnet = testnet
    
    @property
    def ws_url(self) -> str:
        """WebSocket连接URL"""
        return "wss://ws.okx.com:8443/ws/v5/public"
    
    def _symbol_to_instid(self, symbol: str) -> str:
        """将交易对转换为OKX格式"""
        if 'USDT' in symbol:
            return symbol.replace('USDT', '-USDT')
        elif 'BTC' in symbol and symbol != 'BTC':
            return symbol.replace('BTC', '-BTC')
        elif 'ETH' in symbol and symbol != 'ETH':
            return symbol.replace('ETH', '-ETH')
        else:
            if len(symbol) > 4:
                return symbol[:-4] + '-' + symbol[-4:]
            else:
                return symbol
    
    def _convert_interval(self, interval: str) -> str:
        """转换时间间隔格式"""
        interval_map = {
            '1m': '1m', '5m': '5m', '15m': '15m', '30m': '30m',
            '1h': '1H', '4h': '4H', '1d': '1D', '1w': '1W'
        }
        return interval_map.get(interval, '1m')
    
    async def _subscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成订阅消息"""
        args = []
        okx_interval = self._convert_interval(self.interval)
        
        for symbol in symbols:
            inst_id = self._symbol_to_instid(symbol)
            args.append({
                "channel": "candle" + okx_interval,
                "instId": inst_id
            })
        
        return {
            "op": "subscribe",
            "args": args
        }
    
    async def _unsubscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成取消订阅消息"""
        args = []
        okx_interval = self._convert_interval(self.interval)
        
        for symbol in symbols:
            inst_id = self._symbol_to_instid(symbol)
            args.append({
                "channel": "candle" + okx_interval,
                "instId": inst_id
            })
        
        return {
            "op": "unsubscribe",
            "args": args
        }
    
    async def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        try:
            # 处理订阅确认消息
            if message.get("event") in ["subscribe", "unsubscribe"]:
                return None
            
            # 处理错误消息
            if message.get("event") == "error":
                logger.error(f"OKX K线WebSocket错误: {message}")
                return None
            
            # 处理K线数据
            if "data" in message and "arg" in message:
                arg = message["arg"]
                data_list = message["data"]
                
                if "candle" in arg.get("channel", "") and data_list:
                    return await self._parse_kline_data(data_list[0], arg.get("instId"))
            
            return None
            
        except Exception as e:
            logger.error(f"解析OKX K线消息失败: {e}")
            return None
    
    async def _parse_kline_data(self, data: List[str], inst_id: str) -> Dict[str, Any]:
        """解析K线数据"""
        try:
            # 将OKX格式转换回标准格式
            symbol = inst_id.replace('-', '') if inst_id else ""
            
            # OKX K线数据格式: [ts, o, h, l, c, vol, volCcy, volCcyQuote, confirm]
            open_time = int(data[0])
            open_price = float(data[1])
            high_price = float(data[2])
            low_price = float(data[3])
            close_price = float(data[4])
            volume = float(data[5])
            is_closed = data[8] == "1"  # confirm字段
            
            return {
                "type": "kline",
                "exchange": "okx",
                "symbol": symbol,
                "interval": self.interval,
                "open_time": open_time,
                "close_time": open_time + 60000,  # 假设1分钟间隔
                "open_price": open_price,
                "high_price": high_price,
                "low_price": low_price,
                "close_price": close_price,
                "volume": volume,
                "is_closed": is_closed,
                "timestamp": datetime.utcnow().isoformat()
            }
        except (KeyError, ValueError, IndexError) as e:
            logger.error(f"解析OKX K线数据失败: {e}")
            return None

class OKXDepthWebSocket(ExchangeWebSocketBase):
    """OKX深度WebSocket客户端"""
    
    def __init__(self, testnet: bool = False):
        super().__init__()
        self.testnet = testnet
    
    @property
    def ws_url(self) -> str:
        """WebSocket连接URL"""
        return "wss://ws.okx.com:8443/ws/v5/public"
    
    def _symbol_to_instid(self, symbol: str) -> str:
        """将交易对转换为OKX格式"""
        if 'USDT' in symbol:
            return symbol.replace('USDT', '-USDT')
        elif 'BTC' in symbol and symbol != 'BTC':
            return symbol.replace('BTC', '-BTC')
        elif 'ETH' in symbol and symbol != 'ETH':
            return symbol.replace('ETH', '-ETH')
        else:
            if len(symbol) > 4:
                return symbol[:-4] + '-' + symbol[-4:]
            else:
                return symbol
    
    async def _subscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成订阅消息"""
        args = []
        for symbol in symbols:
            inst_id = self._symbol_to_instid(symbol)
            args.append({
                "channel": "books5",  # 5档深度
                "instId": inst_id
            })
        
        return {
            "op": "subscribe",
            "args": args
        }
    
    async def _unsubscribe_message(self, symbols: List[str]) -> Dict[str, Any]:
        """生成取消订阅消息"""
        args = []
        for symbol in symbols:
            inst_id = self._symbol_to_instid(symbol)
            args.append({
                "channel": "books5",
                "instId": inst_id
            })
        
        return {
            "op": "unsubscribe",
            "args": args
        }
    
    async def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        try:
            # 处理订阅确认消息
            if message.get("event") in ["subscribe", "unsubscribe"]:
                return None
            
            # 处理错误消息
            if message.get("event") == "error":
                logger.error(f"OKX深度WebSocket错误: {message}")
                return None
            
            # 处理深度数据
            if "data" in message and "arg" in message:
                arg = message["arg"]
                data_list = message["data"]
                
                if arg.get("channel") == "books5" and data_list:
                    return await self._parse_depth_data(data_list[0], arg.get("instId"))
            
            return None
            
        except Exception as e:
            logger.error(f"解析OKX深度消息失败: {e}")
            return None
    
    async def _parse_depth_data(self, data: Dict[str, Any], inst_id: str) -> Dict[str, Any]:
        """解析深度数据"""
        try:
            # 将OKX格式转换回标准格式
            symbol = inst_id.replace('-', '') if inst_id else ""
            
            bids = [[float(bid[0]), float(bid[1])] for bid in data.get("bids", [])]
            asks = [[float(ask[0]), float(ask[1])] for ask in data.get("asks", [])]
            
            return {
                "type": "orderbook",
                "exchange": "okx",
                "symbol": symbol,
                "bids": bids,
                "asks": asks,
                "timestamp": datetime.utcnow().isoformat()
            }
        except (KeyError, ValueError) as e:
            logger.error(f"解析OKX深度数据失败: {e}")
            return None
