"""
交易所基础抽象类
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from decimal import Decimal
from datetime import datetime
from enum import Enum

class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"

class MarketType(Enum):
    """市场类型"""
    SPOT = "spot"
    FUTURES = "futures"
    MARGIN = "margin"

class TickerData:
    """行情数据"""
    def __init__(self, symbol: str, price: float, change_24h: float, 
                 volume_24h: float, high_24h: float, low_24h: float, timestamp: datetime):
        self.symbol = symbol
        self.price = price
        self.change_24h = change_24h
        self.change_24h_percent = (change_24h / (price - change_24h)) * 100 if price != change_24h else 0
        self.volume_24h = volume_24h
        self.high_24h = high_24h
        self.low_24h = low_24h
        self.timestamp = timestamp

class OrderBookData:
    """订单簿数据"""
    def __init__(self, symbol: str, bids: List[List[float]], asks: List[List[float]], timestamp: datetime):
        self.symbol = symbol
        self.bids = bids  # [[price, quantity], ...]
        self.asks = asks  # [[price, quantity], ...]
        self.timestamp = timestamp

class KlineData:
    """K线数据"""
    def __init__(self, symbol: str, interval: str, open_time: int, close_time: int,
                 open_price: float, high_price: float, low_price: float, 
                 close_price: float, volume: float):
        self.symbol = symbol
        self.interval = interval
        self.open_time = open_time
        self.close_time = close_time
        self.open_price = open_price
        self.high_price = high_price
        self.low_price = low_price
        self.close_price = close_price
        self.volume = volume

class OrderResult:
    """下单结果"""
    def __init__(self, order_id: str, symbol: str, side: OrderSide, order_type: OrderType,
                 amount: float, price: Optional[float], status: OrderStatus, 
                 filled_amount: float = 0, avg_price: Optional[float] = None,
                 fee: float = 0, fee_currency: Optional[str] = None,
                 timestamp: Optional[datetime] = None):
        self.order_id = order_id
        self.symbol = symbol
        self.side = side
        self.order_type = order_type
        self.amount = amount
        self.price = price
        self.status = status
        self.filled_amount = filled_amount
        self.avg_price = avg_price
        self.fee = fee
        self.fee_currency = fee_currency
        self.timestamp = timestamp or datetime.utcnow()

class TradeResult:
    """成交结果"""
    def __init__(self, trade_id: str, order_id: str, symbol: str, side: OrderSide,
                 amount: float, price: float, fee: float, fee_currency: str,
                 timestamp: datetime):
        self.trade_id = trade_id
        self.order_id = order_id
        self.symbol = symbol
        self.side = side
        self.amount = amount
        self.price = price
        self.fee = fee
        self.fee_currency = fee_currency
        self.timestamp = timestamp

class BalanceInfo:
    """余额信息"""
    def __init__(self, currency: str, free: float, locked: float, total: float):
        self.currency = currency
        self.free = free
        self.locked = locked
        self.total = total

class PositionInfo:
    """持仓信息"""
    def __init__(self, symbol: str, side: str, size: float, entry_price: float,
                 mark_price: float, unrealized_pnl: float, margin: float,
                 leverage: float, liquidation_price: Optional[float] = None):
        self.symbol = symbol
        self.side = side
        self.size = size
        self.entry_price = entry_price
        self.mark_price = mark_price
        self.unrealized_pnl = unrealized_pnl
        self.margin = margin
        self.leverage = leverage
        self.liquidation_price = liquidation_price

class BaseExchange(ABC):
    """交易所基础抽象类"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: Optional[str] = None, 
                 testnet: bool = False):
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
        self.testnet = testnet
        self.name = self.__class__.__name__.replace('Exchange', '').lower()
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """测试连接"""
        pass
    
    @abstractmethod
    async def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        pass
    
    @abstractmethod
    async def get_balances(self) -> List[BalanceInfo]:
        """获取余额信息"""
        pass
    
    @abstractmethod
    async def get_ticker(self, symbol: str) -> TickerData:
        """获取单个交易对行情"""
        pass
    
    @abstractmethod
    async def get_all_tickers(self) -> List[TickerData]:
        """获取所有交易对行情"""
        pass
    
    @abstractmethod
    async def get_orderbook(self, symbol: str, limit: int = 20) -> OrderBookData:
        """获取订单簿"""
        pass
    
    @abstractmethod
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> List[KlineData]:
        """获取K线数据"""
        pass
    
    @abstractmethod
    async def create_order(self, symbol: str, side: OrderSide, order_type: OrderType,
                          amount: float, price: Optional[float] = None,
                          stop_price: Optional[float] = None,
                          market_type: MarketType = MarketType.SPOT,
                          **kwargs) -> OrderResult:
        """创建订单"""
        pass
    
    @abstractmethod
    async def cancel_order(self, symbol: str, order_id: str, 
                          market_type: MarketType = MarketType.SPOT) -> bool:
        """取消订单"""
        pass
    
    @abstractmethod
    async def get_order(self, symbol: str, order_id: str,
                       market_type: MarketType = MarketType.SPOT) -> OrderResult:
        """获取订单信息"""
        pass
    
    @abstractmethod
    async def get_open_orders(self, symbol: Optional[str] = None,
                             market_type: MarketType = MarketType.SPOT) -> List[OrderResult]:
        """获取活跃订单"""
        pass
    
    @abstractmethod
    async def get_order_history(self, symbol: Optional[str] = None, limit: int = 100,
                               market_type: MarketType = MarketType.SPOT) -> List[OrderResult]:
        """获取历史订单"""
        pass
    
    @abstractmethod
    async def get_trade_history(self, symbol: Optional[str] = None, limit: int = 100,
                               market_type: MarketType = MarketType.SPOT) -> List[TradeResult]:
        """获取成交历史"""
        pass
    
    @abstractmethod
    async def get_positions(self, symbol: Optional[str] = None) -> List[PositionInfo]:
        """获取持仓信息 (期货)"""
        pass
    
    @abstractmethod
    async def get_symbols(self, market_type: MarketType = MarketType.SPOT) -> List[Dict[str, Any]]:
        """获取交易对信息"""
        pass
    
    def normalize_symbol(self, symbol: str) -> str:
        """标准化交易对格式"""
        return symbol.upper().replace('/', '')
    
    def format_price(self, price: float, symbol: str) -> str:
        """格式化价格精度"""
        # 默认实现，子类可以重写
        return f"{price:.8f}".rstrip('0').rstrip('.')
    
    def format_quantity(self, quantity: float, symbol: str) -> str:
        """格式化数量精度"""
        # 默认实现，子类可以重写
        return f"{quantity:.8f}".rstrip('0').rstrip('.')
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        pass
