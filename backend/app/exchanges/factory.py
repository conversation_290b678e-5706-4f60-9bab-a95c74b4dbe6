"""
交易所工厂类
"""
from typing import Optional, Dict, Type
from .base import BaseExchange
from .binance_exchange import BinanceExchange
from .okx_exchange import OKXExchange

class ExchangeFactory:
    """交易所工厂类"""
    
    # 注册的交易所类
    _exchanges: Dict[str, Type[BaseExchange]] = {
        'binance': BinanceExchange,
        'okx': OKXExchange,
    }
    
    @classmethod
    def create_exchange(cls, exchange_name: str, api_key: str, secret_key: str, 
                       passphrase: Optional[str] = None, testnet: bool = False) -> BaseExchange:
        """创建交易所实例"""
        exchange_name = exchange_name.lower()
        
        if exchange_name not in cls._exchanges:
            raise ValueError(f"不支持的交易所: {exchange_name}")
        
        exchange_class = cls._exchanges[exchange_name]
        
        # OKX需要passphrase
        if exchange_name == 'okx' and not passphrase:
            raise ValueError("OKX交易所需要passphrase参数")
        
        return exchange_class(
            api_key=api_key,
            secret_key=secret_key,
            passphrase=passphrase,
            testnet=testnet
        )
    
    @classmethod
    def get_supported_exchanges(cls) -> list:
        """获取支持的交易所列表"""
        return list(cls._exchanges.keys())
    
    @classmethod
    def register_exchange(cls, name: str, exchange_class: Type[BaseExchange]):
        """注册新的交易所"""
        cls._exchanges[name.lower()] = exchange_class
    
    @classmethod
    def is_supported(cls, exchange_name: str) -> bool:
        """检查是否支持指定交易所"""
        return exchange_name.lower() in cls._exchanges
