"""
策略引擎单元测试
"""
import pytest
import numpy as np
from datetime import datetime, timedelta
from typing import List

from app.core.strategy_engine import BaseStrategy, MarketData, BacktestResult
from app.strategies.grid_strategy import GridStrategy
from app.strategies.martingale_strategy import MartingaleStrategy
from app.strategies.dca_strategy import DCAStrategy
from app.strategies.ma_strategy import MAStrategy


class TestMarketData:
    """市场数据测试"""

    def test_market_data_creation(self):
        """测试市场数据创建"""
        timestamp = datetime.now()
        data = MarketData(
            timestamp=timestamp,
            open=100.0,
            high=105.0,
            low=95.0,
            close=102.0,
            volume=1000.0
        )

        assert data.timestamp == timestamp
        assert data.open == 100.0
        assert data.high == 105.0
        assert data.low == 95.0
        assert data.close == 102.0
        assert data.volume == 1000.0


class TestGridStrategy:
    """网格策略测试"""

    def test_grid_strategy_initialization(self):
        """测试网格策略初始化"""
        parameters = {
            'grid_count': 5,
            'price_range_lower': 90.0,
            'price_range_upper': 110.0,
            'grid_spacing': 'arithmetic',
            'investment_per_grid': 100.0
        }

        strategy = GridStrategy("BTCUSDT", 1000.0, parameters)

        assert strategy.symbol == "BTCUSDT"
        assert strategy.initial_capital == 1000.0
        assert strategy.grid_count == 5
        assert len(strategy.grid_prices) == 5
        assert strategy.grid_prices[0] == 90.0
        assert strategy.grid_prices[-1] == 110.0

    def test_grid_price_calculation_arithmetic(self):
        """测试等差网格价格计算"""
        parameters = {
            'grid_count': 5,
            'price_range_lower': 100.0,
            'price_range_upper': 200.0,
            'grid_spacing': 'arithmetic',
            'investment_per_grid': 100.0
        }

        strategy = GridStrategy("BTCUSDT", 1000.0, parameters)
        expected_prices = [100.0, 125.0, 150.0, 175.0, 200.0]

        assert len(strategy.grid_prices) == 5
        for i, price in enumerate(strategy.grid_prices):
            assert abs(price - expected_prices[i]) < 0.01

    def test_grid_price_calculation_geometric(self):
        """测试等比网格价格计算"""
        parameters = {
            'grid_count': 3,
            'price_range_lower': 100.0,
            'price_range_upper': 400.0,
            'grid_spacing': 'geometric',
            'investment_per_grid': 100.0
        }

        strategy = GridStrategy("BTCUSDT", 1000.0, parameters)

        assert len(strategy.grid_prices) == 3
        assert abs(strategy.grid_prices[0] - 100.0) < 0.01
        assert abs(strategy.grid_prices[1] - 200.0) < 0.01
        assert abs(strategy.grid_prices[2] - 400.0) < 0.01

    def test_parameter_validation(self):
        """测试参数验证"""
        # 有效参数
        valid_params = {
            'grid_count': 5,
            'price_range_lower': 90.0,
            'price_range_upper': 110.0,
            'grid_spacing': 'arithmetic',
            'investment_per_grid': 100.0
        }

        strategy = GridStrategy("BTCUSDT", 1000.0, valid_params)
        is_valid, message = strategy.validate_parameters()
        assert is_valid
        assert message == "参数验证通过"

        # 无效参数：价格下限为0
        invalid_params = valid_params.copy()
        invalid_params['price_range_lower'] = 0

        strategy = GridStrategy("BTCUSDT", 1000.0, invalid_params)
        is_valid, message = strategy.validate_parameters()
        assert not is_valid
        assert "价格下限必须大于0" in message

        # 无效参数：价格上限小于下限
        invalid_params = valid_params.copy()
        invalid_params['price_range_upper'] = 80.0

        strategy = GridStrategy("BTCUSDT", 1000.0, invalid_params)
        is_valid, message = strategy.validate_parameters()
        assert not is_valid
        assert "价格上限必须大于价格下限" in message

    def test_signal_generation(self):
        """测试信号生成"""
        parameters = {
            'grid_count': 3,
            'price_range_lower': 90.0,
            'price_range_upper': 110.0,
            'grid_spacing': 'arithmetic',
            'investment_per_grid': 100.0
        }

        strategy = GridStrategy("BTCUSDT", 1000.0, parameters)

        # 网格价格应该是 [90.0, 100.0, 110.0]
        expected_grid_prices = [90.0, 100.0, 110.0]
        assert strategy.grid_prices == expected_grid_prices

        # 创建测试数据
        test_data = [
            MarketData(datetime.now(), 100, 105, 95, 95, 1000),  # 价格下跌到95，应该触发买入
            MarketData(datetime.now(), 95, 100, 90, 100, 1000),  # 价格上涨到100，应该触发卖出
        ]

        signals = strategy.generate_signals(test_data)

        # 应该有买入信号
        assert len(signals) >= 1

        # 检查信号内容
        buy_signals = [s for s in signals if s[1] == 'buy']
        assert len(buy_signals) >= 1

        # 第一个买入信号应该是在90.0网格价格
        first_buy_signal = buy_signals[0]
        assert first_buy_signal[1] == 'buy'  # 方向
        assert first_buy_signal[2] == 90.0   # 价格（网格价格）


class TestMartingaleStrategy:
    """马丁格尔策略测试"""

    def test_martingale_initialization(self):
        """测试马丁格尔策略初始化"""
        parameters = {
            'base_amount': 100.0,
            'multiplier': 2.0,
            'max_levels': 5,
            'take_profit_pct': 0.02,
            'stop_loss_pct': 0.1,
            'signal_type': 'rsi'
        }

        strategy = MartingaleStrategy("BTCUSDT", 1000.0, parameters)

        assert strategy.symbol == "BTCUSDT"
        assert strategy.initial_capital == 1000.0
        assert strategy.base_amount == 100.0
        assert strategy.multiplier == 2.0
        assert strategy.max_levels == 5

    def test_rsi_calculation(self):
        """测试RSI计算"""
        parameters = {
            'base_amount': 100.0,
            'multiplier': 2.0,
            'max_levels': 3,
            'take_profit_pct': 0.02,
            'stop_loss_pct': 0.1,
            'signal_type': 'rsi',
            'rsi_period': 14
        }

        strategy = MartingaleStrategy("BTCUSDT", 1000.0, parameters)

        # 创建价格序列（下跌趋势，RSI应该较低）
        prices = [100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86]
        strategy.price_history = prices

        rsi = strategy._calculate_rsi()

        # 下跌趋势的RSI应该小于50
        assert rsi < 50
        assert 0 <= rsi <= 100

    def test_parameter_validation(self):
        """测试参数验证"""
        # 有效参数 - 使用更小的基础金额以通过80%资金限制
        valid_params = {
            'base_amount': 50.0,  # 减少基础金额
            'multiplier': 2.0,
            'max_levels': 3,
            'take_profit_pct': 0.02,
            'stop_loss_pct': 0.1,
            'signal_type': 'rsi'
        }

        strategy = MartingaleStrategy("BTCUSDT", 1000.0, valid_params)
        is_valid, message = strategy.validate_parameters()
        assert is_valid, f"验证失败: {message}"

        # 无效参数：倍数小于等于1
        invalid_params = valid_params.copy()
        invalid_params['multiplier'] = 1.0

        strategy = MartingaleStrategy("BTCUSDT", 1000.0, invalid_params)
        is_valid, message = strategy.validate_parameters()
        assert not is_valid
        assert "倍数必须大于1" in message

    def test_martingale_amount_calculation(self):
        """测试马丁格尔金额计算"""
        parameters = {
            'base_amount': 100.0,
            'multiplier': 2.0,
            'max_levels': 3,
            'take_profit_pct': 0.02,
            'stop_loss_pct': 0.1,
            'signal_type': 'rsi'
        }

        strategy = MartingaleStrategy("BTCUSDT", 1000.0, parameters)
        strategy.price_history = [100.0]  # 设置当前价格

        # 测试不同级别的投注金额
        strategy.current_level = 0
        amount_0 = strategy._calculate_martingale_amount()
        expected_0 = 100.0 / 100.0  # base_amount / price
        assert abs(amount_0 - expected_0) < 0.01

        strategy.current_level = 1
        amount_1 = strategy._calculate_martingale_amount()
        expected_1 = (100.0 * 2.0) / 100.0  # base_amount * multiplier / price
        assert abs(amount_1 - expected_1) < 0.01

        strategy.current_level = 2
        amount_2 = strategy._calculate_martingale_amount()
        expected_2 = (100.0 * 4.0) / 100.0  # base_amount * multiplier^2 / price
        assert abs(amount_2 - expected_2) < 0.01


class TestBacktestEngine:
    """回测引擎测试"""

    def test_simple_backtest(self):
        """测试简单回测"""
        parameters = {
            'grid_count': 3,
            'price_range_lower': 90.0,
            'price_range_upper': 110.0,
            'grid_spacing': 'arithmetic',
            'investment_per_grid': 100.0
        }

        strategy = GridStrategy("BTCUSDT", 1000.0, parameters)

        # 创建简单的测试数据
        test_data = []
        base_time = datetime.now()
        prices = [100, 95, 90, 95, 100, 105, 110, 105, 100]

        for i, price in enumerate(prices):
            data = MarketData(
                timestamp=base_time + timedelta(hours=i),
                open=price,
                high=price + 2,
                low=price - 2,
                close=price,
                volume=1000
            )
            test_data.append(data)

        # 执行回测
        result = strategy.backtest(test_data)

        # 验证回测结果
        assert isinstance(result, BacktestResult)
        assert result.total_trades >= 0
        assert len(result.equity_curve) == len(test_data)
        assert isinstance(result.total_return, float)
        assert isinstance(result.max_drawdown, float)
        assert isinstance(result.sharpe_ratio, float)
        assert 0 <= result.win_rate <= 1

    def test_backtest_result_serialization(self):
        """测试回测结果序列化"""
        parameters = {
            'grid_count': 2,
            'price_range_lower': 95.0,
            'price_range_upper': 105.0,
            'grid_spacing': 'arithmetic',
            'investment_per_grid': 100.0
        }

        strategy = GridStrategy("BTCUSDT", 500.0, parameters)

        # 创建测试数据
        test_data = [
            MarketData(datetime.now(), 100, 102, 98, 100, 1000),
            MarketData(datetime.now() + timedelta(hours=1), 100, 102, 98, 95, 1000),
        ]

        result = strategy.backtest(test_data)
        result_dict = result.to_dict()

        # 验证序列化结果
        assert isinstance(result_dict, dict)
        assert 'total_return' in result_dict
        assert 'trades' in result_dict
        assert 'equity_curve' in result_dict
        assert isinstance(result_dict['trades'], list)
        assert isinstance(result_dict['equity_curve'], list)


class TestDCAStrategy:
    """DCA策略测试"""

    def test_dca_initialization(self):
        """测试DCA策略初始化"""
        parameters = {
            'investment_amount': 100.0,
            'investment_interval': 24,
            'price_drop_threshold': 0.05,
            'take_profit_pct': 0.2,
            'max_investment_count': 10,
            'enable_smart_dca': False
        }

        strategy = DCAStrategy("BTCUSDT", 2000.0, parameters)

        assert strategy.symbol == "BTCUSDT"
        assert strategy.initial_capital == 2000.0
        assert strategy.investment_amount == 100.0
        assert strategy.investment_interval == 24
        assert strategy.max_investment_count == 10

    def test_dca_parameter_validation(self):
        """测试DCA参数验证"""
        # 有效参数
        valid_params = {
            'investment_amount': 100.0,
            'investment_interval': 24,
            'price_drop_threshold': 0.05,
            'take_profit_pct': 0.2,
            'max_investment_count': 10,
            'enable_smart_dca': False
        }

        strategy = DCAStrategy("BTCUSDT", 2000.0, valid_params)
        is_valid, message = strategy.validate_parameters()
        assert is_valid

        # 无效参数：投资金额为0
        invalid_params = valid_params.copy()
        invalid_params['investment_amount'] = 0

        strategy = DCAStrategy("BTCUSDT", 2000.0, invalid_params)
        is_valid, message = strategy.validate_parameters()
        assert not is_valid
        assert "每次投资金额必须大于0" in message


class TestMAStrategy:
    """均线策略测试"""

    def test_ma_initialization(self):
        """测试均线策略初始化"""
        parameters = {
            'fast_period': 5,
            'slow_period': 20,
            'ma_type': 'sma',
            'position_size_pct': 0.8,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.1,
            'enable_trend_filter': True,
            'trend_period': 50
        }

        strategy = MAStrategy("BTCUSDT", 1000.0, parameters)

        assert strategy.symbol == "BTCUSDT"
        assert strategy.initial_capital == 1000.0
        assert strategy.fast_period == 5
        assert strategy.slow_period == 20
        assert strategy.ma_type == 'sma'

    def test_ma_calculation(self):
        """测试移动平均线计算"""
        parameters = {
            'fast_period': 3,
            'slow_period': 5,
            'ma_type': 'sma',
            'position_size_pct': 0.8,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.1,
            'enable_trend_filter': False,
            'trend_period': 10
        }

        strategy = MAStrategy("BTCUSDT", 1000.0, parameters)

        # 测试SMA计算
        prices = [100, 102, 98, 105, 103]
        sma_3 = strategy._calculate_ma(prices, 3)
        expected_sma_3 = (98 + 105 + 103) / 3  # 最后3个价格的平均值
        assert abs(sma_3 - expected_sma_3) < 0.01

        sma_5 = strategy._calculate_ma(prices, 5)
        expected_sma_5 = sum(prices) / 5
        assert abs(sma_5 - expected_sma_5) < 0.01

    def test_ma_parameter_validation(self):
        """测试均线策略参数验证"""
        # 有效参数
        valid_params = {
            'fast_period': 5,
            'slow_period': 20,
            'ma_type': 'sma',
            'position_size_pct': 0.8,
            'stop_loss_pct': 0.05,
            'take_profit_pct': 0.1,
            'enable_trend_filter': True,
            'trend_period': 50
        }

        strategy = MAStrategy("BTCUSDT", 1000.0, valid_params)
        is_valid, message = strategy.validate_parameters()
        assert is_valid

        # 无效参数：快线周期大于等于慢线周期
        invalid_params = valid_params.copy()
        invalid_params['fast_period'] = 25

        strategy = MAStrategy("BTCUSDT", 1000.0, invalid_params)
        is_valid, message = strategy.validate_parameters()
        assert not is_valid
        assert "快线周期必须小于慢线周期" in message


if __name__ == "__main__":
    pytest.main([__file__])
