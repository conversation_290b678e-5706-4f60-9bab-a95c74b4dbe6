#!/usr/bin/env python3
"""
WebSocket实时行情测试脚本
"""
import asyncio
import sys
import os
import json
import logging

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from app.exchanges.binance_websocket import BinanceWebSocket, BinanceKlineWebSocket, BinanceDepthWebSocket
from app.exchanges.okx_websocket import OKXWebSocket, OKXKlineWebSocket, OKXDepthWebSocket
from app.services.websocket_manager import ws_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_binance_ticker():
    """测试币安ticker WebSocket"""
    print("🔍 测试币安Ticker WebSocket...")
    
    ws = BinanceWebSocket()
    
    # 数据接收回调
    received_data = []
    
    async def on_ticker_data(data):
        received_data.append(data)
        print(f"📊 币安行情: {data['symbol']} - ${data['price']:.2f} ({data['change_24h_percent']:.2f}%)")
    
    try:
        # 启动WebSocket
        await ws.start()
        
        # 订阅行情
        await ws.subscribe(['BTCUSDT', 'ETHUSDT'], on_ticker_data)
        
        # 等待接收数据
        print("⏳ 等待接收行情数据...")
        await asyncio.sleep(10)
        
        # 检查结果
        if received_data:
            print(f"✅ 成功接收到 {len(received_data)} 条币安行情数据")
            return True
        else:
            print("❌ 未接收到币安行情数据")
            return False
            
    except Exception as e:
        print(f"❌ 币安Ticker WebSocket测试失败: {e}")
        return False
    finally:
        await ws.stop()

async def test_binance_kline():
    """测试币安K线WebSocket"""
    print("\n🔍 测试币安K线WebSocket...")
    
    ws = BinanceKlineWebSocket(interval="1m")
    
    # 数据接收回调
    received_data = []
    
    async def on_kline_data(data):
        received_data.append(data)
        print(f"📈 币安K线: {data['symbol']} - O:{data['open_price']:.2f} H:{data['high_price']:.2f} L:{data['low_price']:.2f} C:{data['close_price']:.2f}")
    
    try:
        # 启动WebSocket
        await ws.start()
        
        # 订阅K线
        await ws.subscribe(['BTCUSDT'], on_kline_data)
        
        # 等待接收数据
        print("⏳ 等待接收K线数据...")
        await asyncio.sleep(10)
        
        # 检查结果
        if received_data:
            print(f"✅ 成功接收到 {len(received_data)} 条币安K线数据")
            return True
        else:
            print("❌ 未接收到币安K线数据")
            return False
            
    except Exception as e:
        print(f"❌ 币安K线WebSocket测试失败: {e}")
        return False
    finally:
        await ws.stop()

async def test_okx_ticker():
    """测试OKX ticker WebSocket"""
    print("\n🔍 测试OKX Ticker WebSocket...")
    
    ws = OKXWebSocket()
    
    # 数据接收回调
    received_data = []
    
    async def on_ticker_data(data):
        received_data.append(data)
        print(f"📊 OKX行情: {data['symbol']} - ${data['price']:.2f} ({data['change_24h_percent']:.2f}%)")
    
    try:
        # 启动WebSocket
        await ws.start()
        
        # 订阅行情
        await ws.subscribe(['BTCUSDT', 'ETHUSDT'], on_ticker_data)
        
        # 等待接收数据
        print("⏳ 等待接收行情数据...")
        await asyncio.sleep(10)
        
        # 检查结果
        if received_data:
            print(f"✅ 成功接收到 {len(received_data)} 条OKX行情数据")
            return True
        else:
            print("❌ 未接收到OKX行情数据")
            return False
            
    except Exception as e:
        print(f"❌ OKX Ticker WebSocket测试失败: {e}")
        return False
    finally:
        await ws.stop()

async def test_websocket_manager():
    """测试WebSocket管理器"""
    print("\n🔍 测试WebSocket管理器...")
    
    # 数据接收回调
    received_data = []
    
    async def on_market_data(data):
        received_data.append(data)
        print(f"📊 {data['exchange']} {data['type']}: {data['symbol']} - ${data['price']:.2f}")
    
    try:
        # 启动管理器
        await ws_manager.start()
        
        # 订阅币安行情
        await ws_manager.subscribe_ticker('binance', ['BTCUSDT'], on_market_data)
        
        # 订阅OKX行情
        await ws_manager.subscribe_ticker('okx', ['BTCUSDT'], on_market_data)
        
        # 等待接收数据
        print("⏳ 等待接收管理器数据...")
        await asyncio.sleep(15)
        
        # 检查连接状态
        status = ws_manager.get_connection_status()
        print(f"📡 连接状态: {json.dumps(status, indent=2)}")
        
        # 检查订阅状态
        subscriptions = ws_manager.get_subscriptions()
        print(f"📋 订阅状态: {json.dumps({k: list(v) for k, v in subscriptions.items()}, indent=2)}")
        
        # 检查结果
        if received_data:
            print(f"✅ 成功接收到 {len(received_data)} 条管理器数据")
            return True
        else:
            print("❌ 未接收到管理器数据")
            return False
            
    except Exception as e:
        print(f"❌ WebSocket管理器测试失败: {e}")
        return False
    finally:
        await ws_manager.stop()

async def test_connection_stability():
    """测试连接稳定性"""
    print("\n🔍 测试连接稳定性...")
    
    ws = BinanceWebSocket()
    
    # 数据接收计数
    data_count = 0
    
    async def on_data(data):
        nonlocal data_count
        data_count += 1
        if data_count % 10 == 0:
            print(f"📊 已接收 {data_count} 条数据")
    
    try:
        # 启动WebSocket
        await ws.start()
        
        # 订阅行情
        await ws.subscribe(['BTCUSDT'], on_data)
        
        # 长时间运行测试
        print("⏳ 运行30秒稳定性测试...")
        await asyncio.sleep(30)
        
        # 检查连接状态
        if ws.is_alive():
            print(f"✅ 连接稳定，共接收 {data_count} 条数据")
            return True
        else:
            print("❌ 连接不稳定")
            return False
            
    except Exception as e:
        print(f"❌ 连接稳定性测试失败: {e}")
        return False
    finally:
        await ws.stop()

async def main():
    """主测试函数"""
    print("🧠 个人量化交易平台 - WebSocket实时行情测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试币安Ticker
    result = await test_binance_ticker()
    test_results.append(("币安Ticker", result))
    
    # 测试币安K线
    result = await test_binance_kline()
    test_results.append(("币安K线", result))
    
    # 测试OKX Ticker
    result = await test_okx_ticker()
    test_results.append(("OKX Ticker", result))
    
    # 测试WebSocket管理器
    result = await test_websocket_manager()
    test_results.append(("WebSocket管理器", result))
    
    # 测试连接稳定性
    result = await test_connection_stability()
    test_results.append(("连接稳定性", result))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 30)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有WebSocket测试通过！")
        print("\n📝 说明:")
        print("- 实时行情推送功能正常")
        print("- 支持币安和OKX交易所")
        print("- WebSocket管理器工作正常")
        print("- 连接稳定性良好")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败")
        print("请检查网络连接和交易所API状态")
    
    print("\n🔧 使用方法:")
    print("1. 在前端订阅实时行情")
    print("2. 支持ticker、kline、depth数据类型")
    print("3. 支持多交易所同时订阅")
    print("4. 自动重连和错误处理")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
