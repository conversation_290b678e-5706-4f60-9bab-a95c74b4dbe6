#!/usr/bin/env python3
"""
交易所集成测试脚本
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from app.exchanges.factory import ExchangeFactory
from app.exchanges.base import OrderSide, OrderType, MarketType

async def test_binance():
    """测试币安交易所"""
    print("🔍 测试币安交易所...")
    
    # 使用测试API密钥（这些是无效的，仅用于测试结构）
    try:
        exchange = ExchangeFactory.create_exchange(
            exchange_name="binance",
            api_key="test_api_key",
            secret_key="test_secret_key",
            testnet=True
        )
        
        print(f"✅ 币安交易所实例创建成功: {exchange.name}")
        
        # 测试连接（会失败，因为是假密钥）
        try:
            connected = await exchange.test_connection()
            if connected:
                print("✅ 币安连接测试成功")
            else:
                print("❌ 币安连接测试失败（预期，因为使用测试密钥）")
        except Exception as e:
            print(f"❌ 币安连接测试异常（预期）: {e}")
        
        # 测试公开API（获取行情）
        try:
            ticker = await exchange.get_ticker("BTCUSDT")
            print(f"✅ 获取币安行情成功: {ticker.symbol} - ${ticker.price}")
        except Exception as e:
            print(f"❌ 获取币安行情失败: {e}")
        
        await exchange.__aexit__(None, None, None)
        
    except Exception as e:
        print(f"❌ 币安交易所测试失败: {e}")

async def test_okx():
    """测试OKX交易所"""
    print("\n🔍 测试OKX交易所...")
    
    try:
        exchange = ExchangeFactory.create_exchange(
            exchange_name="okx",
            api_key="test_api_key",
            secret_key="test_secret_key",
            passphrase="test_passphrase",
            testnet=True
        )
        
        print(f"✅ OKX交易所实例创建成功: {exchange.name}")
        
        # 测试连接（会失败，因为是假密钥）
        try:
            connected = await exchange.test_connection()
            if connected:
                print("✅ OKX连接测试成功")
            else:
                print("❌ OKX连接测试失败（预期，因为使用测试密钥）")
        except Exception as e:
            print(f"❌ OKX连接测试异常（预期）: {e}")
        
        # 测试公开API（获取行情）
        try:
            ticker = await exchange.get_ticker("BTCUSDT")
            print(f"✅ 获取OKX行情成功: {ticker.symbol} - ${ticker.price}")
        except Exception as e:
            print(f"❌ 获取OKX行情失败: {e}")
        
        await exchange.__aexit__(None, None, None)
        
    except Exception as e:
        print(f"❌ OKX交易所测试失败: {e}")

async def test_factory():
    """测试交易所工厂"""
    print("\n🔍 测试交易所工厂...")
    
    # 测试支持的交易所列表
    supported = ExchangeFactory.get_supported_exchanges()
    print(f"✅ 支持的交易所: {supported}")
    
    # 测试是否支持特定交易所
    assert ExchangeFactory.is_supported("binance"), "应该支持币安"
    assert ExchangeFactory.is_supported("okx"), "应该支持OKX"
    assert not ExchangeFactory.is_supported("unknown"), "不应该支持未知交易所"
    print("✅ 交易所支持检查通过")
    
    # 测试创建不支持的交易所
    try:
        ExchangeFactory.create_exchange("unknown", "key", "secret")
        print("❌ 应该抛出异常")
    except ValueError as e:
        print(f"✅ 正确抛出异常: {e}")
    
    # 测试OKX缺少passphrase
    try:
        ExchangeFactory.create_exchange("okx", "key", "secret")
        print("❌ 应该抛出异常")
    except ValueError as e:
        print(f"✅ 正确抛出异常: {e}")

def test_data_models():
    """测试数据模型"""
    print("\n🔍 测试数据模型...")
    
    from app.exchanges.base import TickerData, OrderResult, TradeResult, BalanceInfo, PositionInfo
    from datetime import datetime
    
    # 测试TickerData
    ticker = TickerData(
        symbol="BTCUSDT",
        price=50000.0,
        change_24h=1000.0,
        volume_24h=1000000.0,
        high_24h=51000.0,
        low_24h=49000.0,
        timestamp=datetime.utcnow()
    )
    print(f"✅ TickerData: {ticker.symbol} - ${ticker.price} ({ticker.change_24h_percent:.2f}%)")
    
    # 测试OrderResult
    order = OrderResult(
        order_id="12345",
        symbol="BTCUSDT",
        side=OrderSide.BUY,
        order_type=OrderType.LIMIT,
        amount=0.001,
        price=50000.0,
        status=OrderStatus.OPEN
    )
    print(f"✅ OrderResult: {order.order_id} - {order.side.value} {order.amount} {order.symbol}")
    
    # 测试BalanceInfo
    balance = BalanceInfo(
        currency="USDT",
        free=1000.0,
        locked=100.0,
        total=1100.0
    )
    print(f"✅ BalanceInfo: {balance.currency} - {balance.total} (free: {balance.free})")
    
    print("✅ 所有数据模型测试通过")

async def main():
    """主测试函数"""
    print("🧠 个人量化交易平台 - 交易所集成测试")
    print("=" * 50)
    
    # 测试数据模型
    test_data_models()
    
    # 测试工厂类
    await test_factory()
    
    # 测试币安交易所
    await test_binance()
    
    # 测试OKX交易所
    await test_okx()
    
    print("\n" + "=" * 50)
    print("🎉 交易所集成测试完成！")
    print("\n📝 说明:")
    print("- 连接测试失败是正常的，因为使用的是测试密钥")
    print("- 行情API测试可能因为网络问题失败")
    print("- 实际使用时需要配置真实的API密钥")
    print("\n🔧 下一步:")
    print("1. 在API密钥管理中添加真实的交易所API密钥")
    print("2. 测试API密钥连接功能")
    print("3. 尝试获取账户余额和下单功能")

if __name__ == "__main__":
    # 修复导入问题
    from app.exchanges.base import OrderStatus
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
