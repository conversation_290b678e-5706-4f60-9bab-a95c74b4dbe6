2025-05-29 07:20:27,443 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:20:27,484 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:20:27,498 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:20:27,498 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:20:27,498 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:20:27,498 - app.core.database - ERROR - 数据库连接测试失败: Not an executable object: 'SELECT 1'
2025-05-29 07:20:27,498 - app.main - ERROR - ❌ 数据库连接测试失败
2025-05-29 07:20:27,498 - app.main - ERROR - ❌ 应用启动失败: 数据库连接失败
2025-05-29 07:20:59,216 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:20:59,258 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:20:59,260 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:20:59,260 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:20:59,260 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:20:59,261 - app.core.database - ERROR - 数据库连接测试失败: Not an executable object: 'SELECT 1'
2025-05-29 07:20:59,261 - app.main - ERROR - ❌ 数据库连接测试失败
2025-05-29 07:20:59,261 - app.main - ERROR - ❌ 应用启动失败: 数据库连接失败
2025-05-29 07:21:11,280 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:21:11,328 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:21:11,331 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:21:11,331 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:21:11,331 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:21:11,331 - app.core.database - ERROR - 数据库连接测试失败: Not an executable object: 'SELECT 1'
2025-05-29 07:21:11,331 - app.main - ERROR - ❌ 数据库连接测试失败
2025-05-29 07:21:11,331 - app.main - ERROR - ❌ 应用启动失败: 数据库连接失败
2025-05-29 07:22:35,953 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:22:35,998 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:22:36,006 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:22:36,006 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:22:36,006 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:22:36,006 - app.core.database - ERROR - 数据库连接测试失败: Not an executable object: 'SELECT 1'
2025-05-29 07:22:36,006 - app.main - ERROR - ❌ 数据库连接测试失败
2025-05-29 07:22:36,006 - app.main - ERROR - ❌ 应用启动失败: 数据库连接失败
2025-05-29 07:23:17,952 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:23:17,995 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:23:17,997 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:23:17,997 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:23:17,997 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:23:17,997 - app.core.database - ERROR - 数据库连接测试失败: Not an executable object: 'SELECT 1'
2025-05-29 07:23:17,997 - app.main - ERROR - ❌ 数据库连接测试失败
2025-05-29 07:23:17,997 - app.main - ERROR - ❌ 应用启动失败: 数据库连接失败
2025-05-29 07:23:57,209 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:23:57,257 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:23:57,259 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:23:57,259 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:23:57,259 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:23:57,259 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:23:57,259 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:24:06,632 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:24:06,686 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:24:06,690 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:24:06,690 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:24:06,690 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:24:06,691 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:24:06,691 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:24:19,614 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:24:19,648 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:24:23,567 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:24:23,568 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:24:23,609 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:24:23,610 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:24:23,612 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:24:23,612 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:24:23,612 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:24:23,612 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:24:23,612 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:24:23,613 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:24:23,613 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:24:23,613 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:24:23,613 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:24:23,613 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:24:34,485 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:24:34,500 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:24:35,075 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:24:35,076 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:24:35,117 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:24:35,117 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:24:35,118 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:24:35,118 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:24:35,118 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:24:35,119 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:24:35,119 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:24:35,119 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:24:35,119 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:24:35,119 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:24:35,119 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:24:35,119 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:24:50,377 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:24:50,378 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:24:51,191 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:24:51,191 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:24:51,238 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:24:51,238 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:24:51,240 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:24:51,240 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:24:51,240 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:24:51,240 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:24:51,240 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:24:51,240 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:24:51,240 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:24:51,240 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:24:51,240 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:24:51,240 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:11,754 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:11,849 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:12,353 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:12,354 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:12,392 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:12,393 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:12,394 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:12,395 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:12,395 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:12,395 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:12,395 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:12,395 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:12,395 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:12,396 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:12,396 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:12,396 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:26,826 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:26,826 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:35,708 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:35,708 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:35,747 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:35,747 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:35,748 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:35,748 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:35,748 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:35,748 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:35,748 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:35,748 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:35,749 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:35,749 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:35,749 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:35,749 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:46,361 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:46,432 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:46,958 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:46,961 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:46,999 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:47,000 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:47,000 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:47,000 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:47,000 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:47,001 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:47,001 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:47,001 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:47,001 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:47,001 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:47,001 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:47,001 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:55,742 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:55,765 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:25:56,249 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:56,255 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:25:56,289 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:56,290 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:56,290 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:56,290 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:56,291 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:56,291 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:25:56,294 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:25:56,296 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:25:56,296 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:25:56,296 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:25:56,296 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:25:56,296 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:26:09,745 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:26:09,745 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:26:10,752 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:26:10,752 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:26:10,790 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:26:10,790 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:26:10,792 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:26:10,792 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:26:10,792 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:26:10,792 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:26:10,792 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:26:10,792 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:26:10,792 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:26:10,792 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:26:10,792 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:26:10,792 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:26:35,109 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 07:26:54,534 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 07:26:59,147 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:26:59,208 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:26:59,911 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:26:59,912 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:26:59,954 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:26:59,954 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:26:59,956 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:26:59,956 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:26:59,956 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:26:59,956 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:26:59,956 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:26:59,956 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:26:59,957 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:26:59,957 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:26:59,957 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:26:59,957 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:26:59,986 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 07:27:13,985 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:27:13,986 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:27:14,535 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:27:14,535 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:27:14,588 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:27:14,593 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:27:14,593 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:27:14,593 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:27:14,594 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:27:14,594 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:27:14,597 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:27:14,602 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:27:14,602 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:27:14,602 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:27:14,603 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:27:14,603 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:28:29,760 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:28:29,803 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:28:30,411 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:28:30,425 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:28:30,469 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:28:30,471 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:28:30,472 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:28:30,472 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:28:30,473 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:28:30,473 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:28:30,490 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:28:30,492 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:28:30,492 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:28:30,492 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:28:30,493 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:28:30,494 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:28:47,683 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 07:34:36,247 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:34:36,306 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:34:37,104 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:34:37,106 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:34:37,152 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:34:37,152 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:34:37,156 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:34:37,156 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:34:37,156 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:34:37,156 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:34:37,156 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:34:37,156 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:34:37,157 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:34:37,157 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:34:37,157 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:34:37,157 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:07,981 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:08,053 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:08,605 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:08,605 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:08,666 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:08,667 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:08,672 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:08,672 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:08,672 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:08,673 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:08,673 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:08,674 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:08,674 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:08,674 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:08,675 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:08,675 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:38,327 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:38,331 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:39,095 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:39,095 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:39,136 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:39,137 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:39,139 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:39,139 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:39,139 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:39,139 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:39,139 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:39,140 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:39,140 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:39,140 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:39,140 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:39,140 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:48,225 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:48,228 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:48,897 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:48,897 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:48,942 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:48,943 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:48,945 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:48,945 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:48,945 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:48,945 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:48,945 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:48,945 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:48,946 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:48,946 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:48,946 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:48,946 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:55,910 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:55,914 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:35:56,409 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:56,410 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:35:56,448 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:56,448 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:35:56,450 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:56,450 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:35:56,450 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:56,450 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:56,450 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:35:56,450 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:35:56,450 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:56,450 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:35:56,450 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:35:56,450 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:38:33,954 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:38:34,014 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:38:34,919 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:38:34,919 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:38:34,962 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:38:34,963 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:38:34,965 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:38:34,965 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:38:34,965 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:38:34,965 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:38:34,965 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:38:34,965 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:38:34,965 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:38:34,965 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:38:34,965 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:38:34,965 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:43:41,229 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:43:42,043 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 07:43:42,678 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:43:42,679 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 07:43:42,723 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:43:42,723 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 07:43:42,727 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:43:42,727 - app.core.database - INFO - 数据库表创建成功
2025-05-29 07:43:42,727 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:43:42,727 - app.core.database - INFO - 数据库初始化完成
2025-05-29 07:43:42,727 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:43:42,727 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 07:43:42,727 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:43:42,727 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 07:43:42,728 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:43:42,728 - app.main - INFO - 🎉 应用启动完成
2025-05-29 07:48:49,700 - app.main - ERROR - 未处理的异常: <built-in method rollback of sqlite3.Connection object at 0x12082cf40> returned NULL without setting an exception
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 14, in __call__
    async with AsyncExitStack() as stack:
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 745, in __aexit__
    raise exc_details[1]
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 728, in __aexit__
    cb_suppress = await cb(*exc_details)
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 217, in __aexit__
    await anext(self.gen)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/concurrency.py", line 38, in contextmanager_in_threadpool
    await anyio.to_thread.run_sync(
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/to_thread.py", line 33, in run_sync
    return await get_asynclib().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 877, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 807, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 144, in __exit__
    next(self.gen)
  File "/Users/<USER>/software/pyapp/lazy/backend/app/core/database.py", line 56, in get_db
    db.close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2468, in close
    self._close_impl(invalidate=False)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2537, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1362, in close
    transaction.close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2576, in close
    self._do_close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2714, in _do_close
    self._close_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2700, in _close_impl
    self._connection_rollback_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2692, in _connection_rollback_impl
    self.connection._rollback_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1121, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2346, in _handle_dbapi_exception
    raise exc_info[1].with_traceback(exc_info[2])
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1119, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 692, in do_rollback
    dbapi_connection.rollback()
SystemError: <built-in method rollback of sqlite3.Connection object at 0x12082cf40> returned NULL without setting an exception
2025-05-29 07:48:49,725 - app.main - ERROR - 未处理的异常: (sqlite3.DatabaseError) cannot rollback - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/4xp6)
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1119, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 692, in do_rollback
    dbapi_connection.rollback()
sqlite3.DatabaseError: cannot rollback - no transaction is active

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 14, in __call__
    async with AsyncExitStack() as stack:
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 745, in __aexit__
    raise exc_details[1]
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 728, in __aexit__
    cb_suppress = await cb(*exc_details)
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 217, in __aexit__
    await anext(self.gen)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/concurrency.py", line 38, in contextmanager_in_threadpool
    await anyio.to_thread.run_sync(
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/to_thread.py", line 33, in run_sync
    return await get_asynclib().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 877, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 807, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 144, in __exit__
    next(self.gen)
  File "/Users/<USER>/software/pyapp/lazy/backend/app/core/database.py", line 56, in get_db
    db.close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2468, in close
    self._close_impl(invalidate=False)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2537, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1362, in close
    transaction.close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2576, in close
    self._do_close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2714, in _do_close
    self._close_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2700, in _close_impl
    self._connection_rollback_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2692, in _connection_rollback_impl
    self.connection._rollback_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1121, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1119, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 692, in do_rollback
    dbapi_connection.rollback()
sqlalchemy.exc.DatabaseError: (sqlite3.DatabaseError) cannot rollback - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-05-29 07:51:25,794 - app.main - ERROR - 未处理的异常: <built-in method rollback of sqlite3.Connection object at 0x12082cf40> returned NULL without setting an exception
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 14, in __call__
    async with AsyncExitStack() as stack:
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 745, in __aexit__
    raise exc_details[1]
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 728, in __aexit__
    cb_suppress = await cb(*exc_details)
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 217, in __aexit__
    await anext(self.gen)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/concurrency.py", line 38, in contextmanager_in_threadpool
    await anyio.to_thread.run_sync(
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/to_thread.py", line 33, in run_sync
    return await get_asynclib().run_sync_in_worker_thread(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 877, in run_sync_in_worker_thread
    return await future
           ^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 807, in run
    result = context.run(func, *args)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.12-macos-aarch64-none/lib/python3.11/contextlib.py", line 144, in __exit__
    next(self.gen)
  File "/Users/<USER>/software/pyapp/lazy/backend/app/core/database.py", line 56, in get_db
    db.close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2468, in close
    self._close_impl(invalidate=False)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 2537, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1362, in close
    transaction.close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2576, in close
    self._do_close()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2714, in _do_close
    self._close_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2700, in _close_impl
    self._connection_rollback_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2692, in _connection_rollback_impl
    self.connection._rollback_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1121, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2346, in _handle_dbapi_exception
    raise exc_info[1].with_traceback(exc_info[2])
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1119, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 692, in do_rollback
    dbapi_connection.rollback()
SystemError: <built-in method rollback of sqlite3.Connection object at 0x12082cf40> returned NULL without setting an exception
2025-05-29 07:51:25,851 - app.core.database - ERROR - 数据库会话错误: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 07:51:25,851 - app.main - ERROR - 未处理的异常: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlite3.OperationalError: cannot commit - no transaction is active

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 264, in app
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 563, in solve_dependencies
    solved_result = await solve_dependencies(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/fastapi/dependencies/utils.py", line 592, in solve_dependencies
    solved = await call(**sub_values)
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/app/api/auth.py", line 106, in get_current_user
    db.commit()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1969, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py", line 1263, in commit
    trans.commit()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2619, in commit
    self._do_commit()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2724, in _do_commit
    self._connection_commit_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2695, in _connection_commit_impl
    self.connection._commit_impl()
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1138, in _commit_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 2343, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/base.py", line 1136, in _commit_impl
    self.engine.dialect.do_commit(self.connection)
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/sqlalchemy/engine/default.py", line 695, in do_commit
    dbapi_connection.commit()
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) cannot commit - no transaction is active
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-05-29 08:05:08,421 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 08:05:08,462 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 08:05:11,994 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/software/pyapp/lazy/backend/.venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-05-29 08:05:29,987 - app.core.database - ERROR - 数据库会话错误: 
2025-05-29 08:06:10,471 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 08:06:10,519 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 08:06:11,471 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:06:11,471 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 08:06:11,514 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 08:06:11,514 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 08:06:11,518 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:06:11,518 - app.core.database - INFO - 数据库表创建成功
2025-05-29 08:06:11,518 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:06:11,518 - app.core.database - INFO - 数据库初始化完成
2025-05-29 08:06:11,518 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 08:06:11,518 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 08:06:11,518 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:06:11,518 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 08:06:11,518 - app.main - INFO - 🎉 应用启动完成
2025-05-29 08:06:11,518 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:02:29,817 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:02:29,840 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:02:29,842 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:02:29,842 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:02:29,842 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:02:29,842 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:02:29,842 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:08:29,842 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:08:30,235 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:08:30,257 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:08:30,258 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:08:30,258 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:08:30,258 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:08:30,258 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:08:30,258 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:09:29,715 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:09:29,737 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:09:29,739 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:09:29,739 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:09:29,739 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:09:29,739 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:09:29,739 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:16:53,679 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:16:53,704 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:16:53,706 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:16:53,706 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:16:53,706 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:16:53,706 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:16:53,706 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:31:12,669 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:31:12,694 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:31:12,696 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:31:12,696 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:31:12,696 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:31:12,696 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:31:12,696 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:35:20,501 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:20,887 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:20,908 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:20,911 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:20,911 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:20,911 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:20,911 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:20,911 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:35:32,630 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:33,143 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:33,165 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:33,167 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:33,167 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:33,167 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:33,167 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:33,167 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:35:50,034 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:35:50,455 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:35:50,484 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:35:50,485 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:35:50,485 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:35:50,485 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:35:50,485 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:35:50,486 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:36:08,976 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:09,307 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:09,329 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:09,331 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:09,331 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:09,331 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:09,331 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:09,331 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:36:24,380 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:24,798 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:24,822 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:24,823 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:24,823 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:24,823 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:24,823 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:24,823 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:36:59,461 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 11:36:59,783 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:36:59,803 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:36:59,805 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:36:59,805 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:36:59,805 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:36:59,805 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:36:59,805 - app.main - INFO - 🎉 应用启动完成
2025-05-29 11:38:04,790 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 11:38:04,811 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 11:38:04,812 - app.core.database - INFO - 数据库表创建成功
2025-05-29 11:38:04,813 - app.core.database - INFO - 数据库初始化完成
2025-05-29 11:38:04,813 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 11:38:04,813 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 11:38:04,813 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:39:59,660 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:40:08,138 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:40:08,162 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:40:08,163 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:40:08,163 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:40:08,164 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:40:08,164 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:40:08,164 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:40:19,899 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:40:20,633 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:40:20,656 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:40:20,658 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:40:20,658 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:40:20,658 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:40:20,658 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:40:20,658 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:40:53,497 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:40:54,229 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:40:54,286 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:40:54,287 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:40:54,287 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:40:54,288 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:40:54,288 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:40:54,288 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:44:16,466 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:44:17,203 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:44:17,227 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:44:17,229 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:44:17,229 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:44:17,229 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:44:17,229 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:44:17,229 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:44:39,463 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:44:40,162 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:44:40,191 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:44:40,192 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:44:40,192 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:44:40,192 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:44:40,192 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:44:40,192 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:49:23,253 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:49:24,109 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:49:24,137 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:49:24,139 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:49:24,139 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:49:24,139 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:49:24,139 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:49:24,139 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:51:45,099 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:51:45,939 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:51:45,962 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:51:45,963 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:51:45,963 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:51:45,963 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:51:45,964 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:51:45,964 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:51:56,877 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:51:57,610 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:51:57,633 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:51:57,634 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:51:57,634 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:51:57,634 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:51:57,634 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:51:57,634 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:52:24,618 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:52:25,399 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:52:25,423 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:52:25,425 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:52:25,425 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:52:25,425 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:52:25,425 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:52:25,425 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:52:52,300 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 13:52:52,995 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:52:53,023 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:52:53,024 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:52:53,024 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:52:53,024 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:52:53,024 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:52:53,024 - app.main - INFO - 🎉 应用启动完成
2025-05-29 13:57:32,413 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 13:57:32,437 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 13:57:32,438 - app.core.database - INFO - 数据库表创建成功
2025-05-29 13:57:32,438 - app.core.database - INFO - 数据库初始化完成
2025-05-29 13:57:32,438 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 13:57:32,439 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 13:57:32,439 - app.main - INFO - 🎉 应用启动完成
2025-05-29 14:25:00,576 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:25:01,532 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:25:01,568 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 14:25:01,570 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:25:01,570 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:25:01,570 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 14:25:01,570 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:25:01,570 - app.main - INFO - 🎉 应用启动完成
2025-05-29 14:25:34,196 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:25:34,991 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:25:35,016 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 14:25:35,019 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:25:35,019 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:25:35,019 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 14:25:35,019 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:25:35,019 - app.main - INFO - 🎉 应用启动完成
2025-05-29 14:25:50,370 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:25:51,327 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:25:51,355 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 14:25:51,357 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:25:51,357 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:25:51,357 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 14:25:51,358 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:25:51,358 - app.main - INFO - 🎉 应用启动完成
2025-05-29 14:26:11,657 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:26:12,539 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:26:12,567 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 14:26:12,568 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:26:12,568 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:26:12,568 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 14:26:12,569 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:26:12,569 - app.main - INFO - 🎉 应用启动完成
2025-05-29 14:26:19,534 - app.main - INFO - 👋 关闭个人量化交易平台...
2025-05-29 14:26:20,298 - app.main - INFO - 🚀 启动个人量化交易平台...
2025-05-29 14:26:20,337 - app.main - INFO - ✅ 主密码验证成功
2025-05-29 14:26:20,339 - app.core.database - INFO - 数据库表创建成功
2025-05-29 14:26:20,339 - app.core.database - INFO - 数据库初始化完成
2025-05-29 14:26:20,339 - app.main - INFO - ✅ 数据库初始化完成
2025-05-29 14:26:20,339 - app.main - INFO - ✅ 数据库连接测试成功
2025-05-29 14:26:20,339 - app.main - INFO - 🎉 应用启动完成
