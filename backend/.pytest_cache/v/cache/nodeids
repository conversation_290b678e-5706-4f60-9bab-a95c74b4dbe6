["tests/test_strategy_engine.py::TestBacktestEngine::test_backtest_result_serialization", "tests/test_strategy_engine.py::TestBacktestEngine::test_simple_backtest", "tests/test_strategy_engine.py::TestDCAStrategy::test_dca_initialization", "tests/test_strategy_engine.py::TestDCAStrategy::test_dca_parameter_validation", "tests/test_strategy_engine.py::TestGridStrategy::test_grid_price_calculation_arithmetic", "tests/test_strategy_engine.py::TestGridStrategy::test_grid_price_calculation_geometric", "tests/test_strategy_engine.py::TestGridStrategy::test_grid_strategy_initialization", "tests/test_strategy_engine.py::TestGridStrategy::test_parameter_validation", "tests/test_strategy_engine.py::TestGridStrategy::test_signal_generation", "tests/test_strategy_engine.py::TestMarketData::test_market_data_creation", "tests/test_strategy_engine.py::TestMartingaleStrategy::test_martingale_amount_calculation", "tests/test_strategy_engine.py::TestMartingaleStrategy::test_martingale_initialization", "tests/test_strategy_engine.py::TestMartingaleStrategy::test_parameter_validation", "tests/test_strategy_engine.py::TestMartingaleStrategy::test_rsi_calculation"]