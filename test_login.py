#!/usr/bin/env python3
"""
测试登录API
"""
import requests
import json

def test_login():
    """测试登录功能"""
    print("🧪 测试登录API...")
    
    # 登录数据
    login_data = {
        "username": "admin",
        "password": "123456"
    }
    
    try:
        # 发送登录请求
        response = requests.post(
            "http://localhost:8000/api/v1/auth/login",
            data=login_data,  # 使用form data格式
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 登录成功!")
            print(f"Token: {data.get('access_token', 'N/A')[:50]}...")
            print(f"用户: {data.get('user', {}).get('username', 'N/A')}")
            return data.get('access_token')
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_protected_endpoint(token):
    """测试受保护的端点"""
    if not token:
        print("❌ 没有token，跳过受保护端点测试")
        return
    
    print("\n🧪 测试受保护端点...")
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/auth/me",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            user = response.json()
            print("✅ 获取用户信息成功!")
            print(f"用户ID: {user.get('id')}")
            print(f"用户名: {user.get('username')}")
            print(f"邮箱: {user.get('email')}")
        else:
            print(f"❌ 获取用户信息失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    print("🧠 个人量化交易平台 - API测试")
    print("=" * 50)
    
    # 测试登录
    token = test_login()
    
    # 测试受保护端点
    test_protected_endpoint(token)
    
    print("\n🎉 测试完成!")
