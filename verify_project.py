#!/usr/bin/env python3
"""
项目结构验证脚本
"""
import os
import sys
from pathlib import Path

def check_file_exists(file_path, description=""):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description or file_path}")
        return True
    else:
        print(f"❌ {description or file_path} - 文件不存在")
        return False

def check_directory_exists(dir_path, description=""):
    """检查目录是否存在"""
    if os.path.isdir(dir_path):
        print(f"✅ {description or dir_path}")
        return True
    else:
        print(f"❌ {description or dir_path} - 目录不存在")
        return False

def main():
    """主验证函数"""
    print("🧠 个人量化交易平台 - 项目结构验证")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    passed = 0
    total = 0
    
    # 检查根目录文件
    root_files = [
        ("README.md", "项目说明文档"),
        (".env.example", "环境变量模板"),
        ("docker-compose.yml", "Docker编排文件"),
        ("start.sh", "启动脚本"),
        ("DEPLOYMENT.md", "部署指南"),
        ("PROJECT_SUMMARY.md", "项目总结"),
    ]
    
    print("\n📁 检查根目录文件...")
    for file_path, description in root_files:
        total += 1
        if check_file_exists(file_path, description):
            passed += 1
    
    # 检查后端目录结构
    backend_structure = [
        ("backend", "后端目录"),
        ("backend/app", "应用目录"),
        ("backend/app/api", "API路由目录"),
        ("backend/app/core", "核心模块目录"),
        ("backend/app/models", "数据模型目录"),
        ("backend/app/services", "业务逻辑目录"),
        ("backend/app/strategies", "策略目录"),
        ("backend/app/utils", "工具函数目录"),
    ]
    
    print("\n🔧 检查后端目录结构...")
    for dir_path, description in backend_structure:
        total += 1
        if check_directory_exists(dir_path, description):
            passed += 1
    
    # 检查后端核心文件
    backend_files = [
        ("backend/requirements.txt", "Python依赖文件"),
        ("backend/Dockerfile", "后端Docker文件"),
        ("backend/app/main.py", "FastAPI主应用"),
        ("backend/app/core/config.py", "配置模块"),
        ("backend/app/core/security.py", "安全模块"),
        ("backend/app/core/database.py", "数据库模块"),
        ("backend/app/models/__init__.py", "模型初始化"),
        ("backend/app/api/auth.py", "认证API"),
        ("backend/app/api/users.py", "用户API"),
        ("backend/app/api/api_keys.py", "API密钥管理"),
        ("backend/app/api/orders.py", "订单API"),
        ("backend/app/api/trades.py", "交易API"),
        ("backend/app/api/strategies.py", "策略API"),
        ("backend/app/api/market_data.py", "行情API"),
        ("backend/app/api/telegram_settings.py", "Telegram API"),
        ("backend/app/api/risk_management.py", "风控API"),
    ]
    
    print("\n🔧 检查后端核心文件...")
    for file_path, description in backend_files:
        total += 1
        if check_file_exists(file_path, description):
            passed += 1
    
    # 检查前端目录结构
    frontend_structure = [
        ("frontend", "前端目录"),
        ("frontend/src", "源码目录"),
        ("frontend/src/components", "组件目录"),
        ("frontend/src/pages", "页面目录"),
        ("frontend/src/stores", "状态管理目录"),
        ("frontend/src/services", "服务目录"),
        ("frontend/src/utils", "工具函数目录"),
        ("frontend/src/styles", "样式目录"),
    ]
    
    print("\n🎨 检查前端目录结构...")
    for dir_path, description in frontend_structure:
        total += 1
        if check_directory_exists(dir_path, description):
            passed += 1
    
    # 检查前端核心文件
    frontend_files = [
        ("frontend/package.json", "Node.js依赖文件"),
        ("frontend/Dockerfile", "前端Docker文件"),
        ("frontend/vite.config.ts", "Vite配置"),
        ("frontend/tailwind.config.js", "Tailwind配置"),
        ("frontend/tsconfig.json", "TypeScript配置"),
        ("frontend/index.html", "HTML入口文件"),
        ("frontend/src/main.tsx", "React入口文件"),
        ("frontend/src/App.tsx", "主应用组件"),
        ("frontend/src/styles/index.css", "主样式文件"),
        ("frontend/src/stores/authStore.ts", "认证状态管理"),
        ("frontend/src/stores/themeStore.ts", "主题状态管理"),
        ("frontend/src/services/api.ts", "API服务"),
        ("frontend/src/services/websocket.ts", "WebSocket服务"),
        ("frontend/src/components/Layout.tsx", "布局组件"),
        ("frontend/src/components/LoadingSpinner.tsx", "加载组件"),
        ("frontend/src/pages/LoginPage.tsx", "登录页面"),
        ("frontend/src/pages/RegisterPage.tsx", "注册页面"),
        ("frontend/src/pages/DashboardPage.tsx", "仪表盘页面"),
        ("frontend/src/utils/cn.ts", "样式工具"),
        ("frontend/src/utils/format.ts", "格式化工具"),
    ]
    
    print("\n🎨 检查前端核心文件...")
    for file_path, description in frontend_files:
        total += 1
        if check_file_exists(file_path, description):
            passed += 1
    
    # 检查数据模型文件
    model_files = [
        ("backend/app/models/user.py", "用户模型"),
        ("backend/app/models/api_key.py", "API密钥模型"),
        ("backend/app/models/order.py", "订单模型"),
        ("backend/app/models/trade.py", "交易模型"),
        ("backend/app/models/position.py", "持仓模型"),
        ("backend/app/models/strategy.py", "策略模型"),
        ("backend/app/models/telegram_setting.py", "Telegram设置模型"),
        ("backend/app/models/risk_event_log.py", "风控事件模型"),
    ]
    
    print("\n🗄️ 检查数据模型文件...")
    for file_path, description in model_files:
        total += 1
        if check_file_exists(file_path, description):
            passed += 1
    
    # 输出结果
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 项目结构完整！所有必要文件都已创建")
        print("\n🚀 下一步操作:")
        print("1. 配置环境变量: cp .env.example .env")
        print("2. 编辑 .env 文件，设置必要的配置")
        print("3. 运行启动脚本: ./start.sh")
        print("4. 或使用Docker: docker-compose up -d")
        return True
    else:
        print("⚠️  项目结构不完整，请检查缺失的文件")
        return False

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except Exception as e:
        print(f"\n💥 验证过程中发生错误: {e}")
        sys.exit(1)
