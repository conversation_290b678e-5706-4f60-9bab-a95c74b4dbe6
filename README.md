# 🧠 个人量化交易平台

一个安全、轻量、可部署的个人量化交易平台，支持币安与OKX交易所。

## ✨ 核心特性

- 🔐 **安全加密**: AES加密存储API密钥，主密码保护
- 🤖 **多策略支持**: 网格交易、马丁格尔、偏移挂单
- 📊 **实时行情**: WebSocket实时数据，无延迟交易
- 🛡️ **风险控制**: 多层风控机制，保护资金安全
- 📱 **响应式UI**: 支持深浅色主题，移动端适配
- 🔔 **智能通知**: Telegram Bot实时推送交易信息
- 🐳 **容器化部署**: Docker一键部署，简单易用

## 🏗️ 技术架构

- **前端**: React + Vite + Tailwind CSS
- **后端**: FastAPI + SQLite + WebSocket
- **容器化**: Docker + Docker Compose
- **通知**: Telegram Bot
- **加密**: AES-256加密

## 🚀 快速开始

### 环境要求

- Docker & Docker Compose
- Node.js 18+ (开发环境)
- Python 3.9+ (开发环境)

### 一键部署

1. 克隆项目
```bash
git clone <repository-url>
cd lazy
```

2. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入必要配置
```

3. 启动服务
```bash
docker-compose up -d
```

4. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 开发环境

#### 后端开发
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端开发
```bash
cd frontend
npm install
npm run dev
```

## 📋 功能模块

### 🔑 用户系统
- 用户注册/登录
- JWT Token认证
- 密码哈希存储

### 🔐 API密钥管理
- 支持币安/OKX API密钥
- AES加密存储
- 主密码保护机制

### 💹 交易功能
- 现货交易
- 合约交易(永续)
- 手动交易
- 自动化策略

### 🤖 交易策略
- **网格策略**: 设定价格区间自动挂单
- **马丁格尔**: 递增补仓策略
- **偏移挂单**: 自动设置挂单偏移

### 📊 数据分析
- 实时行情展示
- 收益曲线图
- 每日盈亏统计
- 策略表现分析

### 🛡️ 风险控制
- 最大单笔下单限制
- 累计亏损阈值
- 止盈止损设置
- 挂单数量控制
- 下单频率限制

### 🔔 通知系统
- Telegram Bot集成
- 交易成交通知
- 风控触发提醒
- 策略状态更新

## 🔧 配置说明

### 环境变量

```env
# 数据库
DATABASE_URL=sqlite:///./trading.db

# 安全
SECRET_KEY=your-secret-key
MASTER_PASSWORD=your-master-password

# Telegram
TELEGRAM_BOT_TOKEN=your-bot-token

# 交易所API (加密存储)
# 在应用内配置，不在环境变量中明文存储
```

### 主密码设置

首次启动时需要设置主密码，用于加密/解密API密钥：

```bash
# 启动时会提示输入主密码
docker-compose up -d
```

## 📈 使用指南

### 1. 配置API密钥
1. 登录系统
2. 进入"设置"页面
3. 添加交易所API密钥
4. 系统自动加密存储

### 2. 创建交易策略
1. 进入"策略管理"页面
2. 选择策略类型
3. 配置策略参数
4. 启动策略运行

### 3. 监控交易
1. 实时查看持仓状态
2. 监控策略表现
3. 查看交易记录
4. 分析收益图表

## 🛡️ 安全特性

- **密钥加密**: 所有API密钥使用AES-256加密
- **主密码保护**: 密钥解密需要主密码
- **Token认证**: JWT Token保护API访问
- **HTTPS通信**: 生产环境强制HTTPS
- **风险控制**: 多层风控机制防止损失

## 📞 支持与反馈

如有问题或建议，请提交Issue或联系开发团队。

## 📄 许可证

MIT License
