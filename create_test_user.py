#!/usr/bin/env python3
"""
创建测试用户脚本
"""
import sys
import os
import asyncio

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.core.database import get_db, init_database
from backend.app.core.security import get_password_hash
from backend.app.models.user import User
from sqlalchemy.orm import Session

def create_test_user():
    """创建测试用户"""
    print("🔧 初始化数据库...")
    
    try:
        # 初始化数据库
        init_database()
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 检查用户是否已存在
        existing_user = db.query(User).filter(User.username == "admin").first()
        if existing_user:
            print("✅ 测试用户 'admin' 已存在")
            return True
        
        # 创建测试用户
        print("🔧 创建测试用户...")
        hashed_password = get_password_hash("123456")
        
        test_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True,
            is_superuser=True
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print("✅ 测试用户创建成功:")
        print(f"   用户名: admin")
        print(f"   密码: 123456")
        print(f"   邮箱: <EMAIL>")
        print(f"   用户ID: {test_user.id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🧠 个人量化交易平台 - 创建测试用户")
    print("=" * 50)
    
    success = create_test_user()
    
    if success:
        print("\n🎉 测试用户创建完成！")
        print("现在可以使用以下凭据登录:")
        print("用户名: admin")
        print("密码: 123456")
    else:
        print("\n❌ 测试用户创建失败")
        sys.exit(1)
