"use client"

import React from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function AboutPage() {
  return (
    <MainLayout 
      title="关于作者" 
      subtitle="Nico 投资有道，欢迎关注获取更多投资知识"
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src="/avatar.jpg" alt="Nico投资有道" />
                <AvatarFallback>N</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-2xl">Nico投资有道</CardTitle>
                <CardDescription className="text-base">
                  量化交易专家 • 投资理财导师
                </CardDescription>
                <div className="flex gap-2 mt-2">
                  <Badge variant="secondary">量化交易</Badge>
                  <Badge variant="secondary">投资理财</Badge>
                  <Badge variant="secondary">风险管理</Badge>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">个人简介</h3>
              <p className="text-muted-foreground leading-relaxed">
                专注于量化交易和投资理财领域，拥有丰富的市场经验和专业知识。
                致力于为投资者提供专业的交易策略和风险管理建议，帮助大家在投资路上少走弯路。
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">专业领域</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>• 量化交易策略开发与优化</li>
                <li>• 风险管理与资产配置</li>
                <li>• 加密货币市场分析</li>
                <li>• 投资理财教育与培训</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">联系方式</h3>
              <div className="flex gap-3">
                <Button variant="outline">
                  关注微信公众号
                </Button>
                <Button variant="outline">
                  加入交流群
                </Button>
                <Button variant="outline">
                  查看更多内容
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
