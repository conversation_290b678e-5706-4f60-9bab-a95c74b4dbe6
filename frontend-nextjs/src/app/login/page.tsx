"use client"

import React, { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { Eye, EyeOff, LogIn, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuthStore } from "@/store/auth"
import { showApiError, showApiSuccess } from "@/lib/api"

interface LoginForm {
  username: string
  password: string
}

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const { login } = useAuthStore()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>()

  const onSubmit = async (data: LoginForm) => {
    setIsLoading(true)
    try {
      await login(data.username, data.password)
      showApiSuccess("登录成功！")
      router.push("/")
    } catch (error) {
      showApiError(error)
    } finally {
      setIsLoading(false)
    }
  }

  // 演示登录功能
  const handleDemoLogin = async () => {
    setIsLoading(true)
    try {
      // 使用演示账户
      await login("demo", "demo123")
      showApiSuccess("演示登录成功！")
      router.push("/")
    } catch (error) {
      // 如果演示登录失败，直接设置本地状态
      console.log("演示模式：直接设置登录状态")
      localStorage.setItem('access_token', 'demo-token')
      useAuthStore.setState({
        user: {
          id: 1,
          username: "demo",
          email: "<EMAIL>",
          is_active: true,
          created_at: new Date().toISOString()
        },
        token: 'demo-token',
        isAuthenticated: true
      })
      showApiSuccess("演示模式登录成功！")
      router.push("/")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground text-xl font-bold">
              α
            </div>
          </div>
          <CardTitle className="text-2xl">Alpha Tool</CardTitle>
          <CardDescription>
            登录到您的量化交易平台
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                type="text"
                placeholder="输入用户名"
                {...register("username", { 
                  required: "请输入用户名" 
                })}
              />
              {errors.username && (
                <p className="text-sm text-destructive">{errors.username.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="输入密码"
                  {...register("password", { 
                    required: "请输入密码",
                    minLength: { value: 6, message: "密码至少6位" }
                  })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-destructive">{errors.password.message}</p>
              )}
            </div>

            <Button 
              type="submit" 
              className="w-full" 
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  登录中...
                </>
              ) : (
                <>
                  <LogIn className="mr-2 h-4 w-4" />
                  登录
                </>
              )}
            </Button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                或者
              </span>
            </div>
          </div>

          <Button 
            variant="outline" 
            className="w-full" 
            onClick={handleDemoLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                演示登录中...
              </>
            ) : (
              "演示登录"
            )}
          </Button>

          <div className="text-center text-sm text-muted-foreground">
            <p>演示账户信息：</p>
            <p>用户名: demo | 密码: demo123</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
