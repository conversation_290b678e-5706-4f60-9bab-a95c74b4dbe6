"use client"

import React from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { NotificationBanner } from "@/components/ui/notification-banner"
import { FeatureCards } from "@/components/dashboard/feature-cards"

export default function HomePage() {
  return (
    <MainLayout 
      title="首页" 
      subtitle="专为门户交易 Alpha 空投，前往"
    >
      <div className="space-y-6">
        {/* Notification Banners */}
        <div className="space-y-3">
          <NotificationBanner
            type="warning"
            message="$OHM 正在高速发展中，请谨慎投资！"
          />
          <NotificationBanner
            type="info"
            message="BSC 链暂受攻击交易暂停，建议优先使用 BSC 链上的 Alpha 代币！"
          />
          <NotificationBanner
            type="success"
            message="恭喜！专属链接注册成功，终身享受 20% 交易返佣！"
          />
        </div>

        {/* Feature Cards */}
        <FeatureCards />
      </div>
    </MainLayout>
  )
}
