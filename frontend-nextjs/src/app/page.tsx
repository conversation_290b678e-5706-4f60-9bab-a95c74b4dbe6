"use client"

import React from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { NotificationBanner } from "@/components/ui/notification-banner"
import { FeatureCards } from "@/components/dashboard/feature-cards"

export default function HomePage() {
  return (
    <MainLayout 
      title="Alpha Tool" 
      subtitle="专为「币安 Alpha 空投」而生"
    >
      <div className="space-y-6">
        {/* Notification Banners */}
        <div className="space-y-3">
          <NotificationBanner
            type="info"
            message="SOPH 正在流发空投中，速速领取！"
          />
          <NotificationBanner
            type="warning"
            message="BSC 链暂受攻击交易暂停，建议优先阅 BSC 链上的 Alpha 代币！"
          />
          <NotificationBanner
            type="success"
            message="通过 专属链接 注册币安，终身享受 20% 交易返佣！"
          />
        </div>

        {/* Feature Cards */}
        <FeatureCards />
      </div>
    </MainLayout>
  )
}
