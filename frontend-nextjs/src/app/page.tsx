"use client"

import React from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { NotificationBanner } from "@/components/ui/notification-banner"
import { DashboardStats } from "@/components/dashboard/dashboard-stats"
import { RecentTrades } from "@/components/dashboard/recent-trades"
import { MarketOverview } from "@/components/dashboard/market-overview"

export default function HomePage() {
  return (
    <MainLayout
      title="仪表板"
      subtitle="量化交易平台概览"
    >
      <div className="space-y-6">
        {/* Notification Banners */}
        <div className="space-y-3">
          <NotificationBanner
            type="warning"
            message="市场波动较大，请注意风险控制！"
          />
          <NotificationBanner
            type="info"
            message="新增DCA和均线策略，欢迎体验！"
          />
          <NotificationBanner
            type="success"
            message="系统运行正常，所有API连接稳定！"
          />
        </div>

        {/* Dashboard Stats */}
        <DashboardStats />

        {/* Market Overview and Recent Trades */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <MarketOverview />
          <RecentTrades />
        </div>
      </div>
    </MainLayout>
  )
}
