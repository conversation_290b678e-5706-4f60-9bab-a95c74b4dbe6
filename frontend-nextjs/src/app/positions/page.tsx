"use client"

import React from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function PositionsPage() {
  return (
    <MainLayout 
      title="持仓详情" 
      subtitle="查看您的持仓信息和交易记录"
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>持仓概览</CardTitle>
            <CardDescription>
              当前持仓和盈亏情况
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <p className="text-muted-foreground">持仓功能开发中...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
