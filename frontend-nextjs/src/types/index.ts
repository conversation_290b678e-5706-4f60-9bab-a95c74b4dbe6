// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  is_active: boolean
  created_at: string
}

// API密钥类型
export interface APIKey {
  id: number
  name: string
  exchange: string
  api_key: string
  secret_key: string
  passphrase?: string
  is_active: boolean
  created_at: string
}

// 策略类型
export interface Strategy {
  id: number
  name: string
  description?: string
  strategy_type: string
  exchange: string
  symbol: string
  market_type: string
  base_amount: number
  max_amount?: number
  parameters: Record<string, any>
  status: string
  is_active: boolean
  total_orders: number
  total_trades: number
  total_profit: number
  total_loss: number
  win_rate: number
  max_loss?: number
  stop_loss_percentage?: number
  take_profit_percentage?: number
  notes?: string
  created_at: string
  started_at?: string
  last_executed_at?: string
  last_error?: string
}

// 订单类型
export interface Order {
  id: number
  strategy_id: number
  symbol: string
  side: 'buy' | 'sell'
  type: string
  amount: number
  price: number
  filled_amount: number
  status: string
  exchange_order_id?: string
  created_at: string
  filled_at?: string
}

// 交易记录类型
export interface Trade {
  id: number
  strategy_id: number
  symbol: string
  side: 'buy' | 'sell'
  amount: number
  price: number
  fee: number
  profit?: number
  created_at: string
}

// 策略创建表单类型
export interface StrategyCreateForm {
  name: string
  description?: string
  strategy_type: string
  exchange: string
  symbol: string
  market_type: string
  base_amount: number
  max_amount?: number
  parameters: Record<string, any>
  max_loss?: number
  stop_loss_percentage?: number
  take_profit_percentage?: number
  notes?: string
}

// 策略类型信息
export interface StrategyTypeInfo {
  name: string
  description: string
  parameters: Record<string, ParameterInfo>
  risk_level: string
  suitable_market: string[]
  min_capital: number
}

export interface ParameterInfo {
  name: string
  type: 'int' | 'float' | 'select' | 'boolean'
  min?: number
  max?: number
  default: any
  description: string
  options?: Array<{ value: any; label: string }>
}

// 策略验证结果
export interface StrategyValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  capital_requirements?: {
    min_capital: number
    recommended_capital: number
    max_risk: number
  }
}

// 回测请求
export interface BacktestRequest {
  strategy_type: string
  symbol: string
  initial_capital: number
  parameters: Record<string, any>
  start_date: string
  end_date: string
  interval?: string
  fee_rate?: number
}

// 回测结果
export interface BacktestResult {
  total_return: number
  total_return_pct: number
  sharpe_ratio: number
  max_drawdown: number
  win_rate: number
  total_trades: number
  profit_factor: number
  trades: Array<{
    timestamp: string
    side: string
    price: number
    amount: number
    profit: number
  }>
  equity_curve: Array<{
    timestamp: string
    equity: number
    drawdown: number
  }>
}

// 参数优化请求
export interface ParameterOptimizationRequest {
  strategy_type: string
  symbol: string
  initial_capital: number
  parameter_ranges: Record<string, any>
  start_date: string
  end_date: string
  optimization_target: string
}

// 通知类型
export interface Notification {
  id: number
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  is_read: boolean
  created_at: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 过滤器类型
export interface StrategyFilters {
  status?: string
  strategy_type?: string
  exchange?: string
  search?: string
}

export interface OrderFilters {
  strategy_id?: number
  status?: string
  side?: string
  symbol?: string
  date_from?: string
  date_to?: string
}

export interface TradeFilters {
  strategy_id?: number
  side?: string
  symbol?: string
  date_from?: string
  date_to?: string
}

// 统计数据类型
export interface DashboardStats {
  total_strategies: number
  active_strategies: number
  total_profit: number
  total_trades: number
  win_rate: number
  best_strategy: {
    name: string
    profit: number
  }
  recent_trades: Trade[]
}

// 市场数据类型
export interface MarketData {
  symbol: string
  price: number
  change_24h: number
  change_24h_pct: number
  volume_24h: number
  updated_at: string
}

// 持仓类型
export interface Position {
  symbol: string
  amount: number
  avg_price: number
  current_price: number
  unrealized_pnl: number
  unrealized_pnl_pct: number
  strategy_id?: number
  strategy_name?: string
}
