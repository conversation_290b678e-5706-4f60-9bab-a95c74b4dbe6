import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}



export function formatCurrency(
  amount: number,
  currency: string = "USDT",
  decimals: number = 2
): string {
  return `${amount.toFixed(decimals)} ${currency}`
}

export function formatPercentage(value: number, decimals: number = 2): string {
  const sign = value >= 0 ? "+" : ""
  return `${sign}${value.toFixed(decimals)}%`
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  }).format(new Date(date))
}

export function formatNumber(num: number, decimals: number = 2): string {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(decimals) + "B"
  }
  if (num >= 1e6) {
    return (num / 1e6).toFixed(decimals) + "M"
  }
  if (num >= 1e3) {
    return (num / 1e3).toFixed(decimals) + "K"
  }
  return num.toFixed(decimals)
}

export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case "running":
    case "active":
      return "text-success"
    case "stopped":
    case "inactive":
      return "text-muted-foreground"
    case "error":
    case "failed":
      return "text-destructive"
    case "paused":
      return "text-warning"
    default:
      return "text-muted-foreground"
  }
}

export function getProfitColor(profit: number): string {
  if (profit > 0) return "text-success"
  if (profit < 0) return "text-destructive"
  return "text-muted-foreground"
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}
