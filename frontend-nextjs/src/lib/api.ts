import axios, { AxiosInstance, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'
import {
  User,
  APIKey,
  Strategy,
  Order,
  Trade,
  StrategyCreateForm,
  StrategyTypeInfo,
  StrategyValidationResult,
  BacktestRequest,
  BacktestResult,
  ParameterOptimizationRequest,
  Notification,
  ApiResponse,
  PaginatedResponse,
  StrategyFilters,
  OrderFilters,
  TradeFilters,
  DashboardStats,
  MarketData,
  Position
} from '@/types'

// API配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    let token = localStorage.getItem('access_token')

    // 如果没有直接的token，尝试从auth-storage中获取（兼容Zustand persist）
    if (!token) {
      const authStorage = localStorage.getItem('auth-storage')
      if (authStorage) {
        try {
          const authData = JSON.parse(authStorage)
          token = authData.state?.token
        } catch (error) {
          console.error('解析auth-storage失败:', error)
        }
      }
    }

    // 如果还是没有token，使用debug-token（仅开发环境）
    if (!token && process.env.NODE_ENV === 'development') {
      token = 'debug-token'
    }

    if (token && token !== 'debug-token') {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    } else if (error.response?.status >= 500) {
      toast.error('服务器错误，请稍后重试')
    } else if (error.response?.data?.message) {
      toast.error(error.response.data.message)
    }
    return Promise.reject(error)
  }
)

// 认证API
export const authApi = {
  login: async (username: string, password: string): Promise<{ access_token: string; user: User }> => {
    // 使用FormData格式，符合OAuth2PasswordRequestForm要求
    const formData = new FormData()
    formData.append('username', username)
    formData.append('password', password)

    const response = await apiClient.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    return response.data
  },

  register: async (username: string, email: string, password: string): Promise<User> => {
    const response = await apiClient.post('/auth/register', { username, email, password })
    return response.data
  },

  getCurrentUser: async (): Promise<User> => {
    const response = await apiClient.get('/users/profile')
    return response.data
  },

  logout: async (): Promise<void> => {
    await apiClient.post('/auth/logout')
    localStorage.removeItem('access_token')
  },
}

// API密钥管理API
export const apiKeyApi = {
  getAll: async (): Promise<APIKey[]> => {
    const response = await apiClient.get('/api-keys')
    return response.data
  },

  create: async (data: Omit<APIKey, 'id' | 'created_at'>): Promise<APIKey> => {
    const response = await apiClient.post('/api-keys', data)
    return response.data
  },

  update: async (id: number, data: Partial<APIKey>): Promise<APIKey> => {
    const response = await apiClient.put(`/api-keys/${id}`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/api-keys/${id}`)
  },

  test: async (id: number): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post(`/api-keys/${id}/test`)
    return response.data
  },
}

// 策略管理API
export const strategyApi = {
  getAll: async (filters?: StrategyFilters): Promise<Strategy[]> => {
    const response = await apiClient.get('/strategies', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<Strategy> => {
    const response = await apiClient.get(`/strategies/${id}`)
    return response.data
  },

  create: async (data: StrategyCreateForm): Promise<Strategy> => {
    const response = await apiClient.post('/strategies', data)
    return response.data
  },

  update: async (id: number, data: Partial<StrategyCreateForm>): Promise<Strategy> => {
    const response = await apiClient.put(`/strategies/${id}`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/strategies/${id}`)
  },

  start: async (id: number): Promise<void> => {
    await apiClient.post(`/strategies/${id}/start`)
  },

  stop: async (id: number): Promise<void> => {
    await apiClient.post(`/strategies/${id}/stop`)
  },

  getTypes: async (): Promise<Record<string, StrategyTypeInfo>> => {
    const response = await apiClient.get('/strategies/types')
    return response.data
  },

  validate: async (strategyType: string, parameters: any): Promise<StrategyValidationResult> => {
    const response = await apiClient.post('/strategies/validate', {
      strategy_type: strategyType,
      parameters: parameters
    })
    return response.data
  },

  backtest: async (request: BacktestRequest): Promise<BacktestResult> => {
    const response = await apiClient.post('/strategies/backtest', request)
    return response.data.result
  },

  optimize: async (request: ParameterOptimizationRequest): Promise<any> => {
    const response = await apiClient.post('/strategies/optimize', request)
    return response.data.result
  },
}

// 订单管理API
export const orderApi = {
  getAll: async (filters?: OrderFilters): Promise<PaginatedResponse<Order>> => {
    const response = await apiClient.get('/orders', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<Order> => {
    const response = await apiClient.get(`/orders/${id}`)
    return response.data
  },

  cancel: async (id: number): Promise<void> => {
    await apiClient.post(`/orders/${id}/cancel`)
  },
}

// 交易记录API
export const tradeApi = {
  getAll: async (filters?: TradeFilters): Promise<PaginatedResponse<Trade>> => {
    const response = await apiClient.get('/trades', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<Trade> => {
    const response = await apiClient.get(`/trades/${id}`)
    return response.data
  },

  getByStrategy: async (strategyId: number): Promise<Trade[]> => {
    const response = await apiClient.get(`/strategies/${strategyId}/trades`)
    return response.data
  },
}

// 通知API
export const notificationApi = {
  getAll: async (): Promise<Notification[]> => {
    const response = await apiClient.get('/notifications')
    return response.data
  },

  markAsRead: async (id: number): Promise<void> => {
    await apiClient.put(`/notifications/${id}/read`)
  },

  markAllAsRead: async (): Promise<void> => {
    await apiClient.put('/notifications/read-all')
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/notifications/${id}`)
  },
}

// 仪表板API
export const dashboardApi = {
  getStats: async (): Promise<DashboardStats> => {
    const response = await apiClient.get('/dashboard/stats')
    return response.data
  },

  getMarketData: async (symbols?: string[]): Promise<MarketData[]> => {
    const response = await apiClient.get('/dashboard/market-data', {
      params: { symbols: symbols?.join(',') }
    })
    return response.data
  },

  getPositions: async (): Promise<Position[]> => {
    const response = await apiClient.get('/dashboard/positions')
    return response.data
  },
}

// 工具函数
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message
  } else if (error.message) {
    return error.message
  } else {
    return '未知错误'
  }
}

export const showApiError = (error: any): void => {
  const message = handleApiError(error)
  toast.error(message)
}

export const showApiSuccess = (message: string): void => {
  toast.success(message)
}

export default apiClient
