import { create } from 'zustand'
import { Strategy, StrategyFilters, StrategyTypeInfo } from '@/types'
import { strategyApi } from '@/lib/api'

interface StrategyState {
  strategies: Strategy[]
  strategyTypes: Record<string, StrategyTypeInfo>
  filters: StrategyFilters
  isLoading: boolean
  selectedStrategy: Strategy | null
  
  // Actions
  fetchStrategies: () => Promise<void>
  fetchStrategyTypes: () => Promise<void>
  createStrategy: (data: any) => Promise<Strategy>
  updateStrategy: (id: number, data: any) => Promise<Strategy>
  deleteStrategy: (id: number) => Promise<void>
  startStrategy: (id: number) => Promise<void>
  stopStrategy: (id: number) => Promise<void>
  setFilters: (filters: StrategyFilters) => void
  setSelectedStrategy: (strategy: Strategy | null) => void
  refreshStrategy: (id: number) => Promise<void>
}

export const useStrategyStore = create<StrategyState>((set, get) => ({
  strategies: [],
  strategyTypes: {},
  filters: {},
  isLoading: false,
  selectedStrategy: null,

  fetchStrategies: async () => {
    set({ isLoading: true })
    try {
      const strategies = await strategyApi.getAll(get().filters)
      set({ strategies, isLoading: false })
    } catch (error) {
      set({ isLoading: false })
      throw error
    }
  },

  fetchStrategyTypes: async () => {
    try {
      const strategyTypes = await strategyApi.getTypes()
      set({ strategyTypes })
    } catch (error) {
      throw error
    }
  },

  createStrategy: async (data: any) => {
    const strategy = await strategyApi.create(data)
    set(state => ({
      strategies: [...state.strategies, strategy]
    }))
    return strategy
  },

  updateStrategy: async (id: number, data: any) => {
    const strategy = await strategyApi.update(id, data)
    set(state => ({
      strategies: state.strategies.map(s => s.id === id ? strategy : s),
      selectedStrategy: state.selectedStrategy?.id === id ? strategy : state.selectedStrategy
    }))
    return strategy
  },

  deleteStrategy: async (id: number) => {
    await strategyApi.delete(id)
    set(state => ({
      strategies: state.strategies.filter(s => s.id !== id),
      selectedStrategy: state.selectedStrategy?.id === id ? null : state.selectedStrategy
    }))
  },

  startStrategy: async (id: number) => {
    await strategyApi.start(id)
    await get().refreshStrategy(id)
  },

  stopStrategy: async (id: number) => {
    await strategyApi.stop(id)
    await get().refreshStrategy(id)
  },

  setFilters: (filters: StrategyFilters) => {
    set({ filters })
  },

  setSelectedStrategy: (strategy: Strategy | null) => {
    set({ selectedStrategy: strategy })
  },

  refreshStrategy: async (id: number) => {
    try {
      const strategy = await strategyApi.getById(id)
      set(state => ({
        strategies: state.strategies.map(s => s.id === id ? strategy : s),
        selectedStrategy: state.selectedStrategy?.id === id ? strategy : state.selectedStrategy
      }))
    } catch (error) {
      throw error
    }
  },
}))
