"use client"

import React, { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuthStore } from "@/store/auth"
import { Loader2 } from "lucide-react"

interface AuthProviderProps {
  children: React.ReactNode
}

// 不需要认证的路由
const publicRoutes = ['/login', '/register']

export function AuthProvider({ children }: AuthProviderProps) {
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, getCurrentUser } = useAuthStore()

  useEffect(() => {
    const checkAuth = async () => {
      // 如果是公开路由，直接显示
      if (publicRoutes.includes(pathname)) {
        setIsLoading(false)
        return
      }

      // 检查本地存储的token
      const token = localStorage.getItem('access_token')
      
      if (!token) {
        // 没有token，跳转到登录页
        router.push('/login')
        setIsLoading(false)
        return
      }

      if (!isAuthenticated) {
        try {
          // 尝试获取用户信息
          await getCurrentUser()
        } catch (error) {
          // 获取用户信息失败，清除token并跳转到登录页
          localStorage.removeItem('access_token')
          router.push('/login')
          setIsLoading(false)
          return
        }
      }

      setIsLoading(false)
    }

    checkAuth()
  }, [pathname, isAuthenticated, getCurrentUser, router])

  // 如果正在加载，显示加载界面
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  // 如果是公开路由或已认证，显示内容
  if (publicRoutes.includes(pathname) || isAuthenticated) {
    return <>{children}</>
  }

  // 其他情况不显示内容（会被重定向）
  return null
}
