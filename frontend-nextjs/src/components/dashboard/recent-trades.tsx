"use client"

import React, { useEffect, useState } from "react"
import { <PERSON>UpRight, ArrowDownRight, Clock } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { tradeApi, showApiError } from "@/lib/api"
import { Trade } from "@/types"
import { formatCurrency, formatDate, getProfitColor } from "@/lib/utils"

export function RecentTrades() {
  const [trades, setTrades] = useState<Trade[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchRecentTrades = async () => {
      try {
        const response = await tradeApi.getAll({ page: 1, size: 10 })
        setTrades(response.items || [])
      } catch (error) {
        console.error('获取交易记录失败:', error)
        // 使用模拟数据作为后备
        setTrades([
          {
            id: 1,
            strategy_id: 1,
            symbol: "BTCUSDT",
            side: "buy",
            amount: 0.001,
            price: 43250.50,
            fee: 0.43,
            profit: 12.50,
            created_at: new Date().toISOString()
          },
          {
            id: 2,
            strategy_id: 2,
            symbol: "ETHUSDT",
            side: "sell",
            amount: 0.05,
            price: 2650.75,
            fee: 0.13,
            profit: -5.25,
            created_at: new Date(Date.now() - 3600000).toISOString()
          },
          {
            id: 3,
            strategy_id: 1,
            symbol: "BTCUSDT",
            side: "sell",
            amount: 0.001,
            price: 43380.25,
            fee: 0.43,
            profit: 25.75,
            created_at: new Date(Date.now() - 7200000).toISOString()
          }
        ])
      } finally {
        setIsLoading(false)
      }
    }

    fetchRecentTrades()
  }, [])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>最近交易</CardTitle>
          <CardDescription>最新的交易记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-muted rounded-full"></div>
                  <div>
                    <div className="h-4 bg-muted rounded w-20 mb-1"></div>
                    <div className="h-3 bg-muted rounded w-16"></div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="h-4 bg-muted rounded w-16 mb-1"></div>
                  <div className="h-3 bg-muted rounded w-12"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>最近交易</CardTitle>
        <CardDescription>最新的交易记录</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {!trades || trades.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>暂无交易记录</p>
            </div>
          ) : (
            trades.map((trade) => (
              <div key={trade.id} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`flex h-8 w-8 items-center justify-center rounded-full ${
                    trade.side === 'buy'
                      ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                      : 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400'
                  }`}>
                    {trade.side === 'buy' ? (
                      <ArrowUpRight className="h-4 w-4" />
                    ) : (
                      <ArrowDownRight className="h-4 w-4" />
                    )}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{trade.symbol}</span>
                      <Badge variant="outline" className="text-xs">
                        {trade.side === 'buy' ? '买入' : '卖出'}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {trade.amount} @ {formatCurrency(trade.price)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  {trade.profit !== undefined && (
                    <div className={`font-medium ${getProfitColor(trade.profit)}`}>
                      {trade.profit >= 0 ? '+' : ''}{formatCurrency(trade.profit)}
                    </div>
                  )}
                  <div className="text-sm text-muted-foreground">
                    {formatDate(trade.created_at)}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
