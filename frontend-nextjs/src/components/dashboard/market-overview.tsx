"use client"

import React, { useEffect, useState } from "react"
import { TrendingUp, TrendingDown, Activity } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { dashboardApi, showApiError } from "@/lib/api"
import { MarketData } from "@/types"
import { formatCurrency, formatPercentage, getProfitColor } from "@/lib/utils"

export function MarketOverview() {
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        const data = await dashboardApi.getMarketData(['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT'])
        setMarketData(data)
      } catch (error) {
        showApiError(error)
        // 使用模拟数据作为后备
        setMarketData([
          {
            symbol: "BTCUSDT",
            price: 43250.50,
            change_24h: 1250.75,
            change_24h_pct: 2.98,
            volume_24h: 28450000000,
            updated_at: new Date().toISOString()
          },
          {
            symbol: "ETHUSDT",
            price: 2650.75,
            change_24h: -85.25,
            change_24h_pct: -3.11,
            volume_24h: 15230000000,
            updated_at: new Date().toISOString()
          },
          {
            symbol: "BNBUSDT",
            price: 315.80,
            change_24h: 12.45,
            change_24h_pct: 4.10,
            volume_24h: 1850000000,
            updated_at: new Date().toISOString()
          },
          {
            symbol: "ADAUSDT",
            price: 0.4825,
            change_24h: 0.0125,
            change_24h_pct: 2.66,
            volume_24h: 425000000,
            updated_at: new Date().toISOString()
          }
        ])
      } finally {
        setIsLoading(false)
      }
    }

    fetchMarketData()
    
    // 每30秒更新一次市场数据
    const interval = setInterval(fetchMarketData, 30000)
    return () => clearInterval(interval)
  }, [])

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>市场概览</CardTitle>
          <CardDescription>主要交易对价格动态</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center justify-between animate-pulse">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-muted rounded-full"></div>
                  <div>
                    <div className="h-4 bg-muted rounded w-20 mb-1"></div>
                    <div className="h-3 bg-muted rounded w-16"></div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="h-4 bg-muted rounded w-20 mb-1"></div>
                  <div className="h-3 bg-muted rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          市场概览
        </CardTitle>
        <CardDescription>主要交易对价格动态</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {marketData.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>暂无市场数据</p>
            </div>
          ) : (
            marketData.map((market) => (
              <div key={market.symbol} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                    <span className="text-xs font-bold text-primary">
                      {market.symbol.replace('USDT', '').slice(0, 2)}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium">{market.symbol}</div>
                    <div className="text-sm text-muted-foreground">
                      Vol: {(market.volume_24h / 1000000000).toFixed(2)}B
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">
                    ${market.price.toLocaleString()}
                  </div>
                  <div className="flex items-center gap-1">
                    {market.change_24h_pct >= 0 ? (
                      <TrendingUp className="h-3 w-3 text-green-600" />
                    ) : (
                      <TrendingDown className="h-3 w-3 text-red-600" />
                    )}
                    <span className={`text-sm font-medium ${getProfitColor(market.change_24h_pct)}`}>
                      {formatPercentage(market.change_24h_pct)}
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        
        {marketData.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>最后更新</span>
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
