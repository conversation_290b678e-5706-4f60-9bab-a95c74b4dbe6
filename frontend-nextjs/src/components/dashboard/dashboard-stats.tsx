"use client"

import React, { useEffect, useState } from "react"
import { TrendingUp, TrendingDown, Activity, Target, DollarSign, BarChart3 } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { dashboardApi, showApiError } from "@/lib/api"
import { DashboardStats as DashboardStatsType } from "@/types"
import { formatCurrency, formatPercentage, getProfitColor } from "@/lib/utils"

export function DashboardStats() {
  const [stats, setStats] = useState<DashboardStatsType | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await dashboardApi.getStats()
        setStats(data)
      } catch (error) {
        console.error('获取统计数据失败:', error)
        // 使用模拟数据作为后备
        setStats({
          total_strategies: 8,
          active_strategies: 5,
          total_profit: 2450.75,
          total_trades: 156,
          win_rate: 73.2,
          best_strategy: {
            name: "BTC网格策略",
            profit: 1250.50
          },
          recent_trades: []
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-muted rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-muted rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) return null

  const profitColor = getProfitColor(stats.total_profit)

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
      {/* 总策略数 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总策略数</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total_strategies}</div>
          <p className="text-xs text-muted-foreground">
            活跃策略: {stats.active_strategies}
          </p>
        </CardContent>
      </Card>

      {/* 活跃策略 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">活跃策略</CardTitle>
          <Activity className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.active_strategies}</div>
          <p className="text-xs text-muted-foreground">
            运行中的策略数量
          </p>
        </CardContent>
      </Card>

      {/* 总盈亏 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总盈亏</CardTitle>
          {stats.total_profit >= 0 ? (
            <TrendingUp className="h-4 w-4 text-green-600" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-600" />
          )}
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${profitColor}`}>
            {formatCurrency(stats.total_profit)}
          </div>
          <p className="text-xs text-muted-foreground">
            累计盈亏金额
          </p>
        </CardContent>
      </Card>

      {/* 总交易数 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总交易数</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total_trades}</div>
          <p className="text-xs text-muted-foreground">
            累计交易次数
          </p>
        </CardContent>
      </Card>

      {/* 胜率 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">胜率</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.win_rate.toFixed(1)}%</div>
          <p className="text-xs text-muted-foreground">
            盈利交易占比
          </p>
        </CardContent>
      </Card>

      {/* 最佳策略 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">最佳策略</CardTitle>
          <Badge variant="secondary" className="text-xs">
            TOP
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="text-sm font-medium truncate mb-1">
            {stats.best_strategy.name}
          </div>
          <div className={`text-lg font-bold ${getProfitColor(stats.best_strategy.profit)}`}>
            {formatCurrency(stats.best_strategy.profit)}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
