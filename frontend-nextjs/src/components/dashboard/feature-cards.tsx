"use client"

import React from "react"
import {
  TrendingUp,
  BarChart3,
  History,
  Users,
  BookOpen
} from "lucide-react"
import Link from "next/link"

const features = [
  {
    icon: TrendingUp,
    title: "最佳实践",
    description: "参与币安 Alpha 空投的最佳实践，包含视频教程与文字教程",
    href: "/strategies",
    iconBg: "bg-blue-100",
    iconColor: "text-blue-500",
  },
  {
    icon: BarChart3,
    title: "积分计算器",
    description: "根据当前余额与目标积分，快速计算每日所需交易量。",
    href: "/positions",
    iconBg: "bg-indigo-100",
    iconColor: "text-indigo-500",
  },
  {
    icon: History,
    title: "空投历史",
    description: "查看历史空投 Alpha 空投数据，包含领取门槛、单号收益、历史曲线等。",
    href: "/history",
    iconBg: "bg-purple-100",
    iconColor: "text-purple-500",
  },
  {
    icon: BookOpen,
    title: "规则详解",
    description: "详细解读币安 Alpha 积分与空投规则，帮助你快速入门。",
    href: "/rules",
    iconBg: "bg-orange-100",
    iconColor: "text-orange-500",
  },
  {
    icon: Users,
    title: "关于作者",
    description: "Nico 投资有道，欢迎关注订阅。",
    href: "/about",
    iconBg: "bg-sky-100",
    iconColor: "text-sky-500",
  },
]

export function FeatureCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {features.map((feature, index) => (
        <Link 
          key={index} 
          href={feature.href}
          className="flex flex-col p-6 bg-white rounded-xl border border-gray-100 hover:shadow-md transition-all"
        >
          <div className="flex items-center gap-4 mb-4">
            <div className={`flex h-12 w-12 items-center justify-center rounded-full ${feature.iconBg}`}>
              <feature.icon className={`h-6 w-6 ${feature.iconColor}`} />
            </div>
            <h3 className="text-lg font-medium">{feature.title}</h3>
          </div>
          <p className="text-sm text-gray-500 leading-relaxed">
            {feature.description}
          </p>
        </Link>
      ))}
    </div>
  )
}
