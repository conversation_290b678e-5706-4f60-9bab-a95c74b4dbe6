"use client"

import React from "react"
import { 
  TrendingUp, 
  BarChart3, 
  History, 
  Users,
  BookOpen,
  Target
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

const features = [
  {
    icon: TrendingUp,
    title: "最佳实践",
    description: "学习 Alpha 交易的最佳实践，包含策略解析与交易技巧",
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-950",
  },
  {
    icon: BarChart3,
    title: "积分计算器",
    description: "通过积分计算器和积分策略，快速计算最佳交易时机",
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-950",
  },
  {
    icon: History,
    title: "空投历史",
    description: "查看历史空投记录，包含空投时间、数量、价值等详细信息",
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-950",
  },
  {
    icon: BookOpen,
    title: "规则详解",
    description: "详细解读 Alpha 交易规则和策略，帮助您更好地理解交易机制",
    color: "text-orange-600",
    bgColor: "bg-orange-50 dark:bg-orange-950",
  },
  {
    icon: Users,
    title: "关于作者",
    description: "Nico 投资有道，欢迎关注我们，获取更多投资理财知识",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50 dark:bg-indigo-950",
  },
]

export function FeatureCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {features.map((feature, index) => (
        <Card key={index} className="feature-card group">
          <CardHeader className="pb-4">
            <div className={`inline-flex h-12 w-12 items-center justify-center rounded-lg ${feature.bgColor} mb-4`}>
              <feature.icon className={`h-6 w-6 ${feature.color}`} />
            </div>
            <CardTitle className="text-lg">{feature.title}</CardTitle>
            <CardDescription className="text-sm leading-relaxed">
              {feature.description}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <Button 
              variant="ghost" 
              size="sm" 
              className="group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
            >
              了解更多
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
