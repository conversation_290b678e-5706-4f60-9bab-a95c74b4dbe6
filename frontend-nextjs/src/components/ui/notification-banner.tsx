"use client"

import React, { useState } from "react"
import { X, Volume2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface NotificationBannerProps {
  type?: "info" | "warning" | "success" | "error"
  message: string
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
}

const colorMap = {
  info: "bg-blue-50 border-blue-100 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800/30 dark:text-blue-300",
  warning: "bg-yellow-50 border-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800/30 dark:text-yellow-300",
  success: "bg-green-50 border-green-100 text-green-800 dark:bg-green-900/20 dark:border-green-800/30 dark:text-green-300", 
  error: "bg-red-50 border-red-100 text-red-800 dark:bg-red-900/20 dark:border-red-800/30 dark:text-red-300",
}

export function NotificationBanner({
  type = "info",
  message,
  dismissible = true,
  onDismiss,
  className,
}: NotificationBannerProps) {
  const [isVisible, setIsVisible] = useState(true)

  const handleDismiss = () => {
    setIsVisible(false)
    onDismiss?.()
  }

  if (!isVisible) return null

  return (
    <div
      className={cn(
        "flex items-center gap-3 rounded-lg border px-4 py-3 text-sm",
        colorMap[type],
        className
      )}
    >
      <Volume2 className="h-4 w-4 flex-shrink-0" />
      <div className="flex-1 min-w-0">
        <div className="text-sm">{message}</div>
      </div>
      {dismissible && (
        <Button
          variant="ghost"
          size="icon"
          className="h-5 w-5 rounded-full opacity-70 hover:opacity-100 hover:bg-transparent"
          onClick={handleDismiss}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}
