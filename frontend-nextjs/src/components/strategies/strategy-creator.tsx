"use client"

import React, { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { X, ChevronLeft, ChevronRight, Check, AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useStrategyStore } from "@/store/strategy"
import { StrategyCreateForm, StrategyTypeInfo, ParameterInfo } from "@/types"
import { strategyApi, showApiError, showApiSuccess } from "@/lib/api"
import { cn } from "@/lib/utils"

interface StrategyCreatorProps {
  onClose: () => void
  onSuccess: () => void
}

const steps = [
  { id: 1, name: "基础信息", description: "配置策略基本信息" },
  { id: 2, name: "策略参数", description: "设置策略具体参数" },
  { id: 3, name: "风险管理", description: "配置风险控制参数" },
  { id: 4, name: "确认创建", description: "确认并创建策略" },
]

export function StrategyCreator({ onClose, onSuccess }: StrategyCreatorProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [validationResult, setValidationResult] = useState<any>(null)
  
  const { strategyTypes, fetchStrategyTypes } = useStrategyStore()
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset,
  } = useForm<StrategyCreateForm>({
    defaultValues: {
      market_type: "spot",
      base_amount: 100,
    }
  })

  const watchedStrategyType = watch("strategy_type")
  const watchedParameters = watch("parameters") || {}

  useEffect(() => {
    fetchStrategyTypes().catch(showApiError)
  }, [fetchStrategyTypes])

  const selectedStrategyInfo = strategyTypes[watchedStrategyType]

  const validateCurrentStep = async () => {
    if (currentStep === 2 && watchedStrategyType && Object.keys(watchedParameters).length > 0) {
      try {
        const result = await strategyApi.validate(watchedStrategyType, watchedParameters)
        setValidationResult(result)
        return result.valid
      } catch (error) {
        showApiError(error)
        return false
      }
    }
    return true
  }

  const nextStep = async () => {
    const isValid = await validateCurrentStep()
    if (isValid && currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const onSubmit = async (data: StrategyCreateForm) => {
    setIsLoading(true)
    try {
      await strategyApi.create(data)
      showApiSuccess("策略创建成功！")
      onSuccess()
      onClose()
    } catch (error) {
      showApiError(error)
    } finally {
      setIsLoading(false)
    }
  }

  const renderParameterInput = (key: string, param: ParameterInfo) => {
    const value = watchedParameters[key] ?? param.default

    switch (param.type) {
      case 'select':
        return (
          <Select
            value={value?.toString()}
            onValueChange={(val) => {
              const newParams = { ...watchedParameters }
              newParams[key] = param.options?.find(opt => opt.value.toString() === val)?.value ?? val
              setValue("parameters", newParams)
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder={`选择${param.name}`} />
            </SelectTrigger>
            <SelectContent>
              {param.options?.map((option) => (
                <SelectItem key={option.value.toString()} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      
      case 'boolean':
        return (
          <Select
            value={value?.toString()}
            onValueChange={(val) => {
              const newParams = { ...watchedParameters }
              newParams[key] = val === 'true'
              setValue("parameters", newParams)
            }}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">是</SelectItem>
              <SelectItem value="false">否</SelectItem>
            </SelectContent>
          </Select>
        )
      
      default:
        return (
          <Input
            type={param.type === 'int' ? 'number' : param.type === 'float' ? 'number' : 'text'}
            step={param.type === 'float' ? '0.01' : '1'}
            min={param.min}
            max={param.max}
            value={value ?? ''}
            onChange={(e) => {
              const newParams = { ...watchedParameters }
              let val: any = e.target.value
              if (param.type === 'int') val = parseInt(val) || 0
              if (param.type === 'float') val = parseFloat(val) || 0
              newParams[key] = val
              setValue("parameters", newParams)
            }}
            placeholder={param.description}
          />
        )
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="border-b">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>创建策略</CardTitle>
              <CardDescription>
                步骤 {currentStep} / {steps.length}: {steps[currentStep - 1].description}
              </CardDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {/* 步骤指示器 */}
          <div className="flex items-center justify-between mt-6">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                    currentStep >= step.id
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                  )}
                >
                  {currentStep > step.id ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    step.id
                  )}
                </div>
                <div className="ml-2 hidden sm:block">
                  <div className="text-sm font-medium">{step.name}</div>
                </div>
                {index < steps.length - 1 && (
                  <div className="w-8 h-px bg-border mx-4" />
                )}
              </div>
            ))}
          </div>
        </CardHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="p-6">
            {/* 步骤 1: 基础信息 */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">策略名称 *</Label>
                    <Input
                      id="name"
                      {...register("name", { required: "请输入策略名称" })}
                      placeholder="输入策略名称"
                    />
                    {errors.name && (
                      <p className="text-sm text-destructive">{errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="strategy_type">策略类型 *</Label>
                    <Select
                      value={watchedStrategyType}
                      onValueChange={(value) => setValue("strategy_type", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择策略类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(strategyTypes).map(([key, info]) => (
                          <SelectItem key={key} value={key}>
                            {info.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.strategy_type && (
                      <p className="text-sm text-destructive">{errors.strategy_type.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="exchange">交易所 *</Label>
                    <Select
                      value={watch("exchange")}
                      onValueChange={(value) => setValue("exchange", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择交易所" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="binance">Binance</SelectItem>
                        <SelectItem value="okx">OKX</SelectItem>
                        <SelectItem value="huobi">Huobi</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="symbol">交易对 *</Label>
                    <Input
                      id="symbol"
                      {...register("symbol", { required: "请输入交易对" })}
                      placeholder="如: BTCUSDT"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="base_amount">基础投入金额 *</Label>
                    <Input
                      id="base_amount"
                      type="number"
                      step="0.01"
                      {...register("base_amount", { 
                        required: "请输入基础投入金额",
                        min: { value: 1, message: "金额必须大于0" }
                      })}
                      placeholder="输入基础投入金额"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="max_amount">最大投入金额</Label>
                    <Input
                      id="max_amount"
                      type="number"
                      step="0.01"
                      {...register("max_amount")}
                      placeholder="输入最大投入金额（可选）"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">策略描述</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="输入策略描述（可选）"
                    rows={3}
                  />
                </div>

                {selectedStrategyInfo && (
                  <Card className="bg-muted/50">
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2">{selectedStrategyInfo.name}</h4>
                      <p className="text-sm text-muted-foreground mb-3">
                        {selectedStrategyInfo.description}
                      </p>
                      <div className="flex gap-2 flex-wrap">
                        <Badge variant="outline">
                          风险等级: {selectedStrategyInfo.risk_level}
                        </Badge>
                        <Badge variant="outline">
                          最小资金: ${selectedStrategyInfo.min_capital}
                        </Badge>
                        <Badge variant="outline">
                          适用市场: {selectedStrategyInfo.suitable_market.join(", ")}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* 步骤 2: 策略参数 */}
            {currentStep === 2 && selectedStrategyInfo && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">策略参数配置</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {Object.entries(selectedStrategyInfo.parameters).map(([key, param]) => (
                      <div key={key} className="space-y-2">
                        <Label htmlFor={key}>
                          {param.name}
                          {param.min !== undefined && param.max !== undefined && (
                            <span className="text-xs text-muted-foreground ml-1">
                              ({param.min} - {param.max})
                            </span>
                          )}
                        </Label>
                        {renderParameterInput(key, param)}
                        <p className="text-xs text-muted-foreground">
                          {param.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {validationResult && (
                  <Card className={validationResult.valid ? "border-green-200" : "border-red-200"}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        {validationResult.valid ? (
                          <Check className="h-5 w-5 text-green-600 mt-0.5" />
                        ) : (
                          <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                        )}
                        <div className="flex-1">
                          <h4 className="font-medium mb-2">
                            {validationResult.valid ? "参数验证通过" : "参数验证失败"}
                          </h4>
                          {validationResult.errors?.length > 0 && (
                            <ul className="text-sm text-red-600 space-y-1">
                              {validationResult.errors.map((error: string, index: number) => (
                                <li key={index}>• {error}</li>
                              ))}
                            </ul>
                          )}
                          {validationResult.warnings?.length > 0 && (
                            <ul className="text-sm text-yellow-600 space-y-1 mt-2">
                              {validationResult.warnings.map((warning: string, index: number) => (
                                <li key={index}>• {warning}</li>
                              ))}
                            </ul>
                          )}
                          {validationResult.capital_requirements && (
                            <div className="mt-3 p-3 bg-muted rounded-lg">
                              <h5 className="font-medium text-sm mb-2">资金需求评估</h5>
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="text-muted-foreground">最小资金:</span>
                                  <span className="ml-2 font-medium">
                                    ${validationResult.capital_requirements.min_capital}
                                  </span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">建议资金:</span>
                                  <span className="ml-2 font-medium">
                                    ${validationResult.capital_requirements.recommended_capital}
                                  </span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* 步骤 3: 风险管理 */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium">风险管理配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="max_loss">最大亏损金额</Label>
                    <Input
                      id="max_loss"
                      type="number"
                      step="0.01"
                      {...register("max_loss")}
                      placeholder="输入最大亏损金额（可选）"
                    />
                    <p className="text-xs text-muted-foreground">
                      达到此亏损金额时自动停止策略
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="stop_loss_percentage">止损百分比</Label>
                    <Input
                      id="stop_loss_percentage"
                      type="number"
                      step="0.01"
                      {...register("stop_loss_percentage")}
                      placeholder="输入止损百分比（可选）"
                    />
                    <p className="text-xs text-muted-foreground">
                      单笔交易的止损百分比
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="take_profit_percentage">止盈百分比</Label>
                    <Input
                      id="take_profit_percentage"
                      type="number"
                      step="0.01"
                      {...register("take_profit_percentage")}
                      placeholder="输入止盈百分比（可选）"
                    />
                    <p className="text-xs text-muted-foreground">
                      单笔交易的止盈百分比
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">备注</Label>
                  <Textarea
                    id="notes"
                    {...register("notes")}
                    placeholder="输入策略备注（可选）"
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* 步骤 4: 确认创建 */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium">确认策略信息</h3>
                <Card>
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">策略名称:</span>
                        <span className="ml-2 font-medium">{watch("name")}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">策略类型:</span>
                        <span className="ml-2 font-medium">
                          {selectedStrategyInfo?.name}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">交易所:</span>
                        <span className="ml-2 font-medium">{watch("exchange")}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">交易对:</span>
                        <span className="ml-2 font-medium">{watch("symbol")}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">基础投入:</span>
                        <span className="ml-2 font-medium">${watch("base_amount")}</span>
                      </div>
                      {watch("max_amount") && (
                        <div>
                          <span className="text-muted-foreground">最大投入:</span>
                          <span className="ml-2 font-medium">${watch("max_amount")}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </CardContent>

          <div className="flex items-center justify-between p-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              上一步
            </Button>

            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              {currentStep < steps.length ? (
                <Button type="button" onClick={nextStep}>
                  下一步
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "创建中..." : "创建策略"}
                </Button>
              )}
            </div>
          </div>
        </form>
      </Card>
    </div>
  )
}
