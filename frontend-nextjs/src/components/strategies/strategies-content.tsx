"use client"

import React, { useState, useEffect } from "react"
import { Plus, Search, Filter, MoreHorizontal, Play, Square, Edit, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StrategyCreator } from "./strategy-creator"
import { useStrategyStore } from "@/store/strategy"
import { showApiError, showApiSuccess } from "@/lib/api"
import { formatCurrency, formatPercentage, getProfitColor } from "@/lib/utils"



const strategyTypeMap = {
  grid: { label: "网格策略", color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" },
  martingale: { label: "马丁格尔", color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200" },
  dca: { label: "定投策略", color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" },
  ma: { label: "均线策略", color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200" },
}

const statusMap = {
  running: { label: "运行中", color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" },
  stopped: { label: "已停止", color: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200" },
  paused: { label: "已暂停", color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" },
  error: { label: "错误", color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" },
}

export function StrategiesContent() {
  const [searchTerm, setSearchTerm] = useState("")
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  const {
    strategies,
    isLoading,
    fetchStrategies,
    deleteStrategy,
    startStrategy,
    stopStrategy,
    setFilters
  } = useStrategyStore()

  useEffect(() => {
    fetchStrategies().catch(showApiError)
  }, [fetchStrategies])

  useEffect(() => {
    setFilters({ search: searchTerm })
  }, [searchTerm, setFilters])

  const handleDeleteStrategy = async (id: number) => {
    if (confirm("确定要删除这个策略吗？")) {
      try {
        await deleteStrategy(id)
        showApiSuccess("策略删除成功")
      } catch (error) {
        showApiError(error)
      }
    }
  }

  const handleToggleStrategy = async (id: number, isRunning: boolean) => {
    try {
      if (isRunning) {
        await stopStrategy(id)
        showApiSuccess("策略已停止")
      } else {
        await startStrategy(id)
        showApiSuccess("策略已启动")
      }
    } catch (error) {
      showApiError(error)
    }
  }

  const filteredStrategies = (strategies || []).filter(strategy =>
    strategy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    strategy.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-1 gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="搜索策略名称或交易对..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          创建策略
        </Button>
      </div>

      {/* Strategies Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredStrategies.map((strategy) => (
          <Card key={strategy.id} className="group hover:shadow-lg transition-shadow">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="text-lg">{strategy.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge className={strategyTypeMap[strategy.strategy_type as keyof typeof strategyTypeMap]?.color || "bg-gray-100 text-gray-800"}>
                      {strategyTypeMap[strategy.strategy_type as keyof typeof strategyTypeMap]?.label || strategy.strategy_type}
                    </Badge>
                    <Badge className={statusMap[strategy.status as keyof typeof statusMap]?.color || "bg-gray-100 text-gray-800"}>
                      {statusMap[strategy.status as keyof typeof statusMap]?.label || strategy.status}
                    </Badge>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="opacity-0 hover:opacity-100 transition-opacity">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Edit className="h-4 w-4 mr-2" />
                      编辑策略
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleToggleStrategy(strategy.id, strategy.status === "running")}>
                      {strategy.status === "running" ? (
                        <>
                          <Square className="h-4 w-4 mr-2" />
                          停止策略
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          启动策略
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => handleDeleteStrategy(strategy.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      删除策略
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <CardDescription>
                {strategy.exchange} • {strategy.symbol}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Profit/Loss */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">总盈亏</span>
                <div className="text-right">
                  <div className={`font-medium ${getProfitColor(strategy.total_profit - strategy.total_loss)}`}>
                    {formatCurrency(strategy.total_profit - strategy.total_loss)}
                  </div>
                  <div className={`text-sm ${getProfitColor(strategy.total_profit - strategy.total_loss)}`}>
                    {formatPercentage(((strategy.total_profit - strategy.total_loss) / strategy.base_amount) * 100)}
                  </div>
                </div>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-lg font-semibold">{strategy.total_orders}</div>
                  <div className="text-xs text-muted-foreground">订单数</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">{strategy.total_trades}</div>
                  <div className="text-xs text-muted-foreground">成交数</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">{strategy.win_rate.toFixed(1)}%</div>
                  <div className="text-xs text-muted-foreground">胜率</div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                {strategy.status === "running" ? (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => handleToggleStrategy(strategy.id, true)}
                  >
                    <Square className="h-3 w-3 mr-1" />
                    停止
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => handleToggleStrategy(strategy.id, false)}
                  >
                    <Play className="h-3 w-3 mr-1" />
                    启动
                  </Button>
                )}
                <Button variant="outline" size="sm">
                  详情
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredStrategies.length === 0 && (
        <Card className="p-12 text-center">
          <div className="mx-auto h-12 w-12 rounded-full bg-muted flex items-center justify-center mb-4">
            <Plus className="h-6 w-6 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">暂无策略</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm ? "没有找到符合条件的策略" : "创建您的第一个交易策略"}
          </p>
          {!searchTerm && (
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              创建策略
            </Button>
          )}
        </Card>
      )}

      {/* 策略创建器 */}
      {showCreateDialog && (
        <StrategyCreator
          onClose={() => setShowCreateDialog(false)}
          onSuccess={() => {
            setShowCreateDialog(false)
            fetchStrategies()
          }}
        />
      )}
    </div>
  )
}
