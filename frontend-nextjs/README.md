# Alpha Tool - 量化交易平台

基于 Next.js 14 + shadcn/ui + Tailwind CSS 构建的现代化量化交易平台前端。

## 🚀 技术栈

- **框架**: Next.js 14 (App Router)
- **UI 组件**: shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **图表**: Recharts
- **图标**: Lucide React
- **主题**: next-themes
- **表单**: React Hook Form
- **HTTP 客户端**: Axios
- **通知**: React Hot Toast

## 📦 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

## 🛠️ 开发

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看结果。

## 🏗️ 构建

```bash
npm run build
npm run start
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   ├── page.tsx          # 首页
│   ├── strategies/       # 策略页面
│   ├── positions/        # 持仓页面
│   ├── rules/           # 规则页面
│   ├── history/         # 历史页面
│   └── about/           # 关于页面
├── components/             # 组件
│   ├── ui/               # 基础 UI 组件
│   ├── layout/           # 布局组件
│   ├── dashboard/        # 仪表板组件
│   ├── strategies/       # 策略相关组件
│   └── providers/        # 提供者组件
├── lib/                   # 工具函数
│   └── utils.ts          # 通用工具
├── hooks/                 # 自定义 Hooks
├── types/                 # TypeScript 类型定义
└── store/                 # 状态管理
```

## 🎨 设计特色

### 现代化界面
- 简约优雅的设计风格
- 响应式布局，支持各种设备
- 深色/浅色主题切换
- 流畅的动画效果

### 专业功能
- 策略管理和交易
- 实时数据展示
- 智能通知系统
- 用户友好的操作界面

### 技术亮点
- TypeScript 全面支持
- 组件化开发
- 性能优化
- 无障碍访问支持

## 🔧 配置

### 环境变量

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=Alpha Tool
```

### 主题配置

在 `tailwind.config.js` 中自定义主题：

```js
theme: {
  extend: {
    colors: {
      // 自定义颜色
    }
  }
}
```

## 📱 响应式设计

- **移动端**: 320px+
- **平板**: 768px+
- **桌面**: 1024px+
- **大屏**: 1440px+

## 🎯 核心功能

### 1. 仪表板
- 功能卡片展示
- 通知横幅
- 快速操作入口

### 2. 策略管理
- 策略列表展示
- 创建/编辑策略
- 策略状态管理
- 性能统计

### 3. 用户界面
- 侧边栏导航
- 顶部操作栏
- 主题切换
- 用户菜单

## 🚀 部署

### Vercel 部署

```bash
npm run build
```

推送到 GitHub 后，在 Vercel 中导入项目即可自动部署。

### Docker 部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
