/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/auth-provider.tsx */ \"(ssr)/./src/components/providers/auth-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(ssr)/./src/components/providers/theme-provider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZoZW5pbmd5dSUyRnNvZmV3YXJlJTJGbGF6eSUyRmZyb250ZW5kLW5leHRqcyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRlVzZXJzJTJGaGVuaW5neXUlMkZzb2Zld2FyZSUyRmxhenklMkZmcm9udGVuZC1uZXh0anMlMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJm1vZHVsZXM9JTJGVXNlcnMlMkZoZW5pbmd5dSUyRnNvZmV3YXJlJTJGbGF6eSUyRmZyb250ZW5kLW5leHRqcyUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZoZW5pbmd5dSUyRnNvZmV3YXJlJTJGbGF6eSUyRmZyb250ZW5kLW5leHRqcyUyRnNyYyUyRmNvbXBvbmVudHMlMkZwcm92aWRlcnMlMkZhdXRoLXByb3ZpZGVyLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGaGVuaW5neXUlMkZzb2Zld2FyZSUyRmxhenklMkZmcm9udGVuZC1uZXh0anMlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGdGhlbWUtcHJvdmlkZXIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBOEg7QUFDOUgsb01BQTZIO0FBQzdIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHJhZGluZy1wbGF0Zm9ybS1uZXh0anMvPzZkZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvaGVuaW5neXUvc29mZXdhcmUvbGF6eS9mcm9udGVuZC1uZXh0anMvbm9kZV9tb2R1bGVzL3JlYWN0LWhvdC10b2FzdC9kaXN0L2luZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2hlbmluZ3l1L3NvZmV3YXJlL2xhenkvZnJvbnRlbmQtbmV4dGpzL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9hdXRoLXByb3ZpZGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2hlbmluZ3l1L3NvZmV3YXJlL2xhenkvZnJvbnRlbmQtbmV4dGpzL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy90aGVtZS1wcm92aWRlci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/auth-provider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/auth-provider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\n\n\n\n// 不需要认证的路由\nconst publicRoutes = [\n    \"/login\",\n    \"/register\"\n];\nfunction AuthProvider({ children }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAuthenticated, getCurrentUser } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            // 如果是公开路由，直接显示\n            if (publicRoutes.includes(pathname)) {\n                setIsLoading(false);\n                return;\n            }\n            // 检查本地存储的token\n            const token = localStorage.getItem(\"access_token\");\n            if (!token) {\n                // 没有token，跳转到登录页\n                router.push(\"/login\");\n                setIsLoading(false);\n                return;\n            }\n            if (!isAuthenticated) {\n                try {\n                    // 尝试获取用户信息\n                    await getCurrentUser();\n                } catch (error) {\n                    // 获取用户信息失败，清除token并跳转到登录页\n                    localStorage.removeItem(\"access_token\");\n                    router.push(\"/login\");\n                    setIsLoading(false);\n                    return;\n                }\n            }\n            setIsLoading(false);\n        };\n        checkAuth();\n    }, [\n        pathname,\n        isAuthenticated,\n        getCurrentUser,\n        router\n    ]);\n    // 如果正在加载，显示加载界面\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-background\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/auth-provider.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"加载中...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/auth-provider.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/auth-provider.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/auth-provider.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this);\n    }\n    // 如果是公开路由或已认证，显示内容\n    if (publicRoutes.includes(pathname) || isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // 其他情况不显示内容（会被重定向）\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvdGhlbWUtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHJhZGluZy1wbGF0Zm9ybS1uZXh0anMvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvdGhlbWUtcHJvdmlkZXIudHN4PzU2NzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiKeyApi: () => (/* binding */ apiKeyApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   dashboardApi: () => (/* binding */ dashboardApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   notificationApi: () => (/* binding */ notificationApi),\n/* harmony export */   orderApi: () => (/* binding */ orderApi),\n/* harmony export */   showApiError: () => (/* binding */ showApiError),\n/* harmony export */   showApiSuccess: () => (/* binding */ showApiSuccess),\n/* harmony export */   strategyApi: () => (/* binding */ strategyApi),\n/* harmony export */   tradeApi: () => (/* binding */ tradeApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n// API配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: `${API_BASE_URL}/api/v1`,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 添加认证token\n    let token = localStorage.getItem(\"access_token\");\n    // 如果没有直接的token，尝试从auth-storage中获取（兼容Zustand persist）\n    if (!token) {\n        const authStorage = localStorage.getItem(\"auth-storage\");\n        if (authStorage) {\n            try {\n                const authData = JSON.parse(authStorage);\n                token = authData.state?.token;\n            } catch (error) {\n                console.error(\"解析auth-storage失败:\", error);\n            }\n        }\n    }\n    // 如果还是没有token，使用debug-token（仅开发环境）\n    if (!token && \"development\" === \"development\") {\n        token = \"debug-token\";\n    }\n    if (token && token !== \"debug-token\") {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // 未授权，清除token并跳转到登录页\n        localStorage.removeItem(\"access_token\");\n        window.location.href = \"/login\";\n    } else if (error.response?.status >= 500) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"服务器错误，请稍后重试\");\n    } else if (error.response?.data?.message) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(error.response.data.message);\n    }\n    return Promise.reject(error);\n});\n// 认证API\nconst authApi = {\n    login: async (username, password)=>{\n        // 使用FormData格式，符合OAuth2PasswordRequestForm要求\n        const formData = new FormData();\n        formData.append(\"username\", username);\n        formData.append(\"password\", password);\n        const response = await apiClient.post(\"/auth/login\", formData, {\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        return response.data;\n    },\n    register: async (username, email, password)=>{\n        const response = await apiClient.post(\"/auth/register\", {\n            username,\n            email,\n            password\n        });\n        return response.data;\n    },\n    getCurrentUser: async ()=>{\n        const response = await apiClient.get(\"/users/profile\");\n        return response.data;\n    },\n    logout: async ()=>{\n        await apiClient.post(\"/auth/logout\");\n        localStorage.removeItem(\"access_token\");\n    }\n};\n// API密钥管理API\nconst apiKeyApi = {\n    getAll: async ()=>{\n        const response = await apiClient.get(\"/api-keys\");\n        return response.data;\n    },\n    create: async (data)=>{\n        const response = await apiClient.post(\"/api-keys\", data);\n        return response.data;\n    },\n    update: async (id, data)=>{\n        const response = await apiClient.put(`/api-keys/${id}`, data);\n        return response.data;\n    },\n    delete: async (id)=>{\n        await apiClient.delete(`/api-keys/${id}`);\n    },\n    test: async (id)=>{\n        const response = await apiClient.post(`/api-keys/${id}/test`);\n        return response.data;\n    }\n};\n// 策略管理API\nconst strategyApi = {\n    getAll: async (filters)=>{\n        const response = await apiClient.get(\"/strategies\", {\n            params: filters\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await apiClient.get(`/strategies/${id}`);\n        return response.data;\n    },\n    create: async (data)=>{\n        const response = await apiClient.post(\"/strategies\", data);\n        return response.data;\n    },\n    update: async (id, data)=>{\n        const response = await apiClient.put(`/strategies/${id}`, data);\n        return response.data;\n    },\n    delete: async (id)=>{\n        await apiClient.delete(`/strategies/${id}`);\n    },\n    start: async (id)=>{\n        await apiClient.post(`/strategies/${id}/start`);\n    },\n    stop: async (id)=>{\n        await apiClient.post(`/strategies/${id}/stop`);\n    },\n    getTypes: async ()=>{\n        const response = await apiClient.get(\"/strategies/types\");\n        return response.data;\n    },\n    validate: async (strategyType, parameters)=>{\n        const response = await apiClient.post(\"/strategies/validate\", {\n            strategy_type: strategyType,\n            parameters: parameters\n        });\n        return response.data;\n    },\n    backtest: async (request)=>{\n        const response = await apiClient.post(\"/strategies/backtest\", request);\n        return response.data.result;\n    },\n    optimize: async (request)=>{\n        const response = await apiClient.post(\"/strategies/optimize\", request);\n        return response.data.result;\n    }\n};\n// 订单管理API\nconst orderApi = {\n    getAll: async (filters)=>{\n        const response = await apiClient.get(\"/orders\", {\n            params: filters\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await apiClient.get(`/orders/${id}`);\n        return response.data;\n    },\n    cancel: async (id)=>{\n        await apiClient.post(`/orders/${id}/cancel`);\n    }\n};\n// 交易记录API\nconst tradeApi = {\n    getAll: async (filters)=>{\n        const response = await apiClient.get(\"/trades\", {\n            params: filters\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await apiClient.get(`/trades/${id}`);\n        return response.data;\n    },\n    getByStrategy: async (strategyId)=>{\n        const response = await apiClient.get(`/strategies/${strategyId}/trades`);\n        return response.data;\n    }\n};\n// 通知API\nconst notificationApi = {\n    getAll: async ()=>{\n        const response = await apiClient.get(\"/notifications\");\n        return response.data;\n    },\n    markAsRead: async (id)=>{\n        await apiClient.put(`/notifications/${id}/read`);\n    },\n    markAllAsRead: async ()=>{\n        await apiClient.put(\"/notifications/read-all\");\n    },\n    delete: async (id)=>{\n        await apiClient.delete(`/notifications/${id}`);\n    }\n};\n// 仪表板API\nconst dashboardApi = {\n    getStats: async ()=>{\n        const response = await apiClient.get(\"/dashboard/stats\");\n        return response.data;\n    },\n    getMarketData: async (symbols)=>{\n        const response = await apiClient.get(\"/dashboard/market-data\", {\n            params: {\n                symbols: symbols?.join(\",\")\n            }\n        });\n        return response.data;\n    },\n    getPositions: async ()=>{\n        const response = await apiClient.get(\"/dashboard/positions\");\n        return response.data;\n    }\n};\n// 工具函数\nconst handleApiError = (error)=>{\n    if (error.response?.data?.message) {\n        return error.response.data.message;\n    } else if (error.message) {\n        return error.message;\n    } else {\n        return \"未知错误\";\n    }\n};\nconst showApiError = (error)=>{\n    const message = handleApiError(error);\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message);\n};\nconst showApiSuccess = (message)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(message);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQTJEO0FBQ3hCO0FBd0JuQyxRQUFRO0FBQ1IsTUFBTUUsZUFBZUMsUUFBUUMsR0FBRyxDQUFDQyxtQkFBbUIsSUFBSTtBQUV4RCxZQUFZO0FBQ1osTUFBTUMsWUFBMkJOLDZDQUFLQSxDQUFDTyxNQUFNLENBQUM7SUFDNUNDLFNBQVMsQ0FBQyxFQUFFTixhQUFhLE9BQU8sQ0FBQztJQUNqQ08sU0FBUztJQUNUQyxTQUFTO1FBQ1AsZ0JBQWdCO0lBQ2xCO0FBQ0Y7QUFFQSxRQUFRO0FBQ1JKLFVBQVVLLFlBQVksQ0FBQ0MsT0FBTyxDQUFDQyxHQUFHLENBQ2hDLENBQUNDO0lBQ0MsWUFBWTtJQUNaLElBQUlDLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztJQUVqQyxxREFBcUQ7SUFDckQsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTUcsY0FBY0YsYUFBYUMsT0FBTyxDQUFDO1FBQ3pDLElBQUlDLGFBQWE7WUFDZixJQUFJO2dCQUNGLE1BQU1DLFdBQVdDLEtBQUtDLEtBQUssQ0FBQ0g7Z0JBQzVCSCxRQUFRSSxTQUFTRyxLQUFLLEVBQUVQO1lBQzFCLEVBQUUsT0FBT1EsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLHFCQUFxQkE7WUFDckM7UUFDRjtJQUNGO0lBRUEsbUNBQW1DO0lBQ25DLElBQUksQ0FBQ1IsU0FBU1osa0JBQXlCLGVBQWU7UUFDcERZLFFBQVE7SUFDVjtJQUVBLElBQUlBLFNBQVNBLFVBQVUsZUFBZTtRQUNwQ0QsT0FBT0osT0FBTyxDQUFDZSxhQUFhLEdBQUcsQ0FBQyxPQUFPLEVBQUVWLE1BQU0sQ0FBQztJQUNsRDtJQUVBLE9BQU9EO0FBQ1QsR0FDQSxDQUFDUztJQUNDLE9BQU9HLFFBQVFDLE1BQU0sQ0FBQ0o7QUFDeEI7QUFHRixRQUFRO0FBQ1JqQixVQUFVSyxZQUFZLENBQUNpQixRQUFRLENBQUNmLEdBQUcsQ0FDakMsQ0FBQ2U7SUFDQyxPQUFPQTtBQUNULEdBQ0EsQ0FBQ0w7SUFDQyxJQUFJQSxNQUFNSyxRQUFRLEVBQUVDLFdBQVcsS0FBSztRQUNsQyxxQkFBcUI7UUFDckJiLGFBQWFjLFVBQVUsQ0FBQztRQUN4QkMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7SUFDekIsT0FBTyxJQUFJVixNQUFNSyxRQUFRLEVBQUVDLFVBQVUsS0FBSztRQUN4QzVCLHVEQUFLQSxDQUFDc0IsS0FBSyxDQUFDO0lBQ2QsT0FBTyxJQUFJQSxNQUFNSyxRQUFRLEVBQUVNLE1BQU1DLFNBQVM7UUFDeENsQyx1REFBS0EsQ0FBQ3NCLEtBQUssQ0FBQ0EsTUFBTUssUUFBUSxDQUFDTSxJQUFJLENBQUNDLE9BQU87SUFDekM7SUFDQSxPQUFPVCxRQUFRQyxNQUFNLENBQUNKO0FBQ3hCO0FBR0YsUUFBUTtBQUNELE1BQU1hLFVBQVU7SUFDckJDLE9BQU8sT0FBT0MsVUFBa0JDO1FBQzlCLDZDQUE2QztRQUM3QyxNQUFNQyxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsWUFBWUo7UUFDNUJFLFNBQVNFLE1BQU0sQ0FBQyxZQUFZSDtRQUU1QixNQUFNWCxXQUFXLE1BQU10QixVQUFVcUMsSUFBSSxDQUFDLGVBQWVILFVBQVU7WUFDN0Q5QixTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtRQUNGO1FBQ0EsT0FBT2tCLFNBQVNNLElBQUk7SUFDdEI7SUFFQVUsVUFBVSxPQUFPTixVQUFrQk8sT0FBZU47UUFDaEQsTUFBTVgsV0FBVyxNQUFNdEIsVUFBVXFDLElBQUksQ0FBQyxrQkFBa0I7WUFBRUw7WUFBVU87WUFBT047UUFBUztRQUNwRixPQUFPWCxTQUFTTSxJQUFJO0lBQ3RCO0lBRUFZLGdCQUFnQjtRQUNkLE1BQU1sQixXQUFXLE1BQU10QixVQUFVeUMsR0FBRyxDQUFDO1FBQ3JDLE9BQU9uQixTQUFTTSxJQUFJO0lBQ3RCO0lBRUFjLFFBQVE7UUFDTixNQUFNMUMsVUFBVXFDLElBQUksQ0FBQztRQUNyQjNCLGFBQWFjLFVBQVUsQ0FBQztJQUMxQjtBQUNGLEVBQUM7QUFFRCxhQUFhO0FBQ04sTUFBTW1CLFlBQVk7SUFDdkJDLFFBQVE7UUFDTixNQUFNdEIsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQztRQUNyQyxPQUFPbkIsU0FBU00sSUFBSTtJQUN0QjtJQUVBM0IsUUFBUSxPQUFPMkI7UUFDYixNQUFNTixXQUFXLE1BQU10QixVQUFVcUMsSUFBSSxDQUFDLGFBQWFUO1FBQ25ELE9BQU9OLFNBQVNNLElBQUk7SUFDdEI7SUFFQWlCLFFBQVEsT0FBT0MsSUFBWWxCO1FBQ3pCLE1BQU1OLFdBQVcsTUFBTXRCLFVBQVUrQyxHQUFHLENBQUMsQ0FBQyxVQUFVLEVBQUVELEdBQUcsQ0FBQyxFQUFFbEI7UUFDeEQsT0FBT04sU0FBU00sSUFBSTtJQUN0QjtJQUVBb0IsUUFBUSxPQUFPRjtRQUNiLE1BQU05QyxVQUFVZ0QsTUFBTSxDQUFDLENBQUMsVUFBVSxFQUFFRixHQUFHLENBQUM7SUFDMUM7SUFFQUcsTUFBTSxPQUFPSDtRQUNYLE1BQU14QixXQUFXLE1BQU10QixVQUFVcUMsSUFBSSxDQUFDLENBQUMsVUFBVSxFQUFFUyxHQUFHLEtBQUssQ0FBQztRQUM1RCxPQUFPeEIsU0FBU00sSUFBSTtJQUN0QjtBQUNGLEVBQUM7QUFFRCxVQUFVO0FBQ0gsTUFBTXNCLGNBQWM7SUFDekJOLFFBQVEsT0FBT087UUFDYixNQUFNN0IsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQyxlQUFlO1lBQUVXLFFBQVFEO1FBQVE7UUFDdEUsT0FBTzdCLFNBQVNNLElBQUk7SUFDdEI7SUFFQXlCLFNBQVMsT0FBT1A7UUFDZCxNQUFNeEIsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQyxDQUFDLFlBQVksRUFBRUssR0FBRyxDQUFDO1FBQ3hELE9BQU94QixTQUFTTSxJQUFJO0lBQ3RCO0lBRUEzQixRQUFRLE9BQU8yQjtRQUNiLE1BQU1OLFdBQVcsTUFBTXRCLFVBQVVxQyxJQUFJLENBQUMsZUFBZVQ7UUFDckQsT0FBT04sU0FBU00sSUFBSTtJQUN0QjtJQUVBaUIsUUFBUSxPQUFPQyxJQUFZbEI7UUFDekIsTUFBTU4sV0FBVyxNQUFNdEIsVUFBVStDLEdBQUcsQ0FBQyxDQUFDLFlBQVksRUFBRUQsR0FBRyxDQUFDLEVBQUVsQjtRQUMxRCxPQUFPTixTQUFTTSxJQUFJO0lBQ3RCO0lBRUFvQixRQUFRLE9BQU9GO1FBQ2IsTUFBTTlDLFVBQVVnRCxNQUFNLENBQUMsQ0FBQyxZQUFZLEVBQUVGLEdBQUcsQ0FBQztJQUM1QztJQUVBUSxPQUFPLE9BQU9SO1FBQ1osTUFBTTlDLFVBQVVxQyxJQUFJLENBQUMsQ0FBQyxZQUFZLEVBQUVTLEdBQUcsTUFBTSxDQUFDO0lBQ2hEO0lBRUFTLE1BQU0sT0FBT1Q7UUFDWCxNQUFNOUMsVUFBVXFDLElBQUksQ0FBQyxDQUFDLFlBQVksRUFBRVMsR0FBRyxLQUFLLENBQUM7SUFDL0M7SUFFQVUsVUFBVTtRQUNSLE1BQU1sQyxXQUFXLE1BQU10QixVQUFVeUMsR0FBRyxDQUFDO1FBQ3JDLE9BQU9uQixTQUFTTSxJQUFJO0lBQ3RCO0lBRUE2QixVQUFVLE9BQU9DLGNBQXNCQztRQUNyQyxNQUFNckMsV0FBVyxNQUFNdEIsVUFBVXFDLElBQUksQ0FBQyx3QkFBd0I7WUFDNUR1QixlQUFlRjtZQUNmQyxZQUFZQTtRQUNkO1FBQ0EsT0FBT3JDLFNBQVNNLElBQUk7SUFDdEI7SUFFQWlDLFVBQVUsT0FBT3ZEO1FBQ2YsTUFBTWdCLFdBQVcsTUFBTXRCLFVBQVVxQyxJQUFJLENBQUMsd0JBQXdCL0I7UUFDOUQsT0FBT2dCLFNBQVNNLElBQUksQ0FBQ2tDLE1BQU07SUFDN0I7SUFFQUMsVUFBVSxPQUFPekQ7UUFDZixNQUFNZ0IsV0FBVyxNQUFNdEIsVUFBVXFDLElBQUksQ0FBQyx3QkFBd0IvQjtRQUM5RCxPQUFPZ0IsU0FBU00sSUFBSSxDQUFDa0MsTUFBTTtJQUM3QjtBQUNGLEVBQUM7QUFFRCxVQUFVO0FBQ0gsTUFBTUUsV0FBVztJQUN0QnBCLFFBQVEsT0FBT087UUFDYixNQUFNN0IsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQyxXQUFXO1lBQUVXLFFBQVFEO1FBQVE7UUFDbEUsT0FBTzdCLFNBQVNNLElBQUk7SUFDdEI7SUFFQXlCLFNBQVMsT0FBT1A7UUFDZCxNQUFNeEIsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQyxDQUFDLFFBQVEsRUFBRUssR0FBRyxDQUFDO1FBQ3BELE9BQU94QixTQUFTTSxJQUFJO0lBQ3RCO0lBRUFxQyxRQUFRLE9BQU9uQjtRQUNiLE1BQU05QyxVQUFVcUMsSUFBSSxDQUFDLENBQUMsUUFBUSxFQUFFUyxHQUFHLE9BQU8sQ0FBQztJQUM3QztBQUNGLEVBQUM7QUFFRCxVQUFVO0FBQ0gsTUFBTW9CLFdBQVc7SUFDdEJ0QixRQUFRLE9BQU9PO1FBQ2IsTUFBTTdCLFdBQVcsTUFBTXRCLFVBQVV5QyxHQUFHLENBQUMsV0FBVztZQUFFVyxRQUFRRDtRQUFRO1FBQ2xFLE9BQU83QixTQUFTTSxJQUFJO0lBQ3RCO0lBRUF5QixTQUFTLE9BQU9QO1FBQ2QsTUFBTXhCLFdBQVcsTUFBTXRCLFVBQVV5QyxHQUFHLENBQUMsQ0FBQyxRQUFRLEVBQUVLLEdBQUcsQ0FBQztRQUNwRCxPQUFPeEIsU0FBU00sSUFBSTtJQUN0QjtJQUVBdUMsZUFBZSxPQUFPQztRQUNwQixNQUFNOUMsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQyxDQUFDLFlBQVksRUFBRTJCLFdBQVcsT0FBTyxDQUFDO1FBQ3ZFLE9BQU85QyxTQUFTTSxJQUFJO0lBQ3RCO0FBQ0YsRUFBQztBQUVELFFBQVE7QUFDRCxNQUFNeUMsa0JBQWtCO0lBQzdCekIsUUFBUTtRQUNOLE1BQU10QixXQUFXLE1BQU10QixVQUFVeUMsR0FBRyxDQUFDO1FBQ3JDLE9BQU9uQixTQUFTTSxJQUFJO0lBQ3RCO0lBRUEwQyxZQUFZLE9BQU94QjtRQUNqQixNQUFNOUMsVUFBVStDLEdBQUcsQ0FBQyxDQUFDLGVBQWUsRUFBRUQsR0FBRyxLQUFLLENBQUM7SUFDakQ7SUFFQXlCLGVBQWU7UUFDYixNQUFNdkUsVUFBVStDLEdBQUcsQ0FBQztJQUN0QjtJQUVBQyxRQUFRLE9BQU9GO1FBQ2IsTUFBTTlDLFVBQVVnRCxNQUFNLENBQUMsQ0FBQyxlQUFlLEVBQUVGLEdBQUcsQ0FBQztJQUMvQztBQUNGLEVBQUM7QUFFRCxTQUFTO0FBQ0YsTUFBTTBCLGVBQWU7SUFDMUJDLFVBQVU7UUFDUixNQUFNbkQsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQztRQUNyQyxPQUFPbkIsU0FBU00sSUFBSTtJQUN0QjtJQUVBOEMsZUFBZSxPQUFPQztRQUNwQixNQUFNckQsV0FBVyxNQUFNdEIsVUFBVXlDLEdBQUcsQ0FBQywwQkFBMEI7WUFDN0RXLFFBQVE7Z0JBQUV1QixTQUFTQSxTQUFTQyxLQUFLO1lBQUs7UUFDeEM7UUFDQSxPQUFPdEQsU0FBU00sSUFBSTtJQUN0QjtJQUVBaUQsY0FBYztRQUNaLE1BQU12RCxXQUFXLE1BQU10QixVQUFVeUMsR0FBRyxDQUFDO1FBQ3JDLE9BQU9uQixTQUFTTSxJQUFJO0lBQ3RCO0FBQ0YsRUFBQztBQUVELE9BQU87QUFDQSxNQUFNa0QsaUJBQWlCLENBQUM3RDtJQUM3QixJQUFJQSxNQUFNSyxRQUFRLEVBQUVNLE1BQU1DLFNBQVM7UUFDakMsT0FBT1osTUFBTUssUUFBUSxDQUFDTSxJQUFJLENBQUNDLE9BQU87SUFDcEMsT0FBTyxJQUFJWixNQUFNWSxPQUFPLEVBQUU7UUFDeEIsT0FBT1osTUFBTVksT0FBTztJQUN0QixPQUFPO1FBQ0wsT0FBTztJQUNUO0FBQ0YsRUFBQztBQUVNLE1BQU1rRCxlQUFlLENBQUM5RDtJQUMzQixNQUFNWSxVQUFVaUQsZUFBZTdEO0lBQy9CdEIsdURBQUtBLENBQUNzQixLQUFLLENBQUNZO0FBQ2QsRUFBQztBQUVNLE1BQU1tRCxpQkFBaUIsQ0FBQ25EO0lBQzdCbEMsdURBQUtBLENBQUNzRixPQUFPLENBQUNwRDtBQUNoQixFQUFDO0FBRUQsaUVBQWU3QixTQUFTQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHJhZGluZy1wbGF0Zm9ybS1uZXh0anMvLi9zcmMvbGliL2FwaS50cz8yZmFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcywgeyBBeGlvc0luc3RhbmNlLCBBeGlvc1Jlc3BvbnNlIH0gZnJvbSAnYXhpb3MnXG5pbXBvcnQgdG9hc3QgZnJvbSAncmVhY3QtaG90LXRvYXN0J1xuaW1wb3J0IHtcbiAgVXNlcixcbiAgQVBJS2V5LFxuICBTdHJhdGVneSxcbiAgT3JkZXIsXG4gIFRyYWRlLFxuICBTdHJhdGVneUNyZWF0ZUZvcm0sXG4gIFN0cmF0ZWd5VHlwZUluZm8sXG4gIFN0cmF0ZWd5VmFsaWRhdGlvblJlc3VsdCxcbiAgQmFja3Rlc3RSZXF1ZXN0LFxuICBCYWNrdGVzdFJlc3VsdCxcbiAgUGFyYW1ldGVyT3B0aW1pemF0aW9uUmVxdWVzdCxcbiAgTm90aWZpY2F0aW9uLFxuICBBcGlSZXNwb25zZSxcbiAgUGFnaW5hdGVkUmVzcG9uc2UsXG4gIFN0cmF0ZWd5RmlsdGVycyxcbiAgT3JkZXJGaWx0ZXJzLFxuICBUcmFkZUZpbHRlcnMsXG4gIERhc2hib2FyZFN0YXRzLFxuICBNYXJrZXREYXRhLFxuICBQb3NpdGlvblxufSBmcm9tICdAL3R5cGVzJ1xuXG4vLyBBUEnphY3nva5cbmNvbnN0IEFQSV9CQVNFX1VSTCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6ODAwMCdcblxuLy8g5Yib5bu6YXhpb3Plrp7kvotcbmNvbnN0IGFwaUNsaWVudDogQXhpb3NJbnN0YW5jZSA9IGF4aW9zLmNyZWF0ZSh7XG4gIGJhc2VVUkw6IGAke0FQSV9CQVNFX1VSTH0vYXBpL3YxYCxcbiAgdGltZW91dDogMzAwMDAsXG4gIGhlYWRlcnM6IHtcbiAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICB9LFxufSlcblxuLy8g6K+35rGC5oum5oiq5ZmoXG5hcGlDbGllbnQuaW50ZXJjZXB0b3JzLnJlcXVlc3QudXNlKFxuICAoY29uZmlnKSA9PiB7XG4gICAgLy8g5re75Yqg6K6k6K+BdG9rZW5cbiAgICBsZXQgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWNjZXNzX3Rva2VuJylcblxuICAgIC8vIOWmguaenOayoeacieebtOaOpeeahHRva2Vu77yM5bCd6K+V5LuOYXV0aC1zdG9yYWdl5Lit6I635Y+W77yI5YW85a65WnVzdGFuZCBwZXJzaXN077yJXG4gICAgaWYgKCF0b2tlbikge1xuICAgICAgY29uc3QgYXV0aFN0b3JhZ2UgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYXV0aC1zdG9yYWdlJylcbiAgICAgIGlmIChhdXRoU3RvcmFnZSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGF1dGhEYXRhID0gSlNPTi5wYXJzZShhdXRoU3RvcmFnZSlcbiAgICAgICAgICB0b2tlbiA9IGF1dGhEYXRhLnN0YXRlPy50b2tlblxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekGF1dGgtc3RvcmFnZeWksei0pTonLCBlcnJvcilcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWmguaenOi/mOaYr+ayoeaciXRva2Vu77yM5L2/55SoZGVidWctdG9rZW7vvIjku4XlvIDlj5Hnjq/looPvvIlcbiAgICBpZiAoIXRva2VuICYmIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnKSB7XG4gICAgICB0b2tlbiA9ICdkZWJ1Zy10b2tlbidcbiAgICB9XG5cbiAgICBpZiAodG9rZW4gJiYgdG9rZW4gIT09ICdkZWJ1Zy10b2tlbicpIHtcbiAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YFxuICAgIH1cblxuICAgIHJldHVybiBjb25maWdcbiAgfSxcbiAgKGVycm9yKSA9PiB7XG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKVxuICB9XG4pXG5cbi8vIOWTjeW6lOaLpuaIquWZqFxuYXBpQ2xpZW50LmludGVyY2VwdG9ycy5yZXNwb25zZS51c2UoXG4gIChyZXNwb25zZTogQXhpb3NSZXNwb25zZSkgPT4ge1xuICAgIHJldHVybiByZXNwb25zZVxuICB9LFxuICAoZXJyb3IpID0+IHtcbiAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxKSB7XG4gICAgICAvLyDmnKrmjojmnYPvvIzmuIXpmaR0b2tlbuW5tui3s+i9rOWIsOeZu+W9lemhtVxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FjY2Vzc190b2tlbicpXG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nXG4gICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID49IDUwMCkge1xuICAgICAgdG9hc3QuZXJyb3IoJ+acjeWKoeWZqOmUmeivr++8jOivt+eojeWQjumHjeivlScpXG4gICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSkge1xuICAgICAgdG9hc3QuZXJyb3IoZXJyb3IucmVzcG9uc2UuZGF0YS5tZXNzYWdlKVxuICAgIH1cbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpXG4gIH1cbilcblxuLy8g6K6k6K+BQVBJXG5leHBvcnQgY29uc3QgYXV0aEFwaSA9IHtcbiAgbG9naW46IGFzeW5jICh1c2VybmFtZTogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTx7IGFjY2Vzc190b2tlbjogc3RyaW5nOyB1c2VyOiBVc2VyIH0+ID0+IHtcbiAgICAvLyDkvb/nlKhGb3JtRGF0YeagvOW8j++8jOespuWQiE9BdXRoMlBhc3N3b3JkUmVxdWVzdEZvcm3opoHmsYJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpXG4gICAgZm9ybURhdGEuYXBwZW5kKCd1c2VybmFtZScsIHVzZXJuYW1lKVxuICAgIGZvcm1EYXRhLmFwcGVuZCgncGFzc3dvcmQnLCBwYXNzd29yZClcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoJy9hdXRoL2xvZ2luJywgZm9ybURhdGEsIHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQnLFxuICAgICAgfSxcbiAgICB9KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgcmVnaXN0ZXI6IGFzeW5jICh1c2VybmFtZTogc3RyaW5nLCBlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxVc2VyPiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdCgnL2F1dGgvcmVnaXN0ZXInLCB7IHVzZXJuYW1lLCBlbWFpbCwgcGFzc3dvcmQgfSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIGdldEN1cnJlbnRVc2VyOiBhc3luYyAoKTogUHJvbWlzZTxVc2VyPiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KCcvdXNlcnMvcHJvZmlsZScpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICBsb2dvdXQ6IGFzeW5jICgpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBhd2FpdCBhcGlDbGllbnQucG9zdCgnL2F1dGgvbG9nb3V0JylcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWNjZXNzX3Rva2VuJylcbiAgfSxcbn1cblxuLy8gQVBJ5a+G6ZKl566h55CGQVBJXG5leHBvcnQgY29uc3QgYXBpS2V5QXBpID0ge1xuICBnZXRBbGw6IGFzeW5jICgpOiBQcm9taXNlPEFQSUtleVtdPiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KCcvYXBpLWtleXMnKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgY3JlYXRlOiBhc3luYyAoZGF0YTogT21pdDxBUElLZXksICdpZCcgfCAnY3JlYXRlZF9hdCc+KTogUHJvbWlzZTxBUElLZXk+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KCcvYXBpLWtleXMnLCBkYXRhKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgdXBkYXRlOiBhc3luYyAoaWQ6IG51bWJlciwgZGF0YTogUGFydGlhbDxBUElLZXk+KTogUHJvbWlzZTxBUElLZXk+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wdXQoYC9hcGkta2V5cy8ke2lkfWAsIGRhdGEpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICBkZWxldGU6IGFzeW5jIChpZDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgYXdhaXQgYXBpQ2xpZW50LmRlbGV0ZShgL2FwaS1rZXlzLyR7aWR9YClcbiAgfSxcblxuICB0ZXN0OiBhc3luYyAoaWQ6IG51bWJlcik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoYC9hcGkta2V5cy8ke2lkfS90ZXN0YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxufVxuXG4vLyDnrZbnlaXnrqHnkIZBUElcbmV4cG9ydCBjb25zdCBzdHJhdGVneUFwaSA9IHtcbiAgZ2V0QWxsOiBhc3luYyAoZmlsdGVycz86IFN0cmF0ZWd5RmlsdGVycyk6IFByb21pc2U8U3RyYXRlZ3lbXT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL3N0cmF0ZWdpZXMnLCB7IHBhcmFtczogZmlsdGVycyB9KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgZ2V0QnlJZDogYXN5bmMgKGlkOiBudW1iZXIpOiBQcm9taXNlPFN0cmF0ZWd5PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KGAvc3RyYXRlZ2llcy8ke2lkfWApXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICBjcmVhdGU6IGFzeW5jIChkYXRhOiBTdHJhdGVneUNyZWF0ZUZvcm0pOiBQcm9taXNlPFN0cmF0ZWd5PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdCgnL3N0cmF0ZWdpZXMnLCBkYXRhKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgdXBkYXRlOiBhc3luYyAoaWQ6IG51bWJlciwgZGF0YTogUGFydGlhbDxTdHJhdGVneUNyZWF0ZUZvcm0+KTogUHJvbWlzZTxTdHJhdGVneT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnB1dChgL3N0cmF0ZWdpZXMvJHtpZH1gLCBkYXRhKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgZGVsZXRlOiBhc3luYyAoaWQ6IG51bWJlcik6IFByb21pc2U8dm9pZD4gPT4ge1xuICAgIGF3YWl0IGFwaUNsaWVudC5kZWxldGUoYC9zdHJhdGVnaWVzLyR7aWR9YClcbiAgfSxcblxuICBzdGFydDogYXN5bmMgKGlkOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBhd2FpdCBhcGlDbGllbnQucG9zdChgL3N0cmF0ZWdpZXMvJHtpZH0vc3RhcnRgKVxuICB9LFxuXG4gIHN0b3A6IGFzeW5jIChpZDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgYXdhaXQgYXBpQ2xpZW50LnBvc3QoYC9zdHJhdGVnaWVzLyR7aWR9L3N0b3BgKVxuICB9LFxuXG4gIGdldFR5cGVzOiBhc3luYyAoKTogUHJvbWlzZTxSZWNvcmQ8c3RyaW5nLCBTdHJhdGVneVR5cGVJbmZvPj4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL3N0cmF0ZWdpZXMvdHlwZXMnKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgdmFsaWRhdGU6IGFzeW5jIChzdHJhdGVneVR5cGU6IHN0cmluZywgcGFyYW1ldGVyczogYW55KTogUHJvbWlzZTxTdHJhdGVneVZhbGlkYXRpb25SZXN1bHQ+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KCcvc3RyYXRlZ2llcy92YWxpZGF0ZScsIHtcbiAgICAgIHN0cmF0ZWd5X3R5cGU6IHN0cmF0ZWd5VHlwZSxcbiAgICAgIHBhcmFtZXRlcnM6IHBhcmFtZXRlcnNcbiAgICB9KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgYmFja3Rlc3Q6IGFzeW5jIChyZXF1ZXN0OiBCYWNrdGVzdFJlcXVlc3QpOiBQcm9taXNlPEJhY2t0ZXN0UmVzdWx0PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdCgnL3N0cmF0ZWdpZXMvYmFja3Rlc3QnLCByZXF1ZXN0KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLnJlc3VsdFxuICB9LFxuXG4gIG9wdGltaXplOiBhc3luYyAocmVxdWVzdDogUGFyYW1ldGVyT3B0aW1pemF0aW9uUmVxdWVzdCk6IFByb21pc2U8YW55PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdCgnL3N0cmF0ZWdpZXMvb3B0aW1pemUnLCByZXF1ZXN0KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhLnJlc3VsdFxuICB9LFxufVxuXG4vLyDorqLljZXnrqHnkIZBUElcbmV4cG9ydCBjb25zdCBvcmRlckFwaSA9IHtcbiAgZ2V0QWxsOiBhc3luYyAoZmlsdGVycz86IE9yZGVyRmlsdGVycyk6IFByb21pc2U8UGFnaW5hdGVkUmVzcG9uc2U8T3JkZXI+PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KCcvb3JkZXJzJywgeyBwYXJhbXM6IGZpbHRlcnMgfSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIGdldEJ5SWQ6IGFzeW5jIChpZDogbnVtYmVyKTogUHJvbWlzZTxPcmRlcj4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL29yZGVycy8ke2lkfWApXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICBjYW5jZWw6IGFzeW5jIChpZDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgYXdhaXQgYXBpQ2xpZW50LnBvc3QoYC9vcmRlcnMvJHtpZH0vY2FuY2VsYClcbiAgfSxcbn1cblxuLy8g5Lqk5piT6K6w5b2VQVBJXG5leHBvcnQgY29uc3QgdHJhZGVBcGkgPSB7XG4gIGdldEFsbDogYXN5bmMgKGZpbHRlcnM/OiBUcmFkZUZpbHRlcnMpOiBQcm9taXNlPFBhZ2luYXRlZFJlc3BvbnNlPFRyYWRlPj4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL3RyYWRlcycsIHsgcGFyYW1zOiBmaWx0ZXJzIH0pXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICBnZXRCeUlkOiBhc3luYyAoaWQ6IG51bWJlcik6IFByb21pc2U8VHJhZGU+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoYC90cmFkZXMvJHtpZH1gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgZ2V0QnlTdHJhdGVneTogYXN5bmMgKHN0cmF0ZWd5SWQ6IG51bWJlcik6IFByb21pc2U8VHJhZGVbXT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldChgL3N0cmF0ZWdpZXMvJHtzdHJhdGVneUlkfS90cmFkZXNgKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG59XG5cbi8vIOmAmuefpUFQSVxuZXhwb3J0IGNvbnN0IG5vdGlmaWNhdGlvbkFwaSA9IHtcbiAgZ2V0QWxsOiBhc3luYyAoKTogUHJvbWlzZTxOb3RpZmljYXRpb25bXT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL25vdGlmaWNhdGlvbnMnKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgbWFya0FzUmVhZDogYXN5bmMgKGlkOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBhd2FpdCBhcGlDbGllbnQucHV0KGAvbm90aWZpY2F0aW9ucy8ke2lkfS9yZWFkYClcbiAgfSxcblxuICBtYXJrQWxsQXNSZWFkOiBhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgYXdhaXQgYXBpQ2xpZW50LnB1dCgnL25vdGlmaWNhdGlvbnMvcmVhZC1hbGwnKVxuICB9LFxuXG4gIGRlbGV0ZTogYXN5bmMgKGlkOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBhd2FpdCBhcGlDbGllbnQuZGVsZXRlKGAvbm90aWZpY2F0aW9ucy8ke2lkfWApXG4gIH0sXG59XG5cbi8vIOS7quihqOadv0FQSVxuZXhwb3J0IGNvbnN0IGRhc2hib2FyZEFwaSA9IHtcbiAgZ2V0U3RhdHM6IGFzeW5jICgpOiBQcm9taXNlPERhc2hib2FyZFN0YXRzPiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQuZ2V0KCcvZGFzaGJvYXJkL3N0YXRzJylcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIGdldE1hcmtldERhdGE6IGFzeW5jIChzeW1ib2xzPzogc3RyaW5nW10pOiBQcm9taXNlPE1hcmtldERhdGFbXT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL2Rhc2hib2FyZC9tYXJrZXQtZGF0YScsIHtcbiAgICAgIHBhcmFtczogeyBzeW1ib2xzOiBzeW1ib2xzPy5qb2luKCcsJykgfVxuICAgIH0pXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICBnZXRQb3NpdGlvbnM6IGFzeW5jICgpOiBQcm9taXNlPFBvc2l0aW9uW10+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5nZXQoJy9kYXNoYm9hcmQvcG9zaXRpb25zJylcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxufVxuXG4vLyDlt6Xlhbflh73mlbBcbmV4cG9ydCBjb25zdCBoYW5kbGVBcGlFcnJvciA9IChlcnJvcjogYW55KTogc3RyaW5nID0+IHtcbiAgaWYgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlKSB7XG4gICAgcmV0dXJuIGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZVxuICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UpIHtcbiAgICByZXR1cm4gZXJyb3IubWVzc2FnZVxuICB9IGVsc2Uge1xuICAgIHJldHVybiAn5pyq55+l6ZSZ6K+vJ1xuICB9XG59XG5cbmV4cG9ydCBjb25zdCBzaG93QXBpRXJyb3IgPSAoZXJyb3I6IGFueSk6IHZvaWQgPT4ge1xuICBjb25zdCBtZXNzYWdlID0gaGFuZGxlQXBpRXJyb3IoZXJyb3IpXG4gIHRvYXN0LmVycm9yKG1lc3NhZ2UpXG59XG5cbmV4cG9ydCBjb25zdCBzaG93QXBpU3VjY2VzcyA9IChtZXNzYWdlOiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgdG9hc3Quc3VjY2VzcyhtZXNzYWdlKVxufVxuXG5leHBvcnQgZGVmYXVsdCBhcGlDbGllbnRcbiJdLCJuYW1lcyI6WyJheGlvcyIsInRvYXN0IiwiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJhcGlDbGllbnQiLCJjcmVhdGUiLCJiYXNlVVJMIiwidGltZW91dCIsImhlYWRlcnMiLCJpbnRlcmNlcHRvcnMiLCJyZXF1ZXN0IiwidXNlIiwiY29uZmlnIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiYXV0aFN0b3JhZ2UiLCJhdXRoRGF0YSIsIkpTT04iLCJwYXJzZSIsInN0YXRlIiwiZXJyb3IiLCJjb25zb2xlIiwiQXV0aG9yaXphdGlvbiIsIlByb21pc2UiLCJyZWplY3QiLCJyZXNwb25zZSIsInN0YXR1cyIsInJlbW92ZUl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJkYXRhIiwibWVzc2FnZSIsImF1dGhBcGkiLCJsb2dpbiIsInVzZXJuYW1lIiwicGFzc3dvcmQiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwicG9zdCIsInJlZ2lzdGVyIiwiZW1haWwiLCJnZXRDdXJyZW50VXNlciIsImdldCIsImxvZ291dCIsImFwaUtleUFwaSIsImdldEFsbCIsInVwZGF0ZSIsImlkIiwicHV0IiwiZGVsZXRlIiwidGVzdCIsInN0cmF0ZWd5QXBpIiwiZmlsdGVycyIsInBhcmFtcyIsImdldEJ5SWQiLCJzdGFydCIsInN0b3AiLCJnZXRUeXBlcyIsInZhbGlkYXRlIiwic3RyYXRlZ3lUeXBlIiwicGFyYW1ldGVycyIsInN0cmF0ZWd5X3R5cGUiLCJiYWNrdGVzdCIsInJlc3VsdCIsIm9wdGltaXplIiwib3JkZXJBcGkiLCJjYW5jZWwiLCJ0cmFkZUFwaSIsImdldEJ5U3RyYXRlZ3kiLCJzdHJhdGVneUlkIiwibm90aWZpY2F0aW9uQXBpIiwibWFya0FzUmVhZCIsIm1hcmtBbGxBc1JlYWQiLCJkYXNoYm9hcmRBcGkiLCJnZXRTdGF0cyIsImdldE1hcmtldERhdGEiLCJzeW1ib2xzIiwiam9pbiIsImdldFBvc2l0aW9ucyIsImhhbmRsZUFwaUVycm9yIiwic2hvd0FwaUVycm9yIiwic2hvd0FwaVN1Y2Nlc3MiLCJzdWNjZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        login: async (username, password)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const { access_token, user } = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.authApi.login(username, password);\n                localStorage.setItem(\"access_token\", access_token);\n                set({\n                    user,\n                    token: access_token,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            localStorage.removeItem(\"access_token\");\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false\n            });\n        },\n        getCurrentUser: async ()=>{\n            const token = localStorage.getItem(\"access_token\");\n            if (!token) return;\n            try {\n                const user = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.authApi.getCurrentUser();\n                set({\n                    user,\n                    token,\n                    isAuthenticated: true\n                });\n            } catch (error) {\n                // Token可能已过期\n                get().logout();\n            }\n        },\n        setUser: (user)=>{\n            set({\n                user\n            });\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            token: state.token,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3c3519f0813e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHJhZGluZy1wbGF0Zm9ybS1uZXh0anMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzEzMDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzYzM1MTlmMDgxM2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(rsc)/./src/components/providers/theme-provider.tsx\");\n/* harmony import */ var _components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/auth-provider */ \"(rsc)/./src/components/providers/auth-provider.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Alpha Tool - 量化交易平台\",\n    description: \"专业的量化交易平台，提供策略交易、持仓管理、历史分析等功能\",\n    keywords: \"量化交易,策略交易,Alpha,投资,理财\",\n    authors: [\n        {\n            name: \"Nico投资有道\"\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"hsl(var(--background))\",\n                                color: \"hsl(var(--foreground))\",\n                                border: \"1px solid hsl(var(--border))\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/auth-provider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/auth-provider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/auth-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/auth-provider.tsx#AuthProvider`);


/***/ }),

/***/ "(rsc)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/theme-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/theme-provider.tsx#ThemeProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/zustand","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/lucide-react","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/use-sync-external-store","vendor-chunks/next-themes","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/goober","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();