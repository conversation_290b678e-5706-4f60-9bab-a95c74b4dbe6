/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(ssr)/./src/components/providers/theme-provider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZoZW5pbmd5dSUyRnNvZmV3YXJlJTJGbGF6eSUyRmZyb250ZW5kLW5leHRqcyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRlVzZXJzJTJGaGVuaW5neXUlMkZzb2Zld2FyZSUyRmxhenklMkZmcm9udGVuZC1uZXh0anMlMkZub2RlX21vZHVsZXMlMkZyZWFjdC1ob3QtdG9hc3QlMkZkaXN0JTJGaW5kZXgubWpzJm1vZHVsZXM9JTJGVXNlcnMlMkZoZW5pbmd5dSUyRnNvZmV3YXJlJTJGbGF6eSUyRmZyb250ZW5kLW5leHRqcyUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZoZW5pbmd5dSUyRnNvZmV3YXJlJTJGbGF6eSUyRmZyb250ZW5kLW5leHRqcyUyRnNyYyUyRmNvbXBvbmVudHMlMkZwcm92aWRlcnMlMkZ0aGVtZS1wcm92aWRlci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNNQUE4SDtBQUM5SCIsInNvdXJjZXMiOlsid2VicGFjazovL3RyYWRpbmctcGxhdGZvcm0tbmV4dGpzLz8xMjNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2hlbmluZ3l1L3NvZmV3YXJlL2xhenkvZnJvbnRlbmQtbmV4dGpzL25vZGVfbW9kdWxlcy9yZWFjdC1ob3QtdG9hc3QvZGlzdC9pbmRleC5tanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9oZW5pbmd5dS9zb2Zld2FyZS9sYXp5L2Zyb250ZW5kLW5leHRqcy9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvdGhlbWUtcHJvdmlkZXIudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fpage.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fpage.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZoZW5pbmd5dSUyRnNvZmV3YXJlJTJGbGF6eSUyRmZyb250ZW5kLW5leHRqcyUyRnNyYyUyRmFwcCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3RyYWRpbmctcGxhdGZvcm0tbmV4dGpzLz9hMmM2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2hlbmluZ3l1L3NvZmV3YXJlL2xhenkvZnJvbnRlbmQtbmV4dGpzL3NyYy9hcHAvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(ssr)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/notification-banner */ \"(ssr)/./src/components/ui/notification-banner.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/dashboard-stats */ \"(ssr)/./src/components/dashboard/dashboard-stats.tsx\");\n/* harmony import */ var _components_dashboard_recent_trades__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/recent-trades */ \"(ssr)/./src/components/dashboard/recent-trades.tsx\");\n/* harmony import */ var _components_dashboard_market_overview__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/market-overview */ \"(ssr)/./src/components/dashboard/market-overview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"仪表板\",\n        subtitle: \"量化交易平台概览\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__.NotificationBanner, {\n                            type: \"warning\",\n                            message: \"市场波动较大，请注意风险控制！\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__.NotificationBanner, {\n                            type: \"info\",\n                            message: \"新增DCA和均线策略，欢迎体验！\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__.NotificationBanner, {\n                            type: \"success\",\n                            message: \"系统运行正常，所有API连接稳定！\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_stats__WEBPACK_IMPORTED_MODULE_4__.DashboardStats, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_market_overview__WEBPACK_IMPORTED_MODULE_6__.MarketOverview, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_trades__WEBPACK_IMPORTED_MODULE_5__.RecentTrades, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/dashboard-stats.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/dashboard-stats.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStats: () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardStats auto */ \n\n\n\n\n\n\nfunction DashboardStats() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchStats = async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.dashboardApi.getStats();\n                setStats(data);\n            } catch (error) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.showApiError)(error);\n                // 使用模拟数据作为后备\n                setStats({\n                    total_strategies: 8,\n                    active_strategies: 5,\n                    total_profit: 2450.75,\n                    total_trades: 156,\n                    win_rate: 73.2,\n                    best_strategy: {\n                        name: \"BTC网格策略\",\n                        profit: 1250.50\n                    },\n                    recent_trades: []\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchStats();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n            children: [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            className: \"pb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-muted rounded w-1/2 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-muted rounded w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) return null;\n    const profitColor = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getProfitColor)(stats.total_profit);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"总策略数\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: stats.total_strategies\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"活跃策略: \",\n                                    stats.active_strategies\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"活跃策略\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: stats.active_strategies\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"运行中的策略数量\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"总盈亏\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            stats.total_profit >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-2xl font-bold ${profitColor}`,\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(stats.total_profit)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"累计盈亏金额\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"总交易数\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: stats.total_trades\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"累计交易次数\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"胜率\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: [\n                                    stats.win_rate.toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"盈利交易占比\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"最佳策略\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                variant: \"secondary\",\n                                className: \"text-xs\",\n                                children: \"TOP\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium truncate mb-1\",\n                                children: stats.best_strategy.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-lg font-bold ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getProfitColor)(stats.best_strategy.profit)}`,\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(stats.best_strategy.profit)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/dashboard-stats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/market-overview.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/market-overview.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverview: () => (/* binding */ MarketOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketOverview auto */ \n\n\n\n\n\nfunction MarketOverview() {\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMarketData = async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.dashboardApi.getMarketData([\n                    \"BTCUSDT\",\n                    \"ETHUSDT\",\n                    \"BNBUSDT\",\n                    \"ADAUSDT\"\n                ]);\n                setMarketData(data);\n            } catch (error) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.showApiError)(error);\n                // 使用模拟数据作为后备\n                setMarketData([\n                    {\n                        symbol: \"BTCUSDT\",\n                        price: 43250.50,\n                        change_24h: 1250.75,\n                        change_24h_pct: 2.98,\n                        volume_24h: 28450000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"ETHUSDT\",\n                        price: 2650.75,\n                        change_24h: -85.25,\n                        change_24h_pct: -3.11,\n                        volume_24h: 15230000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"BNBUSDT\",\n                        price: 315.80,\n                        change_24h: 12.45,\n                        change_24h_pct: 4.10,\n                        volume_24h: 1850000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"ADAUSDT\",\n                        price: 0.4825,\n                        change_24h: 0.0125,\n                        change_24h_pct: 2.66,\n                        volume_24h: 425000000,\n                        updated_at: new Date().toISOString()\n                    }\n                ]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchMarketData();\n        // 每30秒更新一次市场数据\n        const interval = setInterval(fetchMarketData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"市场概览\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"主要交易对价格动态\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 bg-muted rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-muted rounded w-16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted rounded w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            \"市场概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"主要交易对价格动态\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: marketData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"暂无市场数据\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : marketData.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex h-8 w-8 items-center justify-center rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-primary\",\n                                                    children: market.symbol.replace(\"USDT\", \"\").slice(0, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: market.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            \"Vol: \",\n                                                            (market.volume_24h / 1000000000).toFixed(2),\n                                                            \"B\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    \"$\",\n                                                    market.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    market.change_24h_pct >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-3 w-3 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-sm font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getProfitColor)(market.change_24h_pct)}`,\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(market.change_24h_pct)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, market.symbol, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    marketData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-4 border-t\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"最后更新\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: new Date().toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/market-overview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/recent-trades.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/recent-trades.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentTrades: () => (/* binding */ RecentTrades)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentTrades auto */ \n\n\n\n\n\n\nfunction RecentTrades() {\n    const [trades, setTrades] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchRecentTrades = async ()=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradeApi.getAll({\n                    page: 1,\n                    size: 10\n                });\n                setTrades(response.items);\n            } catch (error) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_4__.showApiError)(error);\n                // 使用模拟数据作为后备\n                setTrades([\n                    {\n                        id: 1,\n                        strategy_id: 1,\n                        symbol: \"BTCUSDT\",\n                        side: \"buy\",\n                        amount: 0.001,\n                        price: 43250.50,\n                        fee: 0.43,\n                        profit: 12.50,\n                        created_at: new Date().toISOString()\n                    },\n                    {\n                        id: 2,\n                        strategy_id: 2,\n                        symbol: \"ETHUSDT\",\n                        side: \"sell\",\n                        amount: 0.05,\n                        price: 2650.75,\n                        fee: 0.13,\n                        profit: -5.25,\n                        created_at: new Date(Date.now() - 3600000).toISOString()\n                    },\n                    {\n                        id: 3,\n                        strategy_id: 1,\n                        symbol: \"BTCUSDT\",\n                        side: \"sell\",\n                        amount: 0.001,\n                        price: 43380.25,\n                        fee: 0.43,\n                        profit: 25.75,\n                        created_at: new Date(Date.now() - 7200000).toISOString()\n                    }\n                ]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchRecentTrades();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"最近交易\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"最新的交易记录\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 bg-muted rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-muted rounded w-16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted rounded w-16 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted rounded w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        children: \"最近交易\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"最新的交易记录\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: trades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"暂无交易记录\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this) : trades.map((trade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex h-8 w-8 items-center justify-center rounded-full ${trade.side === \"buy\" ? \"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400\" : \"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400\"}`,\n                                            children: trade.side === \"buy\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: trade.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: trade.side === \"buy\" ? \"买入\" : \"卖出\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        trade.amount,\n                                                        \" @ \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(trade.price)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        trade.profit !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getProfitColor)(trade.profit)}`,\n                                            children: [\n                                                trade.profit >= 0 ? \"+\" : \"\",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(trade.profit)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(trade.created_at)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, trade.id, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvcmVjZW50LXRyYWRlcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUVrRDtBQUNnQjtBQUM4QjtBQUNuRDtBQUNLO0FBRXNCO0FBRWpFLFNBQVNpQjtJQUNkLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHakIsK0NBQVFBLENBQVUsRUFBRTtJQUNoRCxNQUFNLENBQUNrQixXQUFXQyxhQUFhLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUUzQ0QsZ0RBQVNBLENBQUM7UUFDUixNQUFNcUIsb0JBQW9CO1lBQ3hCLElBQUk7Z0JBQ0YsTUFBTUMsV0FBVyxNQUFNWCw4Q0FBUUEsQ0FBQ1ksTUFBTSxDQUFDO29CQUFFQyxNQUFNO29CQUFHQyxNQUFNO2dCQUFHO2dCQUMzRFAsVUFBVUksU0FBU0ksS0FBSztZQUMxQixFQUFFLE9BQU9DLE9BQU87Z0JBQ2RmLHNEQUFZQSxDQUFDZTtnQkFDYixhQUFhO2dCQUNiVCxVQUFVO29CQUNSO3dCQUNFVSxJQUFJO3dCQUNKQyxhQUFhO3dCQUNiQyxRQUFRO3dCQUNSQyxNQUFNO3dCQUNOQyxRQUFRO3dCQUNSQyxPQUFPO3dCQUNQQyxLQUFLO3dCQUNMQyxRQUFRO3dCQUNSQyxZQUFZLElBQUlDLE9BQU9DLFdBQVc7b0JBQ3BDO29CQUNBO3dCQUNFVixJQUFJO3dCQUNKQyxhQUFhO3dCQUNiQyxRQUFRO3dCQUNSQyxNQUFNO3dCQUNOQyxRQUFRO3dCQUNSQyxPQUFPO3dCQUNQQyxLQUFLO3dCQUNMQyxRQUFRLENBQUM7d0JBQ1RDLFlBQVksSUFBSUMsS0FBS0EsS0FBS0UsR0FBRyxLQUFLLFNBQVNELFdBQVc7b0JBQ3hEO29CQUNBO3dCQUNFVixJQUFJO3dCQUNKQyxhQUFhO3dCQUNiQyxRQUFRO3dCQUNSQyxNQUFNO3dCQUNOQyxRQUFRO3dCQUNSQyxPQUFPO3dCQUNQQyxLQUFLO3dCQUNMQyxRQUFRO3dCQUNSQyxZQUFZLElBQUlDLEtBQUtBLEtBQUtFLEdBQUcsS0FBSyxTQUFTRCxXQUFXO29CQUN4RDtpQkFDRDtZQUNILFNBQVU7Z0JBQ1JsQixhQUFhO1lBQ2Y7UUFDRjtRQUVBQztJQUNGLEdBQUcsRUFBRTtJQUVMLElBQUlGLFdBQVc7UUFDYixxQkFDRSw4REFBQ2QscURBQUlBOzs4QkFDSCw4REFBQ0csMkRBQVVBOztzQ0FDVCw4REFBQ0MsMERBQVNBO3NDQUFDOzs7Ozs7c0NBQ1gsOERBQUNGLGdFQUFlQTtzQ0FBQzs7Ozs7Ozs7Ozs7OzhCQUVuQiw4REFBQ0QsNERBQVdBOzhCQUNWLDRFQUFDa0M7d0JBQUlDLFdBQVU7a0NBQ1o7K0JBQUlDLE1BQU07eUJBQUcsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUNyQiw4REFBQ0w7Z0NBQVlDLFdBQVU7O2tEQUNyQiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDRDs7a0VBQ0MsOERBQUNBO3dEQUFJQyxXQUFVOzs7Ozs7a0VBQ2YsOERBQUNEO3dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR25CLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7MERBQ2YsOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OytCQVZUSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBa0J0QjtJQUVBLHFCQUNFLDhEQUFDeEMscURBQUlBOzswQkFDSCw4REFBQ0csMkRBQVVBOztrQ0FDVCw4REFBQ0MsMERBQVNBO2tDQUFDOzs7Ozs7a0NBQ1gsOERBQUNGLGdFQUFlQTtrQ0FBQzs7Ozs7Ozs7Ozs7OzBCQUVuQiw4REFBQ0QsNERBQVdBOzBCQUNWLDRFQUFDa0M7b0JBQUlDLFdBQVU7OEJBQ1p4QixPQUFPNkIsTUFBTSxLQUFLLGtCQUNqQiw4REFBQ047d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDckMsNkdBQUtBO2dDQUFDcUMsV0FBVTs7Ozs7OzBDQUNqQiw4REFBQ007MENBQUU7Ozs7Ozs7Ozs7OytCQUdMOUIsT0FBTzBCLEdBQUcsQ0FBQyxDQUFDSyxzQkFDViw4REFBQ1I7NEJBQW1CQyxXQUFVOzs4Q0FDNUIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVcsQ0FBQyxzREFBc0QsRUFDckVPLE1BQU1qQixJQUFJLEtBQUssUUFDWCxzRUFDQSw0REFDTCxDQUFDO3NEQUNDaUIsTUFBTWpCLElBQUksS0FBSyxzQkFDZCw4REFBQzdCLDZHQUFZQTtnREFBQ3VDLFdBQVU7Ozs7O3FFQUV4Qiw4REFBQ3RDLDZHQUFjQTtnREFBQ3NDLFdBQVU7Ozs7Ozs7Ozs7O3NEQUc5Qiw4REFBQ0Q7OzhEQUNDLDhEQUFDQTtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNROzREQUFLUixXQUFVO3NFQUFlTyxNQUFNbEIsTUFBTTs7Ozs7O3NFQUMzQyw4REFBQ3BCLHVEQUFLQTs0REFBQ3dDLFNBQVE7NERBQVVULFdBQVU7c0VBQ2hDTyxNQUFNakIsSUFBSSxLQUFLLFFBQVEsT0FBTzs7Ozs7Ozs7Ozs7OzhEQUduQyw4REFBQ1M7b0RBQUlDLFdBQVU7O3dEQUNaTyxNQUFNaEIsTUFBTTt3REFBQzt3REFBSW5CLDBEQUFjQSxDQUFDbUMsTUFBTWYsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJbEQsOERBQUNPO29DQUFJQyxXQUFVOzt3Q0FDWk8sTUFBTWIsTUFBTSxLQUFLZ0IsMkJBQ2hCLDhEQUFDWDs0Q0FBSUMsV0FBVyxDQUFDLFlBQVksRUFBRTFCLDBEQUFjQSxDQUFDaUMsTUFBTWIsTUFBTSxFQUFFLENBQUM7O2dEQUMxRGEsTUFBTWIsTUFBTSxJQUFJLElBQUksTUFBTTtnREFBSXRCLDBEQUFjQSxDQUFDbUMsTUFBTWIsTUFBTTs7Ozs7OztzREFHOUQsOERBQUNLOzRDQUFJQyxXQUFVO3NEQUNaM0Isc0RBQVVBLENBQUNrQyxNQUFNWixVQUFVOzs7Ozs7Ozs7Ozs7OzJCQWhDeEJZLE1BQU1wQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUEwQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHJhZGluZy1wbGF0Zm9ybS1uZXh0anMvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvcmVjZW50LXRyYWRlcy50c3g/MTNhNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBBcnJvd1VwUmlnaHQsIEFycm93RG93blJpZ2h0LCBDbG9jayB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiXG5pbXBvcnQgeyB0cmFkZUFwaSwgc2hvd0FwaUVycm9yIH0gZnJvbSBcIkAvbGliL2FwaVwiXG5pbXBvcnQgeyBUcmFkZSB9IGZyb20gXCJAL3R5cGVzXCJcbmltcG9ydCB7IGZvcm1hdEN1cnJlbmN5LCBmb3JtYXREYXRlLCBnZXRQcm9maXRDb2xvciB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBmdW5jdGlvbiBSZWNlbnRUcmFkZXMoKSB7XG4gIGNvbnN0IFt0cmFkZXMsIHNldFRyYWRlc10gPSB1c2VTdGF0ZTxUcmFkZVtdPihbXSlcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBmZXRjaFJlY2VudFRyYWRlcyA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdHJhZGVBcGkuZ2V0QWxsKHsgcGFnZTogMSwgc2l6ZTogMTAgfSlcbiAgICAgICAgc2V0VHJhZGVzKHJlc3BvbnNlLml0ZW1zKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgc2hvd0FwaUVycm9yKGVycm9yKVxuICAgICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja7kvZzkuLrlkI7lpIdcbiAgICAgICAgc2V0VHJhZGVzKFtcbiAgICAgICAgICB7XG4gICAgICAgICAgICBpZDogMSxcbiAgICAgICAgICAgIHN0cmF0ZWd5X2lkOiAxLFxuICAgICAgICAgICAgc3ltYm9sOiBcIkJUQ1VTRFRcIixcbiAgICAgICAgICAgIHNpZGU6IFwiYnV5XCIsXG4gICAgICAgICAgICBhbW91bnQ6IDAuMDAxLFxuICAgICAgICAgICAgcHJpY2U6IDQzMjUwLjUwLFxuICAgICAgICAgICAgZmVlOiAwLjQzLFxuICAgICAgICAgICAgcHJvZml0OiAxMi41MCxcbiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH0sXG4gICAgICAgICAge1xuICAgICAgICAgICAgaWQ6IDIsXG4gICAgICAgICAgICBzdHJhdGVneV9pZDogMixcbiAgICAgICAgICAgIHN5bWJvbDogXCJFVEhVU0RUXCIsXG4gICAgICAgICAgICBzaWRlOiBcInNlbGxcIixcbiAgICAgICAgICAgIGFtb3VudDogMC4wNSxcbiAgICAgICAgICAgIHByaWNlOiAyNjUwLjc1LFxuICAgICAgICAgICAgZmVlOiAwLjEzLFxuICAgICAgICAgICAgcHJvZml0OiAtNS4yNSxcbiAgICAgICAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKERhdGUubm93KCkgLSAzNjAwMDAwKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBpZDogMyxcbiAgICAgICAgICAgIHN0cmF0ZWd5X2lkOiAxLFxuICAgICAgICAgICAgc3ltYm9sOiBcIkJUQ1VTRFRcIixcbiAgICAgICAgICAgIHNpZGU6IFwic2VsbFwiLFxuICAgICAgICAgICAgYW1vdW50OiAwLjAwMSxcbiAgICAgICAgICAgIHByaWNlOiA0MzM4MC4yNSxcbiAgICAgICAgICAgIGZlZTogMC40MyxcbiAgICAgICAgICAgIHByb2ZpdDogMjUuNzUsXG4gICAgICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gNzIwMDAwMCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH1cbiAgICAgICAgXSlcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICB9XG5cbiAgICBmZXRjaFJlY2VudFRyYWRlcygpXG4gIH0sIFtdKVxuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPENhcmQ+XG4gICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkVGl0bGU+5pyA6L+R5Lqk5piTPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj7mnIDmlrDnmoTkuqTmmJPorrDlvZU8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIHtbLi4uQXJyYXkoNSldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggYmctbXV0ZWQgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1tdXRlZCByb3VuZGVkIHctMjAgbWItMVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1tdXRlZCByb3VuZGVkIHctMTZcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctbXV0ZWQgcm91bmRlZCB3LTE2IG1iLTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zIGJnLW11dGVkIHJvdW5kZWQgdy0xMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgPC9DYXJkPlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPENhcmQ+XG4gICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRUaXRsZT7mnIDov5HkuqTmmJM8L0NhcmRUaXRsZT5cbiAgICAgICAgPENhcmREZXNjcmlwdGlvbj7mnIDmlrDnmoTkuqTmmJPorrDlvZU8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICB7dHJhZGVzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtOCB3LTggbXgtYXV0byBtYi0yIG9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgICA8cD7mmoLml6DkuqTmmJPorrDlvZU8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgdHJhZGVzLm1hcCgodHJhZGUpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e3RyYWRlLmlkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaC04IHctOCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgIHRyYWRlLnNpZGUgPT09ICdidXknIFxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTYwMCBkYXJrOmJnLWdyZWVuLTkwMCBkYXJrOnRleHQtZ3JlZW4tNDAwJyBcbiAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1yZWQtMTAwIHRleHQtcmVkLTYwMCBkYXJrOmJnLXJlZC05MDAgZGFyazp0ZXh0LXJlZC00MDAnXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHt0cmFkZS5zaWRlID09PSAnYnV5JyA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dVcFJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd0Rvd25SaWdodCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3RyYWRlLnN5bWJvbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3RyYWRlLnNpZGUgPT09ICdidXknID8gJ+S5sOWFpScgOiAn5Y2W5Ye6J31cbiAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHt0cmFkZS5hbW91bnR9IEAge2Zvcm1hdEN1cnJlbmN5KHRyYWRlLnByaWNlKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgIHt0cmFkZS5wcm9maXQgIT09IHVuZGVmaW5lZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZm9udC1tZWRpdW0gJHtnZXRQcm9maXRDb2xvcih0cmFkZS5wcm9maXQpfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHt0cmFkZS5wcm9maXQgPj0gMCA/ICcrJyA6ICcnfXtmb3JtYXRDdXJyZW5jeSh0cmFkZS5wcm9maXQpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKHRyYWRlLmNyZWF0ZWRfYXQpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSlcbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgPC9DYXJkPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkFycm93VXBSaWdodCIsIkFycm93RG93blJpZ2h0IiwiQ2xvY2siLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQmFkZ2UiLCJ0cmFkZUFwaSIsInNob3dBcGlFcnJvciIsImZvcm1hdEN1cnJlbmN5IiwiZm9ybWF0RGF0ZSIsImdldFByb2ZpdENvbG9yIiwiUmVjZW50VHJhZGVzIiwidHJhZGVzIiwic2V0VHJhZGVzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZmV0Y2hSZWNlbnRUcmFkZXMiLCJyZXNwb25zZSIsImdldEFsbCIsInBhZ2UiLCJzaXplIiwiaXRlbXMiLCJlcnJvciIsImlkIiwic3RyYXRlZ3lfaWQiLCJzeW1ib2wiLCJzaWRlIiwiYW1vdW50IiwicHJpY2UiLCJmZWUiLCJwcm9maXQiLCJjcmVhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwibm93IiwiZGl2IiwiY2xhc3NOYW1lIiwiQXJyYXkiLCJtYXAiLCJfIiwiaSIsImxlbmd0aCIsInAiLCJ0cmFkZSIsInNwYW4iLCJ2YXJpYW50IiwidW5kZWZpbmVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/recent-trades.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Search_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Search,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Search_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Search,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Moon_Search_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Search,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\nfunction Header({ title = \"Alpha Tool\", subtitle }) {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"flex h-16 items-center justify-between border-b bg-background px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-orange-500\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative hidden md:block\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Search_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"搜索策略、交易对...\",\n                                className: \"w-64 pl-9\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Search_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Search_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"切换主题\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/header.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFeUI7QUFDdUI7QUFDVjtBQUNTO0FBQ0Y7QUFPdEMsU0FBU08sT0FBTyxFQUFFQyxRQUFRLFlBQVksRUFBRUMsUUFBUSxFQUFlO0lBQ3BFLE1BQU0sRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQUUsR0FBR1AscURBQVFBO0lBRXBDLHFCQUNFLDhEQUFDUTtRQUFPQyxXQUFVOzswQkFFaEIsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQzs7c0NBQ0MsOERBQUNDOzRCQUFHRixXQUFVO3NDQUF5Qkw7Ozs7Ozt3QkFDdENDLDBCQUNDLDhEQUFDTzs0QkFBRUgsV0FBVTtzQ0FBMkJKOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFNOUMsOERBQUNLO2dCQUFJRCxXQUFVOztrQ0FFYiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDWiwyRkFBTUE7Z0NBQUNZLFdBQVU7Ozs7OzswQ0FDbEIsOERBQUNQLHVEQUFLQTtnQ0FDSlcsYUFBWTtnQ0FDWkosV0FBVTs7Ozs7Ozs7Ozs7O2tDQUtkLDhEQUFDUix5REFBTUE7d0JBQ0xhLFNBQVE7d0JBQ1JDLE1BQUs7d0JBQ0xDLFNBQVMsSUFBTVQsU0FBU0QsVUFBVSxTQUFTLFVBQVU7OzBDQUVyRCw4REFBQ1AsMkZBQUdBO2dDQUFDVSxXQUFVOzs7Ozs7MENBQ2YsOERBQUNYLDJGQUFJQTtnQ0FBQ1csV0FBVTs7Ozs7OzBDQUNoQiw4REFBQ1E7Z0NBQUtSLFdBQVU7MENBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtwQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RyYWRpbmctcGxhdGZvcm0tbmV4dGpzLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L2hlYWRlci50c3g/NGFjMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNlYXJjaCwgTW9vbiwgU3VuIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuXG5pbnRlcmZhY2UgSGVhZGVyUHJvcHMge1xuICB0aXRsZT86IHN0cmluZ1xuICBzdWJ0aXRsZT86IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gSGVhZGVyKHsgdGl0bGUgPSBcIkFscGhhIFRvb2xcIiwgc3VidGl0bGUgfTogSGVhZGVyUHJvcHMpIHtcbiAgY29uc3QgeyB0aGVtZSwgc2V0VGhlbWUgfSA9IHVzZVRoZW1lKClcblxuICByZXR1cm4gKFxuICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBoLTE2IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gYm9yZGVyLWIgYmctYmFja2dyb3VuZCBweC02XCI+XG4gICAgICB7LyogTGVmdCBzaWRlIC0gVGl0bGUgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZFwiPnt0aXRsZX08L2gxPlxuICAgICAgICAgIHtzdWJ0aXRsZSAmJiAoXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtb3JhbmdlLTUwMFwiPntzdWJ0aXRsZX08L3A+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFJpZ2h0IHNpZGUgLSBBY3Rpb25zICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICB7LyogU2VhcmNoICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGhpZGRlbiBtZDpibG9ja1wiPlxuICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgaC00IHctNCAtdHJhbnNsYXRlLXktMS8yIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuaQnOe0ouetlueVpeOAgeS6pOaYk+WvuS4uLlwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTY0IHBsLTlcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUaGVtZSBUb2dnbGUgKi99XG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRUaGVtZSh0aGVtZSA9PT0gXCJkYXJrXCIgPyBcImxpZ2h0XCIgOiBcImRhcmtcIil9XG4gICAgICAgID5cbiAgICAgICAgICA8U3VuIGNsYXNzTmFtZT1cImgtNCB3LTQgcm90YXRlLTAgc2NhbGUtMTAwIHRyYW5zaXRpb24tYWxsIGRhcms6LXJvdGF0ZS05MCBkYXJrOnNjYWxlLTBcIiAvPlxuICAgICAgICAgIDxNb29uIGNsYXNzTmFtZT1cImFic29sdXRlIGgtNCB3LTQgcm90YXRlLTkwIHNjYWxlLTAgdHJhbnNpdGlvbi1hbGwgZGFyazpyb3RhdGUtMCBkYXJrOnNjYWxlLTEwMFwiIC8+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPuWIh+aNouS4u+mimDwvc3Bhbj5cbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2hlYWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2VhcmNoIiwiTW9vbiIsIlN1biIsInVzZVRoZW1lIiwiQnV0dG9uIiwiSW5wdXQiLCJIZWFkZXIiLCJ0aXRsZSIsInN1YnRpdGxlIiwidGhlbWUiLCJzZXRUaGVtZSIsImhlYWRlciIsImNsYXNzTmFtZSIsImRpdiIsImgxIiwicCIsInBsYWNlaG9sZGVyIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/main-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/main-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./src/components/layout/sidebar.tsx\");\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./header */ \"(ssr)/./src/components/layout/header.tsx\");\n/* __next_internal_client_entry_do_not_use__ MainLayout auto */ \n\n\n\nfunction MainLayout({ children, title, subtitle }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/main-layout.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                        title: title,\n                        subtitle: subtitle\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/main-layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/main-layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/main-layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/main-layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvbWFpbi1sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXlCO0FBQ1U7QUFDRjtBQVExQixTQUFTRyxXQUFXLEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQW1CO0lBQ3ZFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ1AsNkNBQU9BOzs7OzswQkFHUiw4REFBQ007Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDTiwyQ0FBTUE7d0JBQUNHLE9BQU9BO3dCQUFPQyxVQUFVQTs7Ozs7O2tDQUdoQyw4REFBQ0c7d0JBQUtELFdBQVU7a0NBQ2JKOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL3RyYWRpbmctcGxhdGZvcm0tbmV4dGpzLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L21haW4tbGF5b3V0LnRzeD8wNGQ0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2lkZWJhciB9IGZyb20gXCIuL3NpZGViYXJcIlxuaW1wb3J0IHsgSGVhZGVyIH0gZnJvbSBcIi4vaGVhZGVyXCJcblxuaW50ZXJmYWNlIE1haW5MYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgdGl0bGU/OiBzdHJpbmdcbiAgc3VidGl0bGU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE1haW5MYXlvdXQoeyBjaGlsZHJlbiwgdGl0bGUsIHN1YnRpdGxlIH06IE1haW5MYXlvdXRQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgIDxTaWRlYmFyIC8+XG4gICAgICBcbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8SGVhZGVyIHRpdGxlPXt0aXRsZX0gc3VidGl0bGU9e3N1YnRpdGxlfSAvPlxuICAgICAgICBcbiAgICAgICAgey8qIFBhZ2UgQ29udGVudCAqL31cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTZcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvbWFpbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTaWRlYmFyIiwiSGVhZGVyIiwiTWFpbkxheW91dCIsImNoaWxkcmVuIiwidGl0bGUiLCJzdWJ0aXRsZSIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/main-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,History,Home,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,History,Home,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,History,Home,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,History,Home,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,History,Home,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,History,Home,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./src/components/ui/avatar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"首页\",\n        href: \"/\",\n        icon: _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"最佳实践\",\n        href: \"/strategies\",\n        icon: _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"积分计算器\",\n        href: \"/positions\",\n        icon: _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"规则详解\",\n        href: \"/rules\",\n        icon: _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"空投历史\",\n        href: \"/history\",\n        icon: _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"关于作者\",\n        href: \"/about\",\n        icon: _barrel_optimize_names_BarChart3_History_Home_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    }\n];\nfunction Sidebar({ className }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-full w-64 flex-col bg-white dark:bg-card border-r\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center gap-2 px-6 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-orange-500 text-white font-bold\",\n                        children: \"α\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"Alpha Tool\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-2 text-xs text-orange-500 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block w-4 h-4\",\n                            children: \"\\uD83D\\uDD25\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        \"专为「币安 Alpha 空投」而生\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 p-4\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: item.href,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"sidebar-item\", isActive && \"active\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this),\n                            item.name === \"最佳实践\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-auto h-2 w-2 rounded-full bg-red-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 17\n                            }, this),\n                            item.name === \"积分计算器\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-auto flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 rounded-full bg-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-2 w-2 rounded-full bg-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, item.name, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-auto p-4 border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                            className: \"h-10 w-10 border-2 border-orange-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                    src: \"/avatar.jpg\",\n                                    alt: \"用户头像\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                    children: \"N\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium truncate\",\n                                    children: \"Nico投资有道\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex text-xs space-x-2 text-muted-foreground\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"美股/比特币长期投资\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t flex items-center justify-between text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"hover:text-foreground transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"hover:text-foreground transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.748-1.378 0 0-.599 2.282-.744 2.84-.282 1.084-1.064 2.456-1.549 3.235C9.584 23.815 10.77 24.001 12.017 24.001c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"hover:text-foreground transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"hover:text-foreground transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5\",\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/layout/sidebar.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvdGhlbWUtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHJhZGluZy1wbGF0Zm9ybS1uZXh0anMvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvdGhlbWUtcHJvdmlkZXIudHN4PzU2NzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/avatar.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/avatar.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/avatar.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-success text-success-foreground hover:bg-success/80\",\n            warning: \"border-transparent bg-warning text-warning-foreground hover:bg-warning/80\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/badge.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            success: \"bg-success text-success-foreground hover:bg-success/90\",\n            warning: \"bg-warning text-warning-foreground hover:bg-warning/90\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/button.tsx\",\n        lineNumber: 48,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL3RyYWRpbmctcGxhdGZvcm0tbmV4dGpzLy4vc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2M5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/notification-banner.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/notification-banner.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBanner: () => (/* binding */ NotificationBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Volume2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ NotificationBanner auto */ \n\n\n\n\nconst colorMap = {\n    info: \"bg-blue-50 border-blue-100 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800/30 dark:text-blue-300\",\n    warning: \"bg-yellow-50 border-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800/30 dark:text-yellow-300\",\n    success: \"bg-green-50 border-green-100 text-green-800 dark:bg-green-900/20 dark:border-green-800/30 dark:text-green-300\",\n    error: \"bg-red-50 border-red-100 text-red-800 dark:bg-red-900/20 dark:border-red-800/30 dark:text-red-300\"\n};\nfunction NotificationBanner({ type = \"info\", message, dismissible = true, onDismiss, className }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleDismiss = ()=>{\n        setIsVisible(false);\n        onDismiss?.();\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-3 rounded-lg border px-4 py-3 text-sm\", colorMap[type], className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4 flex-shrink-0\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/notification-banner.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: message\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/notification-banner.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/notification-banner.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            dismissible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                className: \"h-5 w-5 rounded-full opacity-70 hover:opacity-100 hover:bg-transparent\",\n                onClick: handleDismiss,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Volume2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/notification-banner.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/notification-banner.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/ui/notification-banner.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/notification-banner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiKeyApi: () => (/* binding */ apiKeyApi),\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   dashboardApi: () => (/* binding */ dashboardApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   notificationApi: () => (/* binding */ notificationApi),\n/* harmony export */   orderApi: () => (/* binding */ orderApi),\n/* harmony export */   showApiError: () => (/* binding */ showApiError),\n/* harmony export */   showApiSuccess: () => (/* binding */ showApiSuccess),\n/* harmony export */   strategyApi: () => (/* binding */ strategyApi),\n/* harmony export */   tradeApi: () => (/* binding */ tradeApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n// API配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: `${API_BASE_URL}/api/v1`,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    // 添加认证token\n    const token = localStorage.getItem(\"access_token\") || \"debug-token\";\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // 未授权，清除token并跳转到登录页\n        localStorage.removeItem(\"access_token\");\n        window.location.href = \"/login\";\n    } else if (error.response?.status >= 500) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(\"服务器错误，请稍后重试\");\n    } else if (error.response?.data?.message) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(error.response.data.message);\n    }\n    return Promise.reject(error);\n});\n// 认证API\nconst authApi = {\n    login: async (username, password)=>{\n        const response = await apiClient.post(\"/auth/login\", {\n            username,\n            password\n        });\n        return response.data;\n    },\n    register: async (username, email, password)=>{\n        const response = await apiClient.post(\"/auth/register\", {\n            username,\n            email,\n            password\n        });\n        return response.data;\n    },\n    getCurrentUser: async ()=>{\n        const response = await apiClient.get(\"/auth/me\");\n        return response.data;\n    },\n    logout: async ()=>{\n        await apiClient.post(\"/auth/logout\");\n        localStorage.removeItem(\"access_token\");\n    }\n};\n// API密钥管理API\nconst apiKeyApi = {\n    getAll: async ()=>{\n        const response = await apiClient.get(\"/api-keys\");\n        return response.data;\n    },\n    create: async (data)=>{\n        const response = await apiClient.post(\"/api-keys\", data);\n        return response.data;\n    },\n    update: async (id, data)=>{\n        const response = await apiClient.put(`/api-keys/${id}`, data);\n        return response.data;\n    },\n    delete: async (id)=>{\n        await apiClient.delete(`/api-keys/${id}`);\n    },\n    test: async (id)=>{\n        const response = await apiClient.post(`/api-keys/${id}/test`);\n        return response.data;\n    }\n};\n// 策略管理API\nconst strategyApi = {\n    getAll: async (filters)=>{\n        const response = await apiClient.get(\"/strategies\", {\n            params: filters\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await apiClient.get(`/strategies/${id}`);\n        return response.data;\n    },\n    create: async (data)=>{\n        const response = await apiClient.post(\"/strategies\", data);\n        return response.data;\n    },\n    update: async (id, data)=>{\n        const response = await apiClient.put(`/strategies/${id}`, data);\n        return response.data;\n    },\n    delete: async (id)=>{\n        await apiClient.delete(`/strategies/${id}`);\n    },\n    start: async (id)=>{\n        await apiClient.post(`/strategies/${id}/start`);\n    },\n    stop: async (id)=>{\n        await apiClient.post(`/strategies/${id}/stop`);\n    },\n    getTypes: async ()=>{\n        const response = await apiClient.get(\"/strategies/types\");\n        return response.data;\n    },\n    validate: async (strategyType, parameters)=>{\n        const response = await apiClient.post(\"/strategies/validate\", {\n            strategy_type: strategyType,\n            parameters: parameters\n        });\n        return response.data;\n    },\n    backtest: async (request)=>{\n        const response = await apiClient.post(\"/strategies/backtest\", request);\n        return response.data.result;\n    },\n    optimize: async (request)=>{\n        const response = await apiClient.post(\"/strategies/optimize\", request);\n        return response.data.result;\n    }\n};\n// 订单管理API\nconst orderApi = {\n    getAll: async (filters)=>{\n        const response = await apiClient.get(\"/orders\", {\n            params: filters\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await apiClient.get(`/orders/${id}`);\n        return response.data;\n    },\n    cancel: async (id)=>{\n        await apiClient.post(`/orders/${id}/cancel`);\n    }\n};\n// 交易记录API\nconst tradeApi = {\n    getAll: async (filters)=>{\n        const response = await apiClient.get(\"/trades\", {\n            params: filters\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await apiClient.get(`/trades/${id}`);\n        return response.data;\n    },\n    getByStrategy: async (strategyId)=>{\n        const response = await apiClient.get(`/strategies/${strategyId}/trades`);\n        return response.data;\n    }\n};\n// 通知API\nconst notificationApi = {\n    getAll: async ()=>{\n        const response = await apiClient.get(\"/notifications\");\n        return response.data;\n    },\n    markAsRead: async (id)=>{\n        await apiClient.put(`/notifications/${id}/read`);\n    },\n    markAllAsRead: async ()=>{\n        await apiClient.put(\"/notifications/read-all\");\n    },\n    delete: async (id)=>{\n        await apiClient.delete(`/notifications/${id}`);\n    }\n};\n// 仪表板API\nconst dashboardApi = {\n    getStats: async ()=>{\n        const response = await apiClient.get(\"/dashboard/stats\");\n        return response.data;\n    },\n    getMarketData: async (symbols)=>{\n        const response = await apiClient.get(\"/dashboard/market-data\", {\n            params: {\n                symbols: symbols?.join(\",\")\n            }\n        });\n        return response.data;\n    },\n    getPositions: async ()=>{\n        const response = await apiClient.get(\"/dashboard/positions\");\n        return response.data;\n    }\n};\n// 工具函数\nconst handleApiError = (error)=>{\n    if (error.response?.data?.message) {\n        return error.response.data.message;\n    } else if (error.message) {\n        return error.message;\n    } else {\n        return \"未知错误\";\n    }\n};\nconst showApiError = (error)=>{\n    const message = handleApiError(error);\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(message);\n};\nconst showApiSuccess = (message)=>{\n    react_hot_toast__WEBPACK_IMPORTED_MODULE_0__[\"default\"].success(message);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   getProfitColor: () => (/* binding */ getProfitColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"USDT\", decimals = 2) {\n    return `${amount.toFixed(decimals)} ${currency}`;\n}\nfunction formatPercentage(value, decimals = 2) {\n    const sign = value >= 0 ? \"+\" : \"\";\n    return `${sign}${value.toFixed(decimals)}%`;\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"zh-CN\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction formatNumber(num, decimals = 2) {\n    if (num >= 1e9) {\n        return (num / 1e9).toFixed(decimals) + \"B\";\n    }\n    if (num >= 1e6) {\n        return (num / 1e6).toFixed(decimals) + \"M\";\n    }\n    if (num >= 1e3) {\n        return (num / 1e3).toFixed(decimals) + \"K\";\n    }\n    return num.toFixed(decimals);\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case \"running\":\n        case \"active\":\n            return \"text-success\";\n        case \"stopped\":\n        case \"inactive\":\n            return \"text-muted-foreground\";\n        case \"error\":\n        case \"failed\":\n            return \"text-destructive\";\n        case \"paused\":\n            return \"text-warning\";\n        default:\n            return \"text-muted-foreground\";\n    }\n}\nfunction getProfitColor(profit) {\n    if (profit > 0) return \"text-success\";\n    if (profit < 0) return \"text-destructive\";\n    return \"text-muted-foreground\";\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3c3519f0813e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdHJhZGluZy1wbGF0Zm9ybS1uZXh0anMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzEzMDQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzYzM1MTlmMDgxM2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(rsc)/./src/components/providers/theme-provider.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Alpha Tool - 量化交易平台\",\n    description: \"专业的量化交易平台，提供策略交易、持仓管理、历史分析等功能\",\n    keywords: \"量化交易,策略交易,Alpha,投资,理财\",\n    authors: [\n        {\n            name: \"Nico投资有道\"\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"hsl(var(--background))\",\n                                color: \"hsl(var(--foreground))\",\n                                border: \"1px solid hsl(var(--border))\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/theme-provider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/providers/theme-provider.tsx#ThemeProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/lucide-react","vendor-chunks/asynckit","vendor-chunks/@radix-ui","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/use-sync-external-store","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();