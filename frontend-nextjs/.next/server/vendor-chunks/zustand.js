"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createStore: () => (/* reexport safe */ zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore),\n/* harmony export */   \"default\": () => (/* binding */ react),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\");\n\n\n\n\nconst { useDebugValue } = react__WEBPACK_IMPORTED_MODULE_1__;\nconst { useSyncExternalStoreWithSelector } = use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg)=>arg;\nfunction useStore(api, selector = identity, equalityFn) {\n    if (( false ? 0 : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n        console.warn(\"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\");\n        didWarnAboutEqualityFn = true;\n    }\n    const slice = useSyncExternalStoreWithSelector(api.subscribe, api.getState, api.getServerState || api.getInitialState, selector, equalityFn);\n    useDebugValue(slice);\n    return slice;\n}\nconst createImpl = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\" && typeof createState !== \"function\") {\n        console.warn(\"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\");\n    }\n    const api = typeof createState === \"function\" ? (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore)(createState) : createState;\n    const useBoundStore = (selector, equalityFn)=>useStore(api, selector, equalityFn);\n    Object.assign(useBoundStore, api);\n    return useBoundStore;\n};\nconst create = (createState)=>createState ? createImpl(createState) : createImpl;\nvar react = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\");\n    }\n    return create(createState);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial)=>(set, _get, api)=>{\n        api.dispatch = (action)=>{\n            set((state)=>reducer(state, action), false, action);\n            return action;\n        };\n        api.dispatchFromDevtools = true;\n        return {\n            dispatch: (...a)=>api.dispatch(...a),\n            ...initial\n        };\n    };\nconst redux = reduxImpl;\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name)=>{\n    const api = trackedConnections.get(name);\n    if (!api) return {};\n    return Object.fromEntries(Object.entries(api.stores).map(([key, api2])=>[\n            key,\n            api2.getState()\n        ]));\n};\nconst extractConnectionInformation = (store, extensionConnector, options)=>{\n    if (store === void 0) {\n        return {\n            type: \"untracked\",\n            connection: extensionConnector.connect(options)\n        };\n    }\n    const existingConnection = trackedConnections.get(options.name);\n    if (existingConnection) {\n        return {\n            type: \"tracked\",\n            store,\n            ...existingConnection\n        };\n    }\n    const newConnection = {\n        connection: extensionConnector.connect(options),\n        stores: {}\n    };\n    trackedConnections.set(options.name, newConnection);\n    return {\n        type: \"tracked\",\n        store,\n        ...newConnection\n    };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {})=>(set, get, api)=>{\n        const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n        let extensionConnector;\n        try {\n            extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n        } catch (_e) {}\n        if (!extensionConnector) {\n            if (( false ? 0 : void 0) !== \"production\" && enabled) {\n                console.warn(\"[zustand devtools middleware] Please install/enable Redux devtools extension\");\n            }\n            return fn(set, get, api);\n        }\n        const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n        let isRecording = true;\n        api.setState = (state, replace, nameOrAction)=>{\n            const r = set(state, replace);\n            if (!isRecording) return r;\n            const action = nameOrAction === void 0 ? {\n                type: anonymousActionType || \"anonymous\"\n            } : typeof nameOrAction === \"string\" ? {\n                type: nameOrAction\n            } : nameOrAction;\n            if (store === void 0) {\n                connection == null ? void 0 : connection.send(action, get());\n                return r;\n            }\n            connection == null ? void 0 : connection.send({\n                ...action,\n                type: `${store}/${action.type}`\n            }, {\n                ...getTrackedConnectionState(options.name),\n                [store]: api.getState()\n            });\n            return r;\n        };\n        const setStateFromDevtools = (...a)=>{\n            const originalIsRecording = isRecording;\n            isRecording = false;\n            set(...a);\n            isRecording = originalIsRecording;\n        };\n        const initialState = fn(api.setState, get, api);\n        if (connectionInformation.type === \"untracked\") {\n            connection == null ? void 0 : connection.init(initialState);\n        } else {\n            connectionInformation.stores[connectionInformation.store] = api;\n            connection == null ? void 0 : connection.init(Object.fromEntries(Object.entries(connectionInformation.stores).map(([key, store2])=>[\n                    key,\n                    key === connectionInformation.store ? initialState : store2.getState()\n                ])));\n        }\n        if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n            let didWarnAboutReservedActionType = false;\n            const originalDispatch = api.dispatch;\n            api.dispatch = (...a)=>{\n                if (( false ? 0 : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n                    console.warn('[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.');\n                    didWarnAboutReservedActionType = true;\n                }\n                originalDispatch(...a);\n            };\n        }\n        connection.subscribe((message)=>{\n            var _a;\n            switch(message.type){\n                case \"ACTION\":\n                    if (typeof message.payload !== \"string\") {\n                        console.error(\"[zustand devtools middleware] Unsupported action format\");\n                        return;\n                    }\n                    return parseJsonThen(message.payload, (action)=>{\n                        if (action.type === \"__setState\") {\n                            if (store === void 0) {\n                                setStateFromDevtools(action.state);\n                                return;\n                            }\n                            if (Object.keys(action.state).length !== 1) {\n                                console.error(`\n                    [zustand devtools middleware] Unsupported __setState action format. \n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `);\n                            }\n                            const stateFromDevtools = action.state[store];\n                            if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                                return;\n                            }\n                            if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                                setStateFromDevtools(stateFromDevtools);\n                            }\n                            return;\n                        }\n                        if (!api.dispatchFromDevtools) return;\n                        if (typeof api.dispatch !== \"function\") return;\n                        api.dispatch(action);\n                    });\n                case \"DISPATCH\":\n                    switch(message.payload.type){\n                        case \"RESET\":\n                            setStateFromDevtools(initialState);\n                            if (store === void 0) {\n                                return connection == null ? void 0 : connection.init(api.getState());\n                            }\n                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                        case \"COMMIT\":\n                            if (store === void 0) {\n                                connection == null ? void 0 : connection.init(api.getState());\n                                return;\n                            }\n                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                        case \"ROLLBACK\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === void 0) {\n                                    setStateFromDevtools(state);\n                                    connection == null ? void 0 : connection.init(api.getState());\n                                    return;\n                                }\n                                setStateFromDevtools(state[store]);\n                                connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n                            });\n                        case \"JUMP_TO_STATE\":\n                        case \"JUMP_TO_ACTION\":\n                            return parseJsonThen(message.state, (state)=>{\n                                if (store === void 0) {\n                                    setStateFromDevtools(state);\n                                    return;\n                                }\n                                if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                                    setStateFromDevtools(state[store]);\n                                }\n                            });\n                        case \"IMPORT_STATE\":\n                            {\n                                const { nextLiftedState } = message.payload;\n                                const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n                                if (!lastComputedState) return;\n                                if (store === void 0) {\n                                    setStateFromDevtools(lastComputedState);\n                                } else {\n                                    setStateFromDevtools(lastComputedState[store]);\n                                }\n                                connection == null ? void 0 : connection.send(null, // FIXME no-any\n                                nextLiftedState);\n                                return;\n                            }\n                        case \"PAUSE_RECORDING\":\n                            return isRecording = !isRecording;\n                    }\n                    return;\n            }\n        });\n        return initialState;\n    };\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f)=>{\n    let parsed;\n    try {\n        parsed = JSON.parse(stringified);\n    } catch (e) {\n        console.error(\"[zustand devtools middleware] Could not parse the received json\", e);\n    }\n    if (parsed !== void 0) f(parsed);\n};\nconst subscribeWithSelectorImpl = (fn)=>(set, get, api)=>{\n        const origSubscribe = api.subscribe;\n        api.subscribe = (selector, optListener, options)=>{\n            let listener = selector;\n            if (optListener) {\n                const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n                let currentSlice = selector(api.getState());\n                listener = (state)=>{\n                    const nextSlice = selector(state);\n                    if (!equalityFn(currentSlice, nextSlice)) {\n                        const previousSlice = currentSlice;\n                        optListener(currentSlice = nextSlice, previousSlice);\n                    }\n                };\n                if (options == null ? void 0 : options.fireImmediately) {\n                    optListener(currentSlice, currentSlice);\n                }\n            }\n            return origSubscribe(listener);\n        };\n        const initialState = fn(set, get, api);\n        return initialState;\n    };\nconst subscribeWithSelector = subscribeWithSelectorImpl;\nconst combine = (initialState, create)=>(...a)=>Object.assign({}, initialState, create(...a));\nfunction createJSONStorage(getStorage, options) {\n    let storage;\n    try {\n        storage = getStorage();\n    } catch (_e) {\n        return;\n    }\n    const persistStorage = {\n        getItem: (name)=>{\n            var _a;\n            const parse = (str2)=>{\n                if (str2 === null) {\n                    return null;\n                }\n                return JSON.parse(str2, options == null ? void 0 : options.reviver);\n            };\n            const str = (_a = storage.getItem(name)) != null ? _a : null;\n            if (str instanceof Promise) {\n                return str.then(parse);\n            }\n            return parse(str);\n        },\n        setItem: (name, newValue)=>storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n        removeItem: (name)=>storage.removeItem(name)\n    };\n    return persistStorage;\n}\nconst toThenable = (fn)=>(input)=>{\n        try {\n            const result = fn(input);\n            if (result instanceof Promise) {\n                return result;\n            }\n            return {\n                then (onFulfilled) {\n                    return toThenable(onFulfilled)(result);\n                },\n                catch (_onRejected) {\n                    return this;\n                }\n            };\n        } catch (e) {\n            return {\n                then (_onFulfilled) {\n                    return this;\n                },\n                catch (onRejected) {\n                    return toThenable(onRejected)(e);\n                }\n            };\n        }\n    };\nconst oldImpl = (config, baseOptions)=>(set, get, api)=>{\n        let options = {\n            getStorage: ()=>localStorage,\n            serialize: JSON.stringify,\n            deserialize: JSON.parse,\n            partialize: (state)=>state,\n            version: 0,\n            merge: (persistedState, currentState)=>({\n                    ...currentState,\n                    ...persistedState\n                }),\n            ...baseOptions\n        };\n        let hasHydrated = false;\n        const hydrationListeners = /* @__PURE__ */ new Set();\n        const finishHydrationListeners = /* @__PURE__ */ new Set();\n        let storage;\n        try {\n            storage = options.getStorage();\n        } catch (_e) {}\n        if (!storage) {\n            return config((...args)=>{\n                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n                set(...args);\n            }, get, api);\n        }\n        const thenableSerialize = toThenable(options.serialize);\n        const setItem = ()=>{\n            const state = options.partialize({\n                ...get()\n            });\n            let errorInSync;\n            const thenable = thenableSerialize({\n                state,\n                version: options.version\n            }).then((serializedValue)=>storage.setItem(options.name, serializedValue)).catch((e)=>{\n                errorInSync = e;\n            });\n            if (errorInSync) {\n                throw errorInSync;\n            }\n            return thenable;\n        };\n        const savedSetState = api.setState;\n        api.setState = (state, replace)=>{\n            savedSetState(state, replace);\n            void setItem();\n        };\n        const configResult = config((...args)=>{\n            set(...args);\n            void setItem();\n        }, get, api);\n        let stateFromStorage;\n        const hydrate = ()=>{\n            var _a;\n            if (!storage) return;\n            hasHydrated = false;\n            hydrationListeners.forEach((cb)=>cb(get()));\n            const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n            return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue)=>{\n                if (storageValue) {\n                    return options.deserialize(storageValue);\n                }\n            }).then((deserializedStorageValue)=>{\n                if (deserializedStorageValue) {\n                    if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n                        if (options.migrate) {\n                            return options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);\n                        }\n                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n                    } else {\n                        return deserializedStorageValue.state;\n                    }\n                }\n            }).then((migratedState)=>{\n                var _a2;\n                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n                set(stateFromStorage, true);\n                return setItem();\n            }).then(()=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n                hasHydrated = true;\n                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));\n            }).catch((e)=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n            });\n        };\n        api.persist = {\n            setOptions: (newOptions)=>{\n                options = {\n                    ...options,\n                    ...newOptions\n                };\n                if (newOptions.getStorage) {\n                    storage = newOptions.getStorage();\n                }\n            },\n            clearStorage: ()=>{\n                storage == null ? void 0 : storage.removeItem(options.name);\n            },\n            getOptions: ()=>options,\n            rehydrate: ()=>hydrate(),\n            hasHydrated: ()=>hasHydrated,\n            onHydrate: (cb)=>{\n                hydrationListeners.add(cb);\n                return ()=>{\n                    hydrationListeners.delete(cb);\n                };\n            },\n            onFinishHydration: (cb)=>{\n                finishHydrationListeners.add(cb);\n                return ()=>{\n                    finishHydrationListeners.delete(cb);\n                };\n            }\n        };\n        hydrate();\n        return stateFromStorage || configResult;\n    };\nconst newImpl = (config, baseOptions)=>(set, get, api)=>{\n        let options = {\n            storage: createJSONStorage(()=>localStorage),\n            partialize: (state)=>state,\n            version: 0,\n            merge: (persistedState, currentState)=>({\n                    ...currentState,\n                    ...persistedState\n                }),\n            ...baseOptions\n        };\n        let hasHydrated = false;\n        const hydrationListeners = /* @__PURE__ */ new Set();\n        const finishHydrationListeners = /* @__PURE__ */ new Set();\n        let storage = options.storage;\n        if (!storage) {\n            return config((...args)=>{\n                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);\n                set(...args);\n            }, get, api);\n        }\n        const setItem = ()=>{\n            const state = options.partialize({\n                ...get()\n            });\n            return storage.setItem(options.name, {\n                state,\n                version: options.version\n            });\n        };\n        const savedSetState = api.setState;\n        api.setState = (state, replace)=>{\n            savedSetState(state, replace);\n            void setItem();\n        };\n        const configResult = config((...args)=>{\n            set(...args);\n            void setItem();\n        }, get, api);\n        api.getInitialState = ()=>configResult;\n        let stateFromStorage;\n        const hydrate = ()=>{\n            var _a, _b;\n            if (!storage) return;\n            hasHydrated = false;\n            hydrationListeners.forEach((cb)=>{\n                var _a2;\n                return cb((_a2 = get()) != null ? _a2 : configResult);\n            });\n            const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n            return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue)=>{\n                if (deserializedStorageValue) {\n                    if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n                        if (options.migrate) {\n                            return [\n                                true,\n                                options.migrate(deserializedStorageValue.state, deserializedStorageValue.version)\n                            ];\n                        }\n                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);\n                    } else {\n                        return [\n                            false,\n                            deserializedStorageValue.state\n                        ];\n                    }\n                }\n                return [\n                    false,\n                    void 0\n                ];\n            }).then((migrationResult)=>{\n                var _a2;\n                const [migrated, migratedState] = migrationResult;\n                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);\n                set(stateFromStorage, true);\n                if (migrated) {\n                    return setItem();\n                }\n            }).then(()=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n                stateFromStorage = get();\n                hasHydrated = true;\n                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));\n            }).catch((e)=>{\n                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n            });\n        };\n        api.persist = {\n            setOptions: (newOptions)=>{\n                options = {\n                    ...options,\n                    ...newOptions\n                };\n                if (newOptions.storage) {\n                    storage = newOptions.storage;\n                }\n            },\n            clearStorage: ()=>{\n                storage == null ? void 0 : storage.removeItem(options.name);\n            },\n            getOptions: ()=>options,\n            rehydrate: ()=>hydrate(),\n            hasHydrated: ()=>hasHydrated,\n            onHydrate: (cb)=>{\n                hydrationListeners.add(cb);\n                return ()=>{\n                    hydrationListeners.delete(cb);\n                };\n            },\n            onFinishHydration: (cb)=>{\n                finishHydrationListeners.add(cb);\n                return ()=>{\n                    finishHydrationListeners.delete(cb);\n                };\n            }\n        };\n        if (!options.skipHydration) {\n            hydrate();\n        }\n        return stateFromStorage || configResult;\n    };\nconst persistImpl = (config, baseOptions)=>{\n    if (\"getStorage\" in baseOptions || \"serialize\" in baseOptions || \"deserialize\" in baseOptions) {\n        if (( false ? 0 : void 0) !== \"production\") {\n            console.warn(\"[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead.\");\n        }\n        return oldImpl(config, baseOptions);\n    }\n    return newImpl(config, baseOptions);\n};\nconst persist = persistImpl;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   \"default\": () => (/* binding */ vanilla)\n/* harmony export */ });\nconst createStoreImpl = (createState)=>{\n    let state;\n    const listeners = /* @__PURE__ */ new Set();\n    const setState = (partial, replace)=>{\n        const nextState = typeof partial === \"function\" ? partial(state) : partial;\n        if (!Object.is(nextState, state)) {\n            const previousState = state;\n            state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n            listeners.forEach((listener)=>listener(state, previousState));\n        }\n    };\n    const getState = ()=>state;\n    const getInitialState = ()=>initialState;\n    const subscribe = (listener)=>{\n        listeners.add(listener);\n        return ()=>listeners.delete(listener);\n    };\n    const destroy = ()=>{\n        if (( false ? 0 : void 0) !== \"production\") {\n            console.warn(\"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\");\n        }\n        listeners.clear();\n    };\n    const api = {\n        setState,\n        getState,\n        getInitialState,\n        subscribe,\n        destroy\n    };\n    const initialState = state = createState(setState, getState, api);\n    return api;\n};\nconst createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState)=>{\n    if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\");\n    }\n    return createStore(createState);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;