"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name: name,\n            formState: _localProxyFormState.current,\n            exact,\n            callback: (formState)=>{\n                !disabled && updateFormState({\n                    ...control._formState,\n                    ...formState\n                });\n            }\n        }), [\n        name,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name: name,\n            formState: {\n                values: true\n            },\n            exact,\n            callback: (formState)=>!disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n        }), [\n        name,\n        control,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name\n            },\n            type: EVENTS.CHANGE\n        }), [\n        name\n    ]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name\n            },\n            type: EVENTS.BLUR\n        }), [\n        name,\n        control._formValues\n    ]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: ()=>elm.focus(),\n                select: ()=>elm.select(),\n                setCustomValidity: (message)=>elm.setCustomValidity(message),\n                reportValidity: ()=>elm.reportValidity()\n            };\n        }\n    }, [\n        control._fields,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange,\n            onBlur,\n            ref\n        }), [\n        name,\n        disabled,\n        formState.disabled,\n        onChange,\n        onBlur,\n        ref,\n        value1\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...isBoolean(_props.current.disabled) ? {\n                disabled: _props.current.disabled\n            } : {}\n        });\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._setDisabledField({\n            disabled,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar isRegex = (value1)=>value1 instanceof RegExp;\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isMessage = (value1)=>isString(value1);\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    const _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _setValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef)=>{\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data)=>data === checkboxRef.value);\n                                } else {\n                                    checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues)\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            if (!value1.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value1[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues)\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues)\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.state.next({\n                name,\n                type: event.type,\n                values: cloneObject(_formValues)\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    } else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.state.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const _subscribe = (props)=>_subjects.state.subscribe({\n            next: (formState)=>{\n                if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                    props.callback({\n                        values: {\n                            ..._formValues\n                        },\n                        ..._formState,\n                        ...formState\n                    });\n                }\n            }\n        }).unsubscribe;\n    const subscribe = (props)=>{\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState\n        });\n    };\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues)\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _runSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    set(fieldValues, name, undefined);\n                }\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                for (const fieldName of _names.mount){\n                    setValue(fieldName, get(values, fieldName));\n                }\n            }\n            _formValues = cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.state.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n    return {\n        ...methods,\n        formControl: methods\n    };\n}\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._subjects.array.subscribe({\n            next: ({ values, name: fieldArrayName })=>{\n                if (fieldArrayName === _name.current || !fieldArrayName) {\n                    const fieldValues = get(values, _name.current);\n                    if (Array.isArray(fieldValues)) {\n                        setFields(fieldValues);\n                        ids.current = fieldValues.map(generateId);\n                    }\n                }\n            }\n        }).unsubscribe, [\n        control\n    ]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues)\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._setValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return ()=>{\n            const updateMounted = (name, value1)=>{\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value1;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...props.formControl ? props.formControl : createFormControl(props),\n            formState\n        };\n        if (props.formControl && props.defaultValues && !isFunction(props.defaultValues)) {\n            props.formControl.reset(props.defaultValues, props.resetOptions);\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(()=>{\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: ()=>updateFormState({\n                    ...control._formState\n                }),\n            reRenderRoot: true\n        });\n        updateFormState((data)=>({\n                ...data,\n                isReady: true\n            }));\n        control._formState.isReady = true;\n        return sub;\n    }, [\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n        if (props.errors && !isEmptyObject(props.errors)) {\n            control._setErrors(props.errors);\n        }\n    }, [\n        control,\n        props.errors,\n        props.mode,\n        props.reValidateMode\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.state.next({\n            values: control._getWatch()\n        });\n    }, [\n        control,\n        props.shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        control,\n        props.values\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaG9vay1mb3JtL2Rpc3QvaW5kZXguZXNtLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNJO0FBRW5DLElBQUlFLGtCQUFrQixDQUFDQyxVQUFZQSxRQUFRQyxJQUFJLEtBQUs7QUFFcEQsSUFBSUMsZUFBZSxDQUFDQyxTQUFVQSxrQkFBaUJDO0FBRS9DLElBQUlDLG9CQUFvQixDQUFDRixTQUFVQSxVQUFTO0FBRTVDLE1BQU1HLGVBQWUsQ0FBQ0gsU0FBVSxPQUFPQSxXQUFVO0FBQ2pELElBQUlJLFdBQVcsQ0FBQ0osU0FBVSxDQUFDRSxrQkFBa0JGLFdBQ3pDLENBQUNLLE1BQU1DLE9BQU8sQ0FBQ04sV0FDZkcsYUFBYUgsV0FDYixDQUFDRCxhQUFhQztBQUVsQixJQUFJTyxnQkFBZ0IsQ0FBQ0MsUUFBVUosU0FBU0ksVUFBVUEsTUFBTUMsTUFBTSxHQUN4RGIsZ0JBQWdCWSxNQUFNQyxNQUFNLElBQ3hCRCxNQUFNQyxNQUFNLENBQUNDLE9BQU8sR0FDcEJGLE1BQU1DLE1BQU0sQ0FBQ1QsS0FBSyxHQUN0QlE7QUFFTixJQUFJRyxvQkFBb0IsQ0FBQ0MsT0FBU0EsS0FBS0MsU0FBUyxDQUFDLEdBQUdELEtBQUtFLE1BQU0sQ0FBQyxtQkFBbUJGO0FBRW5GLElBQUlHLHFCQUFxQixDQUFDQyxPQUFPSixPQUFTSSxNQUFNQyxHQUFHLENBQUNOLGtCQUFrQkM7QUFFdEUsSUFBSU0sZ0JBQWdCLENBQUNDO0lBQ2pCLE1BQU1DLGdCQUFnQkQsV0FBV0UsV0FBVyxJQUFJRixXQUFXRSxXQUFXLENBQUNDLFNBQVM7SUFDaEYsT0FBUWxCLFNBQVNnQixrQkFBa0JBLGNBQWNHLGNBQWMsQ0FBQztBQUNwRTtBQUVBLElBQUlDLFFBQVEsTUFDc0IsSUFDOUIsQ0FBb0I7QUFFeEIsU0FBU0ksWUFBWUMsSUFBSTtJQUNyQixJQUFJQztJQUNKLE1BQU14QixVQUFVRCxNQUFNQyxPQUFPLENBQUN1QjtJQUM5QixNQUFNRSxxQkFBcUIsT0FBT0MsYUFBYSxjQUFjSCxnQkFBZ0JHLFdBQVc7SUFDeEYsSUFBSUgsZ0JBQWdCNUIsTUFBTTtRQUN0QjZCLE9BQU8sSUFBSTdCLEtBQUs0QjtJQUNwQixPQUNLLElBQUlBLGdCQUFnQkksS0FBSztRQUMxQkgsT0FBTyxJQUFJRyxJQUFJSjtJQUNuQixPQUNLLElBQUksQ0FBRUwsQ0FBQUEsU0FBVUssQ0FBQUEsZ0JBQWdCSyxRQUFRSCxrQkFBaUIsQ0FBQyxLQUMxRHpCLENBQUFBLFdBQVdGLFNBQVN5QixLQUFJLEdBQUk7UUFDN0JDLE9BQU94QixVQUFVLEVBQUUsR0FBRyxDQUFDO1FBQ3ZCLElBQUksQ0FBQ0EsV0FBVyxDQUFDWSxjQUFjVyxPQUFPO1lBQ2xDQyxPQUFPRDtRQUNYLE9BQ0s7WUFDRCxJQUFLLE1BQU1NLE9BQU9OLEtBQU07Z0JBQ3BCLElBQUlBLEtBQUtOLGNBQWMsQ0FBQ1ksTUFBTTtvQkFDMUJMLElBQUksQ0FBQ0ssSUFBSSxHQUFHUCxZQUFZQyxJQUFJLENBQUNNLElBQUk7Z0JBQ3JDO1lBQ0o7UUFDSjtJQUNKLE9BQ0s7UUFDRCxPQUFPTjtJQUNYO0lBQ0EsT0FBT0M7QUFDWDtBQUVBLElBQUlNLFVBQVUsQ0FBQ3BDLFNBQVVLLE1BQU1DLE9BQU8sQ0FBQ04sVUFBU0EsT0FBTXFDLE1BQU0sQ0FBQ0MsV0FBVyxFQUFFO0FBRTFFLElBQUlDLGNBQWMsQ0FBQ0MsTUFBUUEsUUFBUUM7QUFFbkMsSUFBSUMsTUFBTSxDQUFDQyxRQUFRQyxNQUFNQztJQUNyQixJQUFJLENBQUNELFFBQVEsQ0FBQ3hDLFNBQVN1QyxTQUFTO1FBQzVCLE9BQU9FO0lBQ1g7SUFDQSxNQUFNQyxTQUFTVixRQUFRUSxLQUFLRyxLQUFLLENBQUMsY0FBY0MsTUFBTSxDQUFDLENBQUNGLFFBQVFYLE1BQVFqQyxrQkFBa0I0QyxVQUFVQSxTQUFTQSxNQUFNLENBQUNYLElBQUksRUFBRVE7SUFDMUgsT0FBT0osWUFBWU8sV0FBV0EsV0FBV0gsU0FDbkNKLFlBQVlJLE1BQU0sQ0FBQ0MsS0FBSyxJQUNwQkMsZUFDQUYsTUFBTSxDQUFDQyxLQUFLLEdBQ2hCRTtBQUNWO0FBRUEsSUFBSUcsWUFBWSxDQUFDakQsU0FBVSxPQUFPQSxXQUFVO0FBRTVDLElBQUlrRCxRQUFRLENBQUNsRCxTQUFVLFFBQVFtRCxJQUFJLENBQUNuRDtBQUVwQyxJQUFJb0QsZUFBZSxDQUFDQyxRQUFVakIsUUFBUWlCLE1BQU1DLE9BQU8sQ0FBQyxhQUFhLElBQUlQLEtBQUssQ0FBQztBQUUzRSxJQUFJUSxNQUFNLENBQUNaLFFBQVFDLE1BQU01QztJQUNyQixJQUFJd0QsUUFBUSxDQUFDO0lBQ2IsTUFBTUMsV0FBV1AsTUFBTU4sUUFBUTtRQUFDQTtLQUFLLEdBQUdRLGFBQWFSO0lBQ3JELE1BQU1jLFNBQVNELFNBQVNDLE1BQU07SUFDOUIsTUFBTUMsWUFBWUQsU0FBUztJQUMzQixNQUFPLEVBQUVGLFFBQVFFLE9BQVE7UUFDckIsTUFBTXZCLE1BQU1zQixRQUFRLENBQUNELE1BQU07UUFDM0IsSUFBSUksV0FBVzVEO1FBQ2YsSUFBSXdELFVBQVVHLFdBQVc7WUFDckIsTUFBTUUsV0FBV2xCLE1BQU0sQ0FBQ1IsSUFBSTtZQUM1QnlCLFdBQ0l4RCxTQUFTeUQsYUFBYXhELE1BQU1DLE9BQU8sQ0FBQ3VELFlBQzlCQSxXQUNBLENBQUNDLE1BQU0sQ0FBQ0wsUUFBUSxDQUFDRCxRQUFRLEVBQUUsSUFDdkIsRUFBRSxHQUNGLENBQUM7UUFDbkI7UUFDQSxJQUFJckIsUUFBUSxlQUFlQSxRQUFRLGlCQUFpQkEsUUFBUSxhQUFhO1lBQ3JFO1FBQ0o7UUFDQVEsTUFBTSxDQUFDUixJQUFJLEdBQUd5QjtRQUNkakIsU0FBU0EsTUFBTSxDQUFDUixJQUFJO0lBQ3hCO0FBQ0o7QUFFQSxNQUFNNEIsU0FBUztJQUNYQyxNQUFNO0lBQ05DLFdBQVc7SUFDWEMsUUFBUTtBQUNaO0FBQ0EsTUFBTUMsa0JBQWtCO0lBQ3BCQyxRQUFRO0lBQ1JDLFVBQVU7SUFDVkMsVUFBVTtJQUNWQyxXQUFXO0lBQ1hDLEtBQUs7QUFDVDtBQUNBLE1BQU1DLHlCQUF5QjtJQUMzQkMsS0FBSztJQUNMQyxLQUFLO0lBQ0xDLFdBQVc7SUFDWEMsV0FBVztJQUNYQyxTQUFTO0lBQ1RDLFVBQVU7SUFDVkMsVUFBVTtBQUNkO0FBRUEsTUFBTUMsZ0NBQWtCdEYsZ0RBQTRCLENBQUM7QUFDckQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNkJDLEdBQ0QsTUFBTXdGLGlCQUFpQixJQUFNeEYsNkNBQXlCLENBQUNzRjtBQUN2RDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0E2QkMsR0FDRCxNQUFNSSxlQUFlLENBQUNDO0lBQ2xCLE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUcxRCxNQUFNLEdBQUd5RDtJQUM5QixxQkFBUTNGLGdEQUE0QixDQUFDc0YsZ0JBQWdCUSxRQUFRLEVBQUU7UUFBRXpGLE9BQU82QjtJQUFLLEdBQUcwRDtBQUNwRjtBQUVBLElBQUlHLG9CQUFvQixDQUFDQyxXQUFXQyxTQUFTQyxxQkFBcUJDLFNBQVMsSUFBSTtJQUMzRSxNQUFNaEQsU0FBUztRQUNYaUQsZUFBZUgsUUFBUUksY0FBYztJQUN6QztJQUNBLElBQUssTUFBTTdELE9BQU93RCxVQUFXO1FBQ3pCTSxPQUFPQyxjQUFjLENBQUNwRCxRQUFRWCxLQUFLO1lBQy9CTyxLQUFLO2dCQUNELE1BQU15RCxPQUFPaEU7Z0JBQ2IsSUFBSXlELFFBQVFRLGVBQWUsQ0FBQ0QsS0FBSyxLQUFLaEMsZ0JBQWdCSyxHQUFHLEVBQUU7b0JBQ3ZEb0IsUUFBUVEsZUFBZSxDQUFDRCxLQUFLLEdBQUcsQ0FBQ0wsVUFBVTNCLGdCQUFnQkssR0FBRztnQkFDbEU7Z0JBQ0FxQix1QkFBd0JBLENBQUFBLG1CQUFtQixDQUFDTSxLQUFLLEdBQUcsSUFBRztnQkFDdkQsT0FBT1IsU0FBUyxDQUFDUSxLQUFLO1lBQzFCO1FBQ0o7SUFDSjtJQUNBLE9BQU9yRDtBQUNYO0FBRUEsTUFBTXVELDRCQUE0QixNQUFrQixHQUFjM0csQ0FBcUIsR0FBR0EsNENBQWU7QUFFekc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNkJDLEdBQ0QsU0FBUzhHLGFBQWFsQixLQUFLO0lBQ3ZCLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxFQUFFUyxVQUFVYSxRQUFRYixPQUFPLEVBQUVjLFFBQVEsRUFBRTlGLElBQUksRUFBRStGLEtBQUssRUFBRSxHQUFHckIsU0FBUyxDQUFDO0lBQ3ZFLE1BQU0sQ0FBQ0ssV0FBV2lCLGdCQUFnQixHQUFHakgsMkNBQXVCLENBQUNpRyxRQUFRa0IsVUFBVTtJQUMvRSxNQUFNQyx1QkFBdUJwSCx5Q0FBcUIsQ0FBQztRQUMvQ3NILFNBQVM7UUFDVEMsV0FBVztRQUNYQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsa0JBQWtCO1FBQ2xCQyxjQUFjO1FBQ2RDLFNBQVM7UUFDVEMsUUFBUTtJQUNaO0lBQ0FuQiwwQkFBMEIsSUFBTVQsUUFBUTZCLFVBQVUsQ0FBQztZQUMvQzdHLE1BQU1BO1lBQ04rRSxXQUFXb0IscUJBQXFCVyxPQUFPO1lBQ3ZDZjtZQUNBZ0IsVUFBVSxDQUFDaEM7Z0JBQ1AsQ0FBQ2UsWUFDR0UsZ0JBQWdCO29CQUNaLEdBQUdoQixRQUFRa0IsVUFBVTtvQkFDckIsR0FBR25CLFNBQVM7Z0JBQ2hCO1lBQ1I7UUFDSixJQUFJO1FBQUMvRTtRQUFNOEY7UUFBVUM7S0FBTTtJQUMzQmhILDRDQUF3QixDQUFDO1FBQ3JCb0gscUJBQXFCVyxPQUFPLENBQUNILE9BQU8sSUFBSTNCLFFBQVFnQyxTQUFTLENBQUM7SUFDOUQsR0FBRztRQUFDaEM7S0FBUTtJQUNaLE9BQU9qRywwQ0FBc0IsQ0FBQyxJQUFNK0Ysa0JBQWtCQyxXQUFXQyxTQUFTbUIscUJBQXFCVyxPQUFPLEVBQUUsUUFBUTtRQUFDL0I7UUFBV0M7S0FBUTtBQUN4STtBQUVBLElBQUlrQyxXQUFXLENBQUM5SCxTQUFVLE9BQU9BLFdBQVU7QUFFM0MsSUFBSStILHNCQUFzQixDQUFDL0csT0FBT2dILFFBQVFDLFlBQVlDLFVBQVVyRjtJQUM1RCxJQUFJaUYsU0FBUzlHLFFBQVE7UUFDakJrSCxZQUFZRixPQUFPRyxLQUFLLENBQUNDLEdBQUcsQ0FBQ3BIO1FBQzdCLE9BQU8wQixJQUFJdUYsWUFBWWpILE9BQU82QjtJQUNsQztJQUNBLElBQUl4QyxNQUFNQyxPQUFPLENBQUNVLFFBQVE7UUFDdEIsT0FBT0EsTUFBTXFILEdBQUcsQ0FBQyxDQUFDQyxZQUFlSixDQUFBQSxZQUFZRixPQUFPRyxLQUFLLENBQUNDLEdBQUcsQ0FBQ0UsWUFBWTVGLElBQUl1RixZQUFZSyxVQUFTO0lBQ3ZHO0lBQ0FKLFlBQWFGLENBQUFBLE9BQU9PLFFBQVEsR0FBRyxJQUFHO0lBQ2xDLE9BQU9OO0FBQ1g7QUFFQTs7Ozs7Ozs7Ozs7Ozs7O0NBZUMsR0FDRCxTQUFTTyxTQUFTbEQsS0FBSztJQUNuQixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRVMsVUFBVWEsUUFBUWIsT0FBTyxFQUFFaEYsSUFBSSxFQUFFaUMsWUFBWSxFQUFFNkQsUUFBUSxFQUFFQyxLQUFLLEVBQUcsR0FBR3JCLFNBQVMsQ0FBQztJQUN0RixNQUFNbUQsZ0JBQWdCOUkseUNBQXFCLENBQUNrRDtJQUM1QyxNQUFNLENBQUM3QyxRQUFPMEksWUFBWSxHQUFHL0ksMkNBQXVCLENBQUNpRyxRQUFRK0MsU0FBUyxDQUFDL0gsTUFBTTZILGNBQWNmLE9BQU87SUFDbEdyQiwwQkFBMEIsSUFBTVQsUUFBUTZCLFVBQVUsQ0FBQztZQUMvQzdHLE1BQU1BO1lBQ04rRSxXQUFXO2dCQUNQaUQsUUFBUTtZQUNaO1lBQ0FqQztZQUNBZ0IsVUFBVSxDQUFDaEMsWUFBYyxDQUFDZSxZQUN0QmdDLFlBQVlYLG9CQUFvQm5ILE1BQU1nRixRQUFRb0MsTUFBTSxFQUFFckMsVUFBVWlELE1BQU0sSUFBSWhELFFBQVFpRCxXQUFXLEVBQUUsT0FBT0osY0FBY2YsT0FBTztRQUNuSSxJQUFJO1FBQUM5RztRQUFNZ0Y7UUFBU2M7UUFBVUM7S0FBTTtJQUNwQ2hILDRDQUF3QixDQUFDLElBQU1pRyxRQUFRa0QsZ0JBQWdCO0lBQ3ZELE9BQU85STtBQUNYO0FBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBdUJDLEdBQ0QsU0FBUytJLGNBQWN6RCxLQUFLO0lBQ3hCLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxFQUFFdkUsSUFBSSxFQUFFOEYsUUFBUSxFQUFFZCxVQUFVYSxRQUFRYixPQUFPLEVBQUVvRCxnQkFBZ0IsRUFBRSxHQUFHMUQ7SUFDeEUsTUFBTTJELGVBQWVsSSxtQkFBbUI2RSxRQUFRb0MsTUFBTSxDQUFDa0IsS0FBSyxFQUFFdEk7SUFDOUQsTUFBTVosU0FBUXdJLFNBQVM7UUFDbkI1QztRQUNBaEY7UUFDQWlDLGNBQWNILElBQUlrRCxRQUFRaUQsV0FBVyxFQUFFakksTUFBTThCLElBQUlrRCxRQUFRSSxjQUFjLEVBQUVwRixNQUFNMEUsTUFBTXpDLFlBQVk7UUFDakc4RCxPQUFPO0lBQ1g7SUFDQSxNQUFNaEIsWUFBWWEsYUFBYTtRQUMzQlo7UUFDQWhGO1FBQ0ErRixPQUFPO0lBQ1g7SUFDQSxNQUFNd0MsU0FBU3hKLHlDQUFxQixDQUFDMkY7SUFDckMsTUFBTThELGlCQUFpQnpKLHlDQUFxQixDQUFDaUcsUUFBUXlELFFBQVEsQ0FBQ3pJLE1BQU07UUFDaEUsR0FBRzBFLE1BQU1nRSxLQUFLO1FBQ2R0SixPQUFBQTtRQUNBLEdBQUlpRCxVQUFVcUMsTUFBTW9CLFFBQVEsSUFBSTtZQUFFQSxVQUFVcEIsTUFBTW9CLFFBQVE7UUFBQyxJQUFJLENBQUMsQ0FBQztJQUNyRTtJQUNBLE1BQU02QyxhQUFhNUosMENBQXNCLENBQUMsSUFBTXNHLE9BQU91RCxnQkFBZ0IsQ0FBQyxDQUFDLEdBQUc7WUFDeEVDLFNBQVM7Z0JBQ0xDLFlBQVk7Z0JBQ1poSCxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJaUQsVUFBVTZCLE1BQU0sRUFBRTVHO1lBQ3ZDO1lBQ0FxRyxTQUFTO2dCQUNMeUMsWUFBWTtnQkFDWmhILEtBQUssSUFBTSxDQUFDLENBQUNBLElBQUlpRCxVQUFVd0IsV0FBVyxFQUFFdkc7WUFDNUM7WUFDQStJLFdBQVc7Z0JBQ1BELFlBQVk7Z0JBQ1poSCxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJaUQsVUFBVXlCLGFBQWEsRUFBRXhHO1lBQzlDO1lBQ0EwRyxjQUFjO2dCQUNWb0MsWUFBWTtnQkFDWmhILEtBQUssSUFBTSxDQUFDLENBQUNBLElBQUlpRCxVQUFVMEIsZ0JBQWdCLEVBQUV6RztZQUNqRDtZQUNBZ0osT0FBTztnQkFDSEYsWUFBWTtnQkFDWmhILEtBQUssSUFBTUEsSUFBSWlELFVBQVU2QixNQUFNLEVBQUU1RztZQUNyQztRQUNKLElBQUk7UUFBQytFO1FBQVcvRTtLQUFLO0lBQ3JCLE1BQU15RCxXQUFXMUUsOENBQTBCLENBQUMsQ0FBQ2EsUUFBVTRJLGVBQWUxQixPQUFPLENBQUNyRCxRQUFRLENBQUM7WUFDbkY1RCxRQUFRO2dCQUNKVCxPQUFPTyxjQUFjQztnQkFDckJJLE1BQU1BO1lBQ1Y7WUFDQWQsTUFBTWlFLE9BQU9HLE1BQU07UUFDdkIsSUFBSTtRQUFDdEQ7S0FBSztJQUNWLE1BQU13RCxTQUFTekUsOENBQTBCLENBQUMsSUFBTXlKLGVBQWUxQixPQUFPLENBQUN0RCxNQUFNLENBQUM7WUFDMUUzRCxRQUFRO2dCQUNKVCxPQUFPMEMsSUFBSWtELFFBQVFpRCxXQUFXLEVBQUVqSTtnQkFDaENBLE1BQU1BO1lBQ1Y7WUFDQWQsTUFBTWlFLE9BQU9DLElBQUk7UUFDckIsSUFBSTtRQUFDcEQ7UUFBTWdGLFFBQVFpRCxXQUFXO0tBQUM7SUFDL0IsTUFBTWlCLE1BQU1uSyw4Q0FBMEIsQ0FBQyxDQUFDb0s7UUFDcEMsTUFBTUMsUUFBUXRILElBQUlrRCxRQUFRcUUsT0FBTyxFQUFFcko7UUFDbkMsSUFBSW9KLFNBQVNELEtBQUs7WUFDZEMsTUFBTUUsRUFBRSxDQUFDSixHQUFHLEdBQUc7Z0JBQ1hLLE9BQU8sSUFBTUosSUFBSUksS0FBSztnQkFDdEJDLFFBQVEsSUFBTUwsSUFBSUssTUFBTTtnQkFDeEJDLG1CQUFtQixDQUFDQyxVQUFZUCxJQUFJTSxpQkFBaUIsQ0FBQ0M7Z0JBQ3REQyxnQkFBZ0IsSUFBTVIsSUFBSVEsY0FBYztZQUM1QztRQUNKO0lBQ0osR0FBRztRQUFDM0UsUUFBUXFFLE9BQU87UUFBRXJKO0tBQUs7SUFDMUIsTUFBTW9KLFFBQVFySywwQ0FBc0IsQ0FBQyxJQUFPO1lBQ3hDaUI7WUFDQVosT0FBQUE7WUFDQSxHQUFJaUQsVUFBVXlELGFBQWFmLFVBQVVlLFFBQVEsR0FDdkM7Z0JBQUVBLFVBQVVmLFVBQVVlLFFBQVEsSUFBSUE7WUFBUyxJQUMzQyxDQUFDLENBQUM7WUFDUnJDO1lBQ0FEO1lBQ0EwRjtRQUNKLElBQUk7UUFBQ2xKO1FBQU04RjtRQUFVZixVQUFVZSxRQUFRO1FBQUVyQztRQUFVRDtRQUFRMEY7UUFBSzlKO0tBQU07SUFDdEVMLDRDQUF3QixDQUFDO1FBQ3JCLE1BQU02Syx5QkFBeUI1RSxRQUFRNkUsUUFBUSxDQUFDekIsZ0JBQWdCLElBQUlBO1FBQ3BFcEQsUUFBUXlELFFBQVEsQ0FBQ3pJLE1BQU07WUFDbkIsR0FBR3VJLE9BQU96QixPQUFPLENBQUM0QixLQUFLO1lBQ3ZCLEdBQUlyRyxVQUFVa0csT0FBT3pCLE9BQU8sQ0FBQ2hCLFFBQVEsSUFDL0I7Z0JBQUVBLFVBQVV5QyxPQUFPekIsT0FBTyxDQUFDaEIsUUFBUTtZQUFDLElBQ3BDLENBQUMsQ0FBQztRQUNaO1FBQ0EsTUFBTWdFLGdCQUFnQixDQUFDOUosTUFBTVo7WUFDekIsTUFBTWdLLFFBQVF0SCxJQUFJa0QsUUFBUXFFLE9BQU8sRUFBRXJKO1lBQ25DLElBQUlvSixTQUFTQSxNQUFNRSxFQUFFLEVBQUU7Z0JBQ25CRixNQUFNRSxFQUFFLENBQUNTLEtBQUssR0FBRzNLO1lBQ3JCO1FBQ0o7UUFDQTBLLGNBQWM5SixNQUFNO1FBQ3BCLElBQUk0Six3QkFBd0I7WUFDeEIsTUFBTXhLLFNBQVE0QixZQUFZYyxJQUFJa0QsUUFBUTZFLFFBQVEsQ0FBQzFFLGFBQWEsRUFBRW5GO1lBQzlEMkMsSUFBSXFDLFFBQVFJLGNBQWMsRUFBRXBGLE1BQU1aO1lBQ2xDLElBQUl1QyxZQUFZRyxJQUFJa0QsUUFBUWlELFdBQVcsRUFBRWpJLFFBQVE7Z0JBQzdDMkMsSUFBSXFDLFFBQVFpRCxXQUFXLEVBQUVqSSxNQUFNWjtZQUNuQztRQUNKO1FBQ0EsQ0FBQ2lKLGdCQUFnQnJELFFBQVF5RCxRQUFRLENBQUN6STtRQUNsQyxPQUFPO1lBQ0ZxSSxDQUFBQSxlQUNLdUIsMEJBQTBCLENBQUM1RSxRQUFRZ0YsTUFBTSxDQUFDQyxNQUFNLEdBQ2hETCxzQkFBcUIsSUFDckI1RSxRQUFRa0YsVUFBVSxDQUFDbEssUUFDbkI4SixjQUFjOUosTUFBTTtRQUM5QjtJQUNKLEdBQUc7UUFBQ0E7UUFBTWdGO1FBQVNxRDtRQUFjRDtLQUFpQjtJQUNsRHJKLDRDQUF3QixDQUFDO1FBQ3JCaUcsUUFBUW1GLGlCQUFpQixDQUFDO1lBQ3RCckU7WUFDQTlGO1FBQ0o7SUFDSixHQUFHO1FBQUM4RjtRQUFVOUY7UUFBTWdGO0tBQVE7SUFDNUIsT0FBT2pHLDBDQUFzQixDQUFDLElBQU87WUFDakNxSztZQUNBckU7WUFDQTREO1FBQ0osSUFBSTtRQUFDUztRQUFPckU7UUFBVzREO0tBQVc7QUFDdEM7QUFFQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0F5Q0MsR0FDRCxNQUFNeUIsYUFBYSxDQUFDMUYsUUFBVUEsTUFBTTJGLE1BQU0sQ0FBQ2xDLGNBQWN6RDtBQUV6RCxNQUFNNEYsVUFBVSxDQUFDQztJQUNiLE1BQU1DLFNBQVMsQ0FBQztJQUNoQixLQUFLLE1BQU1qSixPQUFPOEQsT0FBT29GLElBQUksQ0FBQ0YsS0FBTTtRQUNoQyxJQUFJaEwsYUFBYWdMLEdBQUcsQ0FBQ2hKLElBQUksS0FBS2dKLEdBQUcsQ0FBQ2hKLElBQUksS0FBSyxNQUFNO1lBQzdDLE1BQU1tSixTQUFTSixRQUFRQyxHQUFHLENBQUNoSixJQUFJO1lBQy9CLEtBQUssTUFBTW9KLGFBQWF0RixPQUFPb0YsSUFBSSxDQUFDQyxRQUFTO2dCQUN6Q0YsTUFBTSxDQUFDLENBQUMsRUFBRWpKLElBQUksQ0FBQyxFQUFFb0osVUFBVSxDQUFDLENBQUMsR0FBR0QsTUFBTSxDQUFDQyxVQUFVO1lBQ3JEO1FBQ0osT0FDSztZQUNESCxNQUFNLENBQUNqSixJQUFJLEdBQUdnSixHQUFHLENBQUNoSixJQUFJO1FBQzFCO0lBQ0o7SUFDQSxPQUFPaUo7QUFDWDtBQUVBLE1BQU1JLGVBQWU7QUFDckI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXFCQyxHQUNELFNBQVNDLEtBQUtuRyxLQUFLO0lBQ2YsTUFBTW1CLFVBQVV0QjtJQUNoQixNQUFNLENBQUN1RyxTQUFTQyxXQUFXLEdBQUdoTSwyQ0FBdUIsQ0FBQztJQUN0RCxNQUFNLEVBQUVpRyxVQUFVYSxRQUFRYixPQUFPLEVBQUV0QixRQUFRLEVBQUVpQixRQUFRLEVBQUVzRixNQUFNLEVBQUVlLFNBQVNKLFlBQVksRUFBRUssT0FBTyxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRWQsTUFBTSxFQUFFZSxTQUFTLEVBQUVDLGNBQWMsRUFBRSxHQUFHQyxNQUFNLEdBQUc1RztJQUNoSyxNQUFNNkcsU0FBUyxPQUFPM0w7UUFDbEIsSUFBSTRMLFdBQVc7UUFDZixJQUFJdE0sT0FBTztRQUNYLE1BQU04RixRQUFReUcsWUFBWSxDQUFDLE9BQU94SztZQUM5QixNQUFNeUssV0FBVyxJQUFJQztZQUNyQixJQUFJQyxlQUFlO1lBQ25CLElBQUk7Z0JBQ0FBLGVBQWVDLEtBQUtDLFNBQVMsQ0FBQzdLO1lBQ2xDLEVBQ0EsT0FBTzhLLElBQUksQ0FBRTtZQUNiLE1BQU1DLG9CQUFvQjFCLFFBQVF0RixRQUFRaUQsV0FBVztZQUNyRCxJQUFLLE1BQU0xRyxPQUFPeUssa0JBQW1CO2dCQUNqQ04sU0FBU08sTUFBTSxDQUFDMUssS0FBS3lLLGlCQUFpQixDQUFDekssSUFBSTtZQUMvQztZQUNBLElBQUltQyxVQUFVO2dCQUNWLE1BQU1BLFNBQVM7b0JBQ1h6QztvQkFDQXJCO29CQUNBb0w7b0JBQ0FVO29CQUNBRTtnQkFDSjtZQUNKO1lBQ0EsSUFBSTNCLFFBQVE7Z0JBQ1IsSUFBSTtvQkFDQSxNQUFNaUMsZ0NBQWdDO3dCQUNsQ2pCLFdBQVdBLE9BQU8sQ0FBQyxlQUFlO3dCQUNsQ0M7cUJBQ0gsQ0FBQ2lCLElBQUksQ0FBQyxDQUFDL00sU0FBVUEsVUFBU0EsT0FBTWdOLFFBQVEsQ0FBQztvQkFDMUMsTUFBTUMsV0FBVyxNQUFNQyxNQUFNQyxPQUFPdEMsU0FBUzt3QkFDekNlO3dCQUNBQyxTQUFTOzRCQUNMLEdBQUdBLE9BQU87NEJBQ1YsR0FBSUMsVUFBVTtnQ0FBRSxnQkFBZ0JBOzRCQUFRLElBQUksQ0FBQyxDQUFDO3dCQUNsRDt3QkFDQXNCLE1BQU1OLGdDQUFnQ04sZUFBZUY7b0JBQ3pEO29CQUNBLElBQUlXLFlBQ0NoQixDQUFBQSxpQkFDSyxDQUFDQSxlQUFlZ0IsU0FBU0ksTUFBTSxJQUMvQkosU0FBU0ksTUFBTSxHQUFHLE9BQU9KLFNBQVNJLE1BQU0sSUFBSSxHQUFFLEdBQUk7d0JBQ3hEakIsV0FBVzt3QkFDWEwsV0FBV0EsUUFBUTs0QkFBRWtCO3dCQUFTO3dCQUM5Qm5OLE9BQU9xTixPQUFPRixTQUFTSSxNQUFNO29CQUNqQyxPQUNLO3dCQUNEckIsYUFBYUEsVUFBVTs0QkFBRWlCO3dCQUFTO29CQUN0QztnQkFDSixFQUNBLE9BQU9yRCxPQUFPO29CQUNWd0MsV0FBVztvQkFDWEwsV0FBV0EsUUFBUTt3QkFBRW5DO29CQUFNO2dCQUMvQjtZQUNKO1FBQ0osR0FBR3BKO1FBQ0gsSUFBSTRMLFlBQVk5RyxNQUFNTSxPQUFPLEVBQUU7WUFDM0JOLE1BQU1NLE9BQU8sQ0FBQzBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQy9CQyxvQkFBb0I7WUFDeEI7WUFDQW5JLE1BQU1NLE9BQU8sQ0FBQzhILFFBQVEsQ0FBQyxlQUFlO2dCQUNsQzVOO1lBQ0o7UUFDSjtJQUNKO0lBQ0FILDRDQUF3QixDQUFDO1FBQ3JCZ00sV0FBVztJQUNmLEdBQUcsRUFBRTtJQUNMLE9BQU9WLHVCQUFVdEwsZ0RBQTRCLENBQUNBLDJDQUF1QixFQUFFLE1BQU1zTCxPQUFPO1FBQ2hGa0I7SUFDSixvQkFBUXhNLGdEQUE0QixDQUFDLFFBQVE7UUFBRWlPLFlBQVlsQztRQUFTYixRQUFRQTtRQUFRZSxRQUFRQTtRQUFRRSxTQUFTQTtRQUFTeEgsVUFBVTZIO1FBQVEsR0FBR0QsSUFBSTtJQUFDLEdBQUczRztBQUN2SjtBQUVBLElBQUlzSSxlQUFlLENBQUNqTixNQUFNa04sMEJBQTBCdEcsUUFBUTFILE1BQU13SyxVQUFZd0QsMkJBQ3hFO1FBQ0UsR0FBR3RHLE1BQU0sQ0FBQzVHLEtBQUs7UUFDZm1OLE9BQU87WUFDSCxHQUFJdkcsTUFBTSxDQUFDNUcsS0FBSyxJQUFJNEcsTUFBTSxDQUFDNUcsS0FBSyxDQUFDbU4sS0FBSyxHQUFHdkcsTUFBTSxDQUFDNUcsS0FBSyxDQUFDbU4sS0FBSyxHQUFHLENBQUMsQ0FBQztZQUNoRSxDQUFDak8sS0FBSyxFQUFFd0ssV0FBVztRQUN2QjtJQUNKLElBQ0UsQ0FBQztBQUVQLElBQUkwRCx3QkFBd0IsQ0FBQ2hPLFNBQVdLLE1BQU1DLE9BQU8sQ0FBQ04sVUFBU0EsU0FBUTtRQUFDQTtLQUFNO0FBRTlFLElBQUlpTyxnQkFBZ0I7SUFDaEIsSUFBSUMsYUFBYSxFQUFFO0lBQ25CLE1BQU1WLE9BQU8sQ0FBQ3hOO1FBQ1YsS0FBSyxNQUFNbU8sWUFBWUQsV0FBWTtZQUMvQkMsU0FBU1gsSUFBSSxJQUFJVyxTQUFTWCxJQUFJLENBQUN4TjtRQUNuQztJQUNKO0lBQ0EsTUFBTW9PLFlBQVksQ0FBQ0Q7UUFDZkQsV0FBV0csSUFBSSxDQUFDRjtRQUNoQixPQUFPO1lBQ0hHLGFBQWE7Z0JBQ1RKLGFBQWFBLFdBQVc3TCxNQUFNLENBQUMsQ0FBQ2tNLElBQU1BLE1BQU1KO1lBQ2hEO1FBQ0o7SUFDSjtJQUNBLE1BQU1HLGNBQWM7UUFDaEJKLGFBQWEsRUFBRTtJQUNuQjtJQUNBLE9BQU87UUFDSCxJQUFJTSxhQUFZO1lBQ1osT0FBT047UUFDWDtRQUNBVjtRQUNBWTtRQUNBRTtJQUNKO0FBQ0o7QUFFQSxJQUFJRyxjQUFjLENBQUN6TyxTQUFVRSxrQkFBa0JGLFdBQVUsQ0FBQ0csYUFBYUg7QUFFdkUsU0FBUzBPLFVBQVVDLE9BQU8sRUFBRUMsT0FBTztJQUMvQixJQUFJSCxZQUFZRSxZQUFZRixZQUFZRyxVQUFVO1FBQzlDLE9BQU9ELFlBQVlDO0lBQ3ZCO0lBQ0EsSUFBSTdPLGFBQWE0TyxZQUFZNU8sYUFBYTZPLFVBQVU7UUFDaEQsT0FBT0QsUUFBUUUsT0FBTyxPQUFPRCxRQUFRQyxPQUFPO0lBQ2hEO0lBQ0EsTUFBTUMsUUFBUTdJLE9BQU9vRixJQUFJLENBQUNzRDtJQUMxQixNQUFNSSxRQUFROUksT0FBT29GLElBQUksQ0FBQ3VEO0lBQzFCLElBQUlFLE1BQU1wTCxNQUFNLEtBQUtxTCxNQUFNckwsTUFBTSxFQUFFO1FBQy9CLE9BQU87SUFDWDtJQUNBLEtBQUssTUFBTXZCLE9BQU8yTSxNQUFPO1FBQ3JCLE1BQU1FLE9BQU9MLE9BQU8sQ0FBQ3hNLElBQUk7UUFDekIsSUFBSSxDQUFDNE0sTUFBTS9CLFFBQVEsQ0FBQzdLLE1BQU07WUFDdEIsT0FBTztRQUNYO1FBQ0EsSUFBSUEsUUFBUSxPQUFPO1lBQ2YsTUFBTThNLE9BQU9MLE9BQU8sQ0FBQ3pNLElBQUk7WUFDekIsSUFBSSxhQUFjNk0sU0FBU2pQLGFBQWFrUCxTQUNuQzdPLFNBQVM0TyxTQUFTNU8sU0FBUzZPLFNBQzNCNU8sTUFBTUMsT0FBTyxDQUFDME8sU0FBUzNPLE1BQU1DLE9BQU8sQ0FBQzJPLFFBQ3BDLENBQUNQLFVBQVVNLE1BQU1DLFFBQ2pCRCxTQUFTQyxNQUFNO2dCQUNqQixPQUFPO1lBQ1g7UUFDSjtJQUNKO0lBQ0EsT0FBTztBQUNYO0FBRUEsSUFBSUMsZ0JBQWdCLENBQUNsUCxTQUFVSSxTQUFTSixXQUFVLENBQUNpRyxPQUFPb0YsSUFBSSxDQUFDckwsUUFBTzBELE1BQU07QUFFNUUsSUFBSXlMLGNBQWMsQ0FBQ3RQLFVBQVlBLFFBQVFDLElBQUksS0FBSztBQUVoRCxJQUFJc1AsYUFBYSxDQUFDcFAsU0FBVSxPQUFPQSxXQUFVO0FBRTdDLElBQUlxUCxnQkFBZ0IsQ0FBQ3JQO0lBQ2pCLElBQUksQ0FBQ3dCLE9BQU87UUFDUixPQUFPO0lBQ1g7SUFDQSxNQUFNOE4sUUFBUXRQLFNBQVFBLE9BQU11UCxhQUFhLEdBQUc7SUFDNUMsT0FBUXZQLGtCQUNIc1AsQ0FBQUEsU0FBU0EsTUFBTUUsV0FBVyxHQUFHRixNQUFNRSxXQUFXLENBQUM5TixXQUFXLEdBQUdBLFdBQVU7QUFDaEY7QUFFQSxJQUFJK04sbUJBQW1CLENBQUM1UCxVQUFZQSxRQUFRQyxJQUFJLEtBQUssQ0FBQyxlQUFlLENBQUM7QUFFdEUsSUFBSTRQLGVBQWUsQ0FBQzdQLFVBQVlBLFFBQVFDLElBQUksS0FBSztBQUVqRCxJQUFJNlAsb0JBQW9CLENBQUM3RixNQUFRNEYsYUFBYTVGLFFBQVFsSyxnQkFBZ0JrSztBQUV0RSxJQUFJOEYsT0FBTyxDQUFDOUYsTUFBUXVGLGNBQWN2RixRQUFRQSxJQUFJK0YsV0FBVztBQUV6RCxTQUFTQyxRQUFRbk4sTUFBTSxFQUFFb04sVUFBVTtJQUMvQixNQUFNck0sU0FBU3FNLFdBQVdDLEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBR3RNLE1BQU07SUFDN0MsSUFBSUYsUUFBUTtJQUNaLE1BQU9BLFFBQVFFLE9BQVE7UUFDbkJmLFNBQVNKLFlBQVlJLFVBQVVhLFVBQVViLE1BQU0sQ0FBQ29OLFVBQVUsQ0FBQ3ZNLFFBQVEsQ0FBQztJQUN4RTtJQUNBLE9BQU9iO0FBQ1g7QUFDQSxTQUFTc04sYUFBYTlFLEdBQUc7SUFDckIsSUFBSyxNQUFNaEosT0FBT2dKLElBQUs7UUFDbkIsSUFBSUEsSUFBSTVKLGNBQWMsQ0FBQ1ksUUFBUSxDQUFDSSxZQUFZNEksR0FBRyxDQUFDaEosSUFBSSxHQUFHO1lBQ25ELE9BQU87UUFDWDtJQUNKO0lBQ0EsT0FBTztBQUNYO0FBQ0EsU0FBUytOLE1BQU12TixNQUFNLEVBQUVDLElBQUk7SUFDdkIsTUFBTXVOLFFBQVE5UCxNQUFNQyxPQUFPLENBQUNzQyxRQUN0QkEsT0FDQU0sTUFBTU4sUUFDRjtRQUFDQTtLQUFLLEdBQ05RLGFBQWFSO0lBQ3ZCLE1BQU13TixjQUFjRCxNQUFNek0sTUFBTSxLQUFLLElBQUlmLFNBQVNtTixRQUFRbk4sUUFBUXdOO0lBQ2xFLE1BQU0zTSxRQUFRMk0sTUFBTXpNLE1BQU0sR0FBRztJQUM3QixNQUFNdkIsTUFBTWdPLEtBQUssQ0FBQzNNLE1BQU07SUFDeEIsSUFBSTRNLGFBQWE7UUFDYixPQUFPQSxXQUFXLENBQUNqTyxJQUFJO0lBQzNCO0lBQ0EsSUFBSXFCLFVBQVUsS0FDVCxVQUFVNE0sZ0JBQWdCbEIsY0FBY2tCLGdCQUNwQy9QLE1BQU1DLE9BQU8sQ0FBQzhQLGdCQUFnQkgsYUFBYUcsWUFBWSxHQUFJO1FBQ2hFRixNQUFNdk4sUUFBUXdOLE1BQU1ILEtBQUssQ0FBQyxHQUFHLENBQUM7SUFDbEM7SUFDQSxPQUFPck47QUFDWDtBQUVBLElBQUkwTixvQkFBb0IsQ0FBQ3hPO0lBQ3JCLElBQUssTUFBTU0sT0FBT04sS0FBTTtRQUNwQixJQUFJdU4sV0FBV3ZOLElBQUksQ0FBQ00sSUFBSSxHQUFHO1lBQ3ZCLE9BQU87UUFDWDtJQUNKO0lBQ0EsT0FBTztBQUNYO0FBRUEsU0FBU21PLGdCQUFnQnpPLElBQUksRUFBRTBPLFNBQVMsQ0FBQyxDQUFDO0lBQ3RDLE1BQU1DLG9CQUFvQm5RLE1BQU1DLE9BQU8sQ0FBQ3VCO0lBQ3hDLElBQUl6QixTQUFTeUIsU0FBUzJPLG1CQUFtQjtRQUNyQyxJQUFLLE1BQU1yTyxPQUFPTixLQUFNO1lBQ3BCLElBQUl4QixNQUFNQyxPQUFPLENBQUN1QixJQUFJLENBQUNNLElBQUksS0FDdEIvQixTQUFTeUIsSUFBSSxDQUFDTSxJQUFJLEtBQUssQ0FBQ2tPLGtCQUFrQnhPLElBQUksQ0FBQ00sSUFBSSxHQUFJO2dCQUN4RG9PLE1BQU0sQ0FBQ3BPLElBQUksR0FBRzlCLE1BQU1DLE9BQU8sQ0FBQ3VCLElBQUksQ0FBQ00sSUFBSSxJQUFJLEVBQUUsR0FBRyxDQUFDO2dCQUMvQ21PLGdCQUFnQnpPLElBQUksQ0FBQ00sSUFBSSxFQUFFb08sTUFBTSxDQUFDcE8sSUFBSTtZQUMxQyxPQUNLLElBQUksQ0FBQ2pDLGtCQUFrQjJCLElBQUksQ0FBQ00sSUFBSSxHQUFHO2dCQUNwQ29PLE1BQU0sQ0FBQ3BPLElBQUksR0FBRztZQUNsQjtRQUNKO0lBQ0o7SUFDQSxPQUFPb087QUFDWDtBQUNBLFNBQVNFLGdDQUFnQzVPLElBQUksRUFBRW9HLFVBQVUsRUFBRXlJLHFCQUFxQjtJQUM1RSxNQUFNRixvQkFBb0JuUSxNQUFNQyxPQUFPLENBQUN1QjtJQUN4QyxJQUFJekIsU0FBU3lCLFNBQVMyTyxtQkFBbUI7UUFDckMsSUFBSyxNQUFNck8sT0FBT04sS0FBTTtZQUNwQixJQUFJeEIsTUFBTUMsT0FBTyxDQUFDdUIsSUFBSSxDQUFDTSxJQUFJLEtBQ3RCL0IsU0FBU3lCLElBQUksQ0FBQ00sSUFBSSxLQUFLLENBQUNrTyxrQkFBa0J4TyxJQUFJLENBQUNNLElBQUksR0FBSTtnQkFDeEQsSUFBSUksWUFBWTBGLGVBQ1p3RyxZQUFZaUMscUJBQXFCLENBQUN2TyxJQUFJLEdBQUc7b0JBQ3pDdU8scUJBQXFCLENBQUN2TyxJQUFJLEdBQUc5QixNQUFNQyxPQUFPLENBQUN1QixJQUFJLENBQUNNLElBQUksSUFDOUNtTyxnQkFBZ0J6TyxJQUFJLENBQUNNLElBQUksRUFBRSxFQUFFLElBQzdCO3dCQUFFLEdBQUdtTyxnQkFBZ0J6TyxJQUFJLENBQUNNLElBQUksQ0FBQztvQkFBQztnQkFDMUMsT0FDSztvQkFDRHNPLGdDQUFnQzVPLElBQUksQ0FBQ00sSUFBSSxFQUFFakMsa0JBQWtCK0gsY0FBYyxDQUFDLElBQUlBLFVBQVUsQ0FBQzlGLElBQUksRUFBRXVPLHFCQUFxQixDQUFDdk8sSUFBSTtnQkFDL0g7WUFDSixPQUNLO2dCQUNEdU8scUJBQXFCLENBQUN2TyxJQUFJLEdBQUcsQ0FBQ3VNLFVBQVU3TSxJQUFJLENBQUNNLElBQUksRUFBRThGLFVBQVUsQ0FBQzlGLElBQUk7WUFDdEU7UUFDSjtJQUNKO0lBQ0EsT0FBT3VPO0FBQ1g7QUFDQSxJQUFJQyxpQkFBaUIsQ0FBQzVLLGVBQWVrQyxhQUFld0ksZ0NBQWdDMUssZUFBZWtDLFlBQVlxSSxnQkFBZ0JySTtBQUUvSCxNQUFNMkksZ0JBQWdCO0lBQ2xCNVEsT0FBTztJQUNQdUgsU0FBUztBQUNiO0FBQ0EsTUFBTXNKLGNBQWM7SUFBRTdRLE9BQU87SUFBTXVILFNBQVM7QUFBSztBQUNqRCxJQUFJdUosbUJBQW1CLENBQUNDO0lBQ3BCLElBQUkxUSxNQUFNQyxPQUFPLENBQUN5USxVQUFVO1FBQ3hCLElBQUlBLFFBQVFyTixNQUFNLEdBQUcsR0FBRztZQUNwQixNQUFNa0YsU0FBU21JLFFBQ1YxTyxNQUFNLENBQUMsQ0FBQzJPLFNBQVdBLFVBQVVBLE9BQU90USxPQUFPLElBQUksQ0FBQ3NRLE9BQU90SyxRQUFRLEVBQy9EMkIsR0FBRyxDQUFDLENBQUMySSxTQUFXQSxPQUFPaFIsS0FBSztZQUNqQyxPQUFPO2dCQUFFQSxPQUFPNEk7Z0JBQVFyQixTQUFTLENBQUMsQ0FBQ3FCLE9BQU9sRixNQUFNO1lBQUM7UUFDckQ7UUFDQSxPQUFPcU4sT0FBTyxDQUFDLEVBQUUsQ0FBQ3JRLE9BQU8sSUFBSSxDQUFDcVEsT0FBTyxDQUFDLEVBQUUsQ0FBQ3JLLFFBQVEsR0FFekNxSyxPQUFPLENBQUMsRUFBRSxDQUFDRSxVQUFVLElBQUksQ0FBQzFPLFlBQVl3TyxPQUFPLENBQUMsRUFBRSxDQUFDRSxVQUFVLENBQUNqUixLQUFLLElBQzNEdUMsWUFBWXdPLE9BQU8sQ0FBQyxFQUFFLENBQUMvUSxLQUFLLEtBQUsrUSxPQUFPLENBQUMsRUFBRSxDQUFDL1EsS0FBSyxLQUFLLEtBQ2xENlEsY0FDQTtZQUFFN1EsT0FBTytRLE9BQU8sQ0FBQyxFQUFFLENBQUMvUSxLQUFLO1lBQUV1SCxTQUFTO1FBQUssSUFDN0NzSixjQUNSRDtJQUNWO0lBQ0EsT0FBT0E7QUFDWDtBQUVBLElBQUlNLGtCQUFrQixDQUFDbFIsUUFBTyxFQUFFbVIsYUFBYSxFQUFFQyxXQUFXLEVBQUVDLFVBQVUsRUFBRSxHQUFLOU8sWUFBWXZDLFVBQ25GQSxTQUNBbVIsZ0JBQ0luUixXQUFVLEtBQ05zUixNQUNBdFIsU0FDSSxDQUFDQSxTQUNEQSxTQUNSb1IsZUFBZXRKLFNBQVM5SCxVQUNwQixJQUFJQyxLQUFLRCxVQUNUcVIsYUFDSUEsV0FBV3JSLFVBQ1hBO0FBRWxCLE1BQU11UixnQkFBZ0I7SUFDbEJoSyxTQUFTO0lBQ1R2SCxPQUFPO0FBQ1g7QUFDQSxJQUFJd1IsZ0JBQWdCLENBQUNULFVBQVkxUSxNQUFNQyxPQUFPLENBQUN5USxXQUN6Q0EsUUFBUS9OLE1BQU0sQ0FBQyxDQUFDeU8sVUFBVVQsU0FBV0EsVUFBVUEsT0FBT3RRLE9BQU8sSUFBSSxDQUFDc1EsT0FBT3RLLFFBQVEsR0FDN0U7WUFDRWEsU0FBUztZQUNUdkgsT0FBT2dSLE9BQU9oUixLQUFLO1FBQ3ZCLElBQ0V5UixVQUFVRixpQkFDZEE7QUFFTixTQUFTRyxjQUFjeEgsRUFBRTtJQUNyQixNQUFNSixNQUFNSSxHQUFHSixHQUFHO0lBQ2xCLElBQUlxRixZQUFZckYsTUFBTTtRQUNsQixPQUFPQSxJQUFJNkgsS0FBSztJQUNwQjtJQUNBLElBQUlqQyxhQUFhNUYsTUFBTTtRQUNuQixPQUFPMEgsY0FBY3RILEdBQUcwSCxJQUFJLEVBQUU1UixLQUFLO0lBQ3ZDO0lBQ0EsSUFBSXlQLGlCQUFpQjNGLE1BQU07UUFDdkIsT0FBTztlQUFJQSxJQUFJK0gsZUFBZTtTQUFDLENBQUN4SixHQUFHLENBQUMsQ0FBQyxFQUFFckksT0FBQUEsTUFBSyxFQUFFLEdBQUtBO0lBQ3ZEO0lBQ0EsSUFBSUosZ0JBQWdCa0ssTUFBTTtRQUN0QixPQUFPZ0gsaUJBQWlCNUcsR0FBRzBILElBQUksRUFBRTVSLEtBQUs7SUFDMUM7SUFDQSxPQUFPa1IsZ0JBQWdCM08sWUFBWXVILElBQUk5SixLQUFLLElBQUlrSyxHQUFHSixHQUFHLENBQUM5SixLQUFLLEdBQUc4SixJQUFJOUosS0FBSyxFQUFFa0s7QUFDOUU7QUFFQSxJQUFJNEgscUJBQXFCLENBQUNDLGFBQWE5SCxTQUFTK0gsY0FBY0M7SUFDMUQsTUFBTTFCLFNBQVMsQ0FBQztJQUNoQixLQUFLLE1BQU0zUCxRQUFRbVIsWUFBYTtRQUM1QixNQUFNL0gsUUFBUXRILElBQUl1SCxTQUFTcko7UUFDM0JvSixTQUFTekcsSUFBSWdOLFFBQVEzUCxNQUFNb0osTUFBTUUsRUFBRTtJQUN2QztJQUNBLE9BQU87UUFDSDhIO1FBQ0FoUixPQUFPO2VBQUkrUTtTQUFZO1FBQ3ZCeEI7UUFDQTBCO0lBQ0o7QUFDSjtBQUVBLElBQUlDLFVBQVUsQ0FBQ2xTLFNBQVVBLGtCQUFpQm1TO0FBRTFDLElBQUlDLGVBQWUsQ0FBQ0MsT0FBUzlQLFlBQVk4UCxRQUNuQ0EsT0FDQUgsUUFBUUcsUUFDSkEsS0FBS0MsTUFBTSxHQUNYbFMsU0FBU2lTLFFBQ0xILFFBQVFHLEtBQUtyUyxLQUFLLElBQ2RxUyxLQUFLclMsS0FBSyxDQUFDc1MsTUFBTSxHQUNqQkQsS0FBS3JTLEtBQUssR0FDZHFTO0FBRWQsSUFBSUUscUJBQXFCLENBQUNDLE9BQVU7UUFDaENDLFlBQVksQ0FBQ0QsUUFBUUEsU0FBU3JPLGdCQUFnQkcsUUFBUTtRQUN0RG9PLFVBQVVGLFNBQVNyTyxnQkFBZ0JDLE1BQU07UUFDekN1TyxZQUFZSCxTQUFTck8sZ0JBQWdCRSxRQUFRO1FBQzdDdU8sU0FBU0osU0FBU3JPLGdCQUFnQkssR0FBRztRQUNyQ3FPLFdBQVdMLFNBQVNyTyxnQkFBZ0JJLFNBQVM7SUFDakQ7QUFFQSxNQUFNdU8saUJBQWlCO0FBQ3ZCLElBQUlDLHVCQUF1QixDQUFDQyxpQkFBbUIsQ0FBQyxDQUFDQSxrQkFDN0MsQ0FBQyxDQUFDQSxlQUFlaE8sUUFBUSxJQUN6QixDQUFDLENBQUUsWUFBWWdPLGVBQWVoTyxRQUFRLEtBQ2xDZ08sZUFBZWhPLFFBQVEsQ0FBQzNELFdBQVcsQ0FBQ1QsSUFBSSxLQUFLa1Msa0JBQzVDMVMsU0FBUzRTLGVBQWVoTyxRQUFRLEtBQzdCaUIsT0FBTzJDLE1BQU0sQ0FBQ29LLGVBQWVoTyxRQUFRLEVBQUVpTyxJQUFJLENBQUMsQ0FBQ0MsbUJBQXFCQSxpQkFBaUI3UixXQUFXLENBQUNULElBQUksS0FBS2tTLGVBQWU7QUFFbkksSUFBSUssZ0JBQWdCLENBQUNwQyxVQUFZQSxRQUFRcEcsS0FBSyxJQUN6Q29HLENBQUFBLFFBQVFoTSxRQUFRLElBQ2JnTSxRQUFRcE0sR0FBRyxJQUNYb00sUUFBUXJNLEdBQUcsSUFDWHFNLFFBQVFuTSxTQUFTLElBQ2pCbU0sUUFBUWxNLFNBQVMsSUFDakJrTSxRQUFRak0sT0FBTyxJQUNmaU0sUUFBUS9MLFFBQVE7QUFFeEIsSUFBSW9PLFlBQVksQ0FBQ3hTLE1BQU1vSCxRQUFRcUwsY0FBZ0IsQ0FBQ0EsZUFDM0NyTCxDQUFBQSxPQUFPTyxRQUFRLElBQ1pQLE9BQU9HLEtBQUssQ0FBQ2xILEdBQUcsQ0FBQ0wsU0FDakI7V0FBSW9ILE9BQU9HLEtBQUs7S0FBQyxDQUFDNEUsSUFBSSxDQUFDLENBQUN1RyxZQUFjMVMsS0FBSzJTLFVBQVUsQ0FBQ0QsY0FDbEQsU0FBU25RLElBQUksQ0FBQ3ZDLEtBQUtvUCxLQUFLLENBQUNzRCxVQUFVNVAsTUFBTSxHQUFFO0FBRXZELE1BQU04UCx3QkFBd0IsQ0FBQ2pELFFBQVExRixRQUFRa0gsYUFBYTBCO0lBQ3hELEtBQUssTUFBTXRSLE9BQU80UCxlQUFlOUwsT0FBT29GLElBQUksQ0FBQ2tGLFFBQVM7UUFDbEQsTUFBTXZHLFFBQVF0SCxJQUFJNk4sUUFBUXBPO1FBQzFCLElBQUk2SCxPQUFPO1lBQ1AsTUFBTSxFQUFFRSxFQUFFLEVBQUUsR0FBR3dKLGNBQWMsR0FBRzFKO1lBQ2hDLElBQUlFLElBQUk7Z0JBQ0osSUFBSUEsR0FBRzBILElBQUksSUFBSTFILEdBQUcwSCxJQUFJLENBQUMsRUFBRSxJQUFJL0csT0FBT1gsR0FBRzBILElBQUksQ0FBQyxFQUFFLEVBQUV6UCxRQUFRLENBQUNzUixZQUFZO29CQUNqRSxPQUFPO2dCQUNYLE9BQ0ssSUFBSXZKLEdBQUdKLEdBQUcsSUFBSWUsT0FBT1gsR0FBR0osR0FBRyxFQUFFSSxHQUFHdEosSUFBSSxLQUFLLENBQUM2UyxZQUFZO29CQUN2RCxPQUFPO2dCQUNYLE9BQ0s7b0JBQ0QsSUFBSUQsc0JBQXNCRSxjQUFjN0ksU0FBUzt3QkFDN0M7b0JBQ0o7Z0JBQ0o7WUFDSixPQUNLLElBQUl6SyxTQUFTc1QsZUFBZTtnQkFDN0IsSUFBSUYsc0JBQXNCRSxjQUFjN0ksU0FBUztvQkFDN0M7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0o7SUFDQTtBQUNKO0FBRUEsU0FBUzhJLGtCQUFrQm5NLE1BQU0sRUFBRXlDLE9BQU8sRUFBRXJKLElBQUk7SUFDNUMsTUFBTWdKLFFBQVFsSCxJQUFJOEUsUUFBUTVHO0lBQzFCLElBQUlnSixTQUFTMUcsTUFBTXRDLE9BQU87UUFDdEIsT0FBTztZQUNIZ0o7WUFDQWhKO1FBQ0o7SUFDSjtJQUNBLE1BQU1JLFFBQVFKLEtBQUttQyxLQUFLLENBQUM7SUFDekIsTUFBTy9CLE1BQU0wQyxNQUFNLENBQUU7UUFDakIsTUFBTTRFLFlBQVl0SCxNQUFNNFMsSUFBSSxDQUFDO1FBQzdCLE1BQU01SixRQUFRdEgsSUFBSXVILFNBQVMzQjtRQUMzQixNQUFNdUwsYUFBYW5SLElBQUk4RSxRQUFRYztRQUMvQixJQUFJMEIsU0FBUyxDQUFDM0osTUFBTUMsT0FBTyxDQUFDMEosVUFBVXBKLFNBQVMwSCxXQUFXO1lBQ3RELE9BQU87Z0JBQUUxSDtZQUFLO1FBQ2xCO1FBQ0EsSUFBSWlULGNBQWNBLFdBQVcvVCxJQUFJLEVBQUU7WUFDL0IsT0FBTztnQkFDSGMsTUFBTTBIO2dCQUNOc0IsT0FBT2lLO1lBQ1g7UUFDSjtRQUNBN1MsTUFBTThTLEdBQUc7SUFDYjtJQUNBLE9BQU87UUFDSGxUO0lBQ0o7QUFDSjtBQUVBLElBQUltVCx3QkFBd0IsQ0FBQ0MsZUFBZTVOLGlCQUFpQlEsaUJBQWlCZDtJQUMxRWMsZ0JBQWdCb047SUFDaEIsTUFBTSxFQUFFcFQsSUFBSSxFQUFFLEdBQUcrRSxXQUFXLEdBQUdxTztJQUMvQixPQUFROUUsY0FBY3ZKLGNBQ2xCTSxPQUFPb0YsSUFBSSxDQUFDMUYsV0FBV2pDLE1BQU0sSUFBSXVDLE9BQU9vRixJQUFJLENBQUNqRixpQkFBaUIxQyxNQUFNLElBQ3BFdUMsT0FBT29GLElBQUksQ0FBQzFGLFdBQVdzTixJQUFJLENBQUMsQ0FBQzlRLE1BQVFpRSxlQUFlLENBQUNqRSxJQUFJLEtBQ3BELEVBQUMyRCxVQUFVM0IsZ0JBQWdCSyxHQUFHO0FBQzNDO0FBRUEsSUFBSXlQLHdCQUF3QixDQUFDclQsTUFBTXNULFlBQVl2TixRQUFVLENBQUMvRixRQUN0RCxDQUFDc1QsY0FDRHRULFNBQVNzVCxjQUNUbEcsc0JBQXNCcE4sTUFBTW1NLElBQUksQ0FBQyxDQUFDb0gsY0FBZ0JBLGVBQzdDeE4sQ0FBQUEsUUFDS3dOLGdCQUFnQkQsYUFDaEJDLFlBQVlaLFVBQVUsQ0FBQ1csZUFDckJBLFdBQVdYLFVBQVUsQ0FBQ1ksWUFBVztBQUVqRCxJQUFJQyxpQkFBaUIsQ0FBQ2YsYUFBYTFKLFdBQVcwSyxhQUFhQyxnQkFBZ0I5QjtJQUN2RSxJQUFJQSxLQUFLSSxPQUFPLEVBQUU7UUFDZCxPQUFPO0lBQ1gsT0FDSyxJQUFJLENBQUN5QixlQUFlN0IsS0FBS0ssU0FBUyxFQUFFO1FBQ3JDLE9BQU8sQ0FBRWxKLENBQUFBLGFBQWEwSixXQUFVO0lBQ3BDLE9BQ0ssSUFBSWdCLGNBQWNDLGVBQWU1QixRQUFRLEdBQUdGLEtBQUtFLFFBQVEsRUFBRTtRQUM1RCxPQUFPLENBQUNXO0lBQ1osT0FDSyxJQUFJZ0IsY0FBY0MsZUFBZTNCLFVBQVUsR0FBR0gsS0FBS0csVUFBVSxFQUFFO1FBQ2hFLE9BQU9VO0lBQ1g7SUFDQSxPQUFPO0FBQ1g7QUFFQSxJQUFJa0Isa0JBQWtCLENBQUN6SyxLQUFLbEosT0FBUyxDQUFDd0IsUUFBUU0sSUFBSW9ILEtBQUtsSixPQUFPOEMsTUFBTSxJQUFJd00sTUFBTXBHLEtBQUtsSjtBQUVuRixJQUFJNFQsNEJBQTRCLENBQUNoTixRQUFRb0MsT0FBT2hKO0lBQzVDLE1BQU02VCxtQkFBbUJ6RyxzQkFBc0J0TCxJQUFJOEUsUUFBUTVHO0lBQzNEMkMsSUFBSWtSLGtCQUFrQixRQUFRN0ssS0FBSyxDQUFDaEosS0FBSztJQUN6QzJDLElBQUlpRSxRQUFRNUcsTUFBTTZUO0lBQ2xCLE9BQU9qTjtBQUNYO0FBRUEsSUFBSWtOLFlBQVksQ0FBQzFVLFNBQVU4SCxTQUFTOUg7QUFFcEMsU0FBUzJVLGlCQUFpQjdSLE1BQU0sRUFBRWdILEdBQUcsRUFBRWhLLE9BQU8sVUFBVTtJQUNwRCxJQUFJNFUsVUFBVTVSLFdBQ1R6QyxNQUFNQyxPQUFPLENBQUN3QyxXQUFXQSxPQUFPOFIsS0FBSyxDQUFDRixjQUN0Q3pSLFVBQVVILFdBQVcsQ0FBQ0EsUUFBUztRQUNoQyxPQUFPO1lBQ0hoRDtZQUNBd0ssU0FBU29LLFVBQVU1UixVQUFVQSxTQUFTO1lBQ3RDZ0g7UUFDSjtJQUNKO0FBQ0o7QUFFQSxJQUFJK0sscUJBQXFCLENBQUNDLGlCQUFtQjFVLFNBQVMwVSxtQkFBbUIsQ0FBQzVDLFFBQVE0QyxrQkFDNUVBLGlCQUNBO1FBQ0U5VSxPQUFPOFU7UUFDUHhLLFNBQVM7SUFDYjtBQUVKLElBQUl5SyxnQkFBZ0IsT0FBTy9LLE9BQU9nTCxvQkFBb0IvTSxZQUFZNkYsMEJBQTBCbUUsMkJBQTJCZ0Q7SUFDbkgsTUFBTSxFQUFFbkwsR0FBRyxFQUFFOEgsSUFBSSxFQUFFN00sUUFBUSxFQUFFSCxTQUFTLEVBQUVDLFNBQVMsRUFBRUYsR0FBRyxFQUFFRCxHQUFHLEVBQUVJLE9BQU8sRUFBRUUsUUFBUSxFQUFFcEUsSUFBSSxFQUFFdVEsYUFBYSxFQUFFeEcsS0FBSyxFQUFHLEdBQUdYLE1BQU1FLEVBQUU7SUFDeEgsTUFBTWdMLGFBQWF4UyxJQUFJdUYsWUFBWXJIO0lBQ25DLElBQUksQ0FBQytKLFNBQVNxSyxtQkFBbUIvVCxHQUFHLENBQUNMLE9BQU87UUFDeEMsT0FBTyxDQUFDO0lBQ1o7SUFDQSxNQUFNdVUsV0FBV3ZELE9BQU9BLElBQUksQ0FBQyxFQUFFLEdBQUc5SDtJQUNsQyxNQUFNTyxvQkFBb0IsQ0FBQ0M7UUFDdkIsSUFBSTJILDZCQUE2QmtELFNBQVM1SyxjQUFjLEVBQUU7WUFDdEQ0SyxTQUFTOUssaUJBQWlCLENBQUNwSCxVQUFVcUgsV0FBVyxLQUFLQSxXQUFXO1lBQ2hFNkssU0FBUzVLLGNBQWM7UUFDM0I7SUFDSjtJQUNBLE1BQU1YLFFBQVEsQ0FBQztJQUNmLE1BQU13TCxVQUFVMUYsYUFBYTVGO0lBQzdCLE1BQU11TCxhQUFhelYsZ0JBQWdCa0s7SUFDbkMsTUFBTTZGLG9CQUFvQnlGLFdBQVdDO0lBQ3JDLE1BQU1DLFVBQVUsQ0FBRW5FLGlCQUFpQmhDLFlBQVlyRixJQUFHLEtBQzlDdkgsWUFBWXVILElBQUk5SixLQUFLLEtBQ3JCdUMsWUFBWTJTLGVBQ1g3RixjQUFjdkYsUUFBUUEsSUFBSTlKLEtBQUssS0FBSyxNQUNyQ2tWLGVBQWUsTUFDZDdVLE1BQU1DLE9BQU8sQ0FBQzRVLGVBQWUsQ0FBQ0EsV0FBV3hSLE1BQU07SUFDcEQsTUFBTTZSLG9CQUFvQjFILGFBQWEySCxJQUFJLENBQUMsTUFBTTVVLE1BQU1rTiwwQkFBMEJsRTtJQUNsRixNQUFNNkwsbUJBQW1CLENBQUNDLFdBQVdDLGtCQUFrQkMsa0JBQWtCQyxVQUFVcFIsdUJBQXVCRyxTQUFTLEVBQUVrUixVQUFVclIsdUJBQXVCSSxTQUFTO1FBQzNKLE1BQU15RixVQUFVb0wsWUFBWUMsbUJBQW1CQztRQUMvQ2hNLEtBQUssQ0FBQ2hKLEtBQUssR0FBRztZQUNWZCxNQUFNNFYsWUFBWUcsVUFBVUM7WUFDNUJ4TDtZQUNBUjtZQUNBLEdBQUd5TCxrQkFBa0JHLFlBQVlHLFVBQVVDLFNBQVN4TCxRQUFRO1FBQ2hFO0lBQ0o7SUFDQSxJQUFJMkssZUFDRSxDQUFDNVUsTUFBTUMsT0FBTyxDQUFDNFUsZUFBZSxDQUFDQSxXQUFXeFIsTUFBTSxHQUNoRHFCLFlBQ0csRUFBRTRLLHFCQUFzQjJGLENBQUFBLFdBQVdwVixrQkFBa0JnVixXQUFVLEtBQzNEalMsVUFBVWlTLGVBQWUsQ0FBQ0EsY0FDMUJHLGNBQWMsQ0FBQ3ZFLGlCQUFpQmMsTUFBTXJLLE9BQU8sSUFDN0M2TixXQUFXLENBQUM1RCxjQUFjSSxNQUFNckssT0FBTyxHQUFJO1FBQ3BELE1BQU0sRUFBRXZILE9BQUFBLE1BQUssRUFBRXNLLE9BQU8sRUFBRSxHQUFHb0ssVUFBVTNQLFlBQy9CO1lBQUUvRSxPQUFPLENBQUMsQ0FBQytFO1lBQVV1RixTQUFTdkY7UUFBUyxJQUN2QzhQLG1CQUFtQjlQO1FBQ3pCLElBQUkvRSxRQUFPO1lBQ1A0SixLQUFLLENBQUNoSixLQUFLLEdBQUc7Z0JBQ1ZkLE1BQU0yRSx1QkFBdUJNLFFBQVE7Z0JBQ3JDdUY7Z0JBQ0FSLEtBQUtxTDtnQkFDTCxHQUFHSSxrQkFBa0I5USx1QkFBdUJNLFFBQVEsRUFBRXVGLFFBQVE7WUFDbEU7WUFDQSxJQUFJLENBQUN3RCwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCQztnQkFDbEIsT0FBT1Y7WUFDWDtRQUNKO0lBQ0o7SUFDQSxJQUFJLENBQUMwTCxXQUFZLEVBQUNwVixrQkFBa0J5RSxRQUFRLENBQUN6RSxrQkFBa0J3RSxJQUFHLEdBQUk7UUFDbEUsSUFBSWdSO1FBQ0osSUFBSUs7UUFDSixNQUFNQyxZQUFZbkIsbUJBQW1CblE7UUFDckMsTUFBTXVSLFlBQVlwQixtQkFBbUJsUTtRQUNyQyxJQUFJLENBQUN6RSxrQkFBa0JnVixlQUFlLENBQUNwUixNQUFNb1IsYUFBYTtZQUN0RCxNQUFNZ0IsY0FBY3BNLElBQUlxSCxhQUFhLElBQ2hDK0QsQ0FBQUEsYUFBYSxDQUFDQSxhQUFhQSxVQUFTO1lBQ3pDLElBQUksQ0FBQ2hWLGtCQUFrQjhWLFVBQVVoVyxLQUFLLEdBQUc7Z0JBQ3JDMFYsWUFBWVEsY0FBY0YsVUFBVWhXLEtBQUs7WUFDN0M7WUFDQSxJQUFJLENBQUNFLGtCQUFrQitWLFVBQVVqVyxLQUFLLEdBQUc7Z0JBQ3JDK1YsWUFBWUcsY0FBY0QsVUFBVWpXLEtBQUs7WUFDN0M7UUFDSixPQUNLO1lBQ0QsTUFBTW1XLFlBQVlyTSxJQUFJc0gsV0FBVyxJQUFJLElBQUluUixLQUFLaVY7WUFDOUMsTUFBTWtCLG9CQUFvQixDQUFDQyxPQUFTLElBQUlwVyxLQUFLLElBQUlBLE9BQU9xVyxZQUFZLEtBQUssTUFBTUQ7WUFDL0UsTUFBTUUsU0FBU3pNLElBQUloSyxJQUFJLElBQUk7WUFDM0IsTUFBTTBXLFNBQVMxTSxJQUFJaEssSUFBSSxJQUFJO1lBQzNCLElBQUlnSSxTQUFTa08sVUFBVWhXLEtBQUssS0FBS2tWLFlBQVk7Z0JBQ3pDUSxZQUFZYSxTQUNOSCxrQkFBa0JsQixjQUFja0Isa0JBQWtCSixVQUFVaFcsS0FBSyxJQUNqRXdXLFNBQ0l0QixhQUFhYyxVQUFVaFcsS0FBSyxHQUM1Qm1XLFlBQVksSUFBSWxXLEtBQUsrVixVQUFVaFcsS0FBSztZQUNsRDtZQUNBLElBQUk4SCxTQUFTbU8sVUFBVWpXLEtBQUssS0FBS2tWLFlBQVk7Z0JBQ3pDYSxZQUFZUSxTQUNOSCxrQkFBa0JsQixjQUFja0Isa0JBQWtCSCxVQUFValcsS0FBSyxJQUNqRXdXLFNBQ0l0QixhQUFhZSxVQUFValcsS0FBSyxHQUM1Qm1XLFlBQVksSUFBSWxXLEtBQUtnVyxVQUFValcsS0FBSztZQUNsRDtRQUNKO1FBQ0EsSUFBSTBWLGFBQWFLLFdBQVc7WUFDeEJOLGlCQUFpQixDQUFDLENBQUNDLFdBQVdNLFVBQVUxTCxPQUFPLEVBQUUyTCxVQUFVM0wsT0FBTyxFQUFFN0YsdUJBQXVCQyxHQUFHLEVBQUVELHVCQUF1QkUsR0FBRztZQUMxSCxJQUFJLENBQUNtSiwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCVCxLQUFLLENBQUNoSixLQUFLLENBQUMwSixPQUFPO2dCQUNyQyxPQUFPVjtZQUNYO1FBQ0o7SUFDSjtJQUNBLElBQUksQ0FBQ2hGLGFBQWFDLFNBQVEsS0FDdEIsQ0FBQ3lRLFdBQ0F4TixDQUFBQSxTQUFTb04sZUFBZ0JELGdCQUFnQjVVLE1BQU1DLE9BQU8sQ0FBQzRVLFdBQVcsR0FBSTtRQUN2RSxNQUFNdUIsa0JBQWtCNUIsbUJBQW1CalE7UUFDM0MsTUFBTThSLGtCQUFrQjdCLG1CQUFtQmhRO1FBQzNDLE1BQU02USxZQUFZLENBQUN4VixrQkFBa0J1VyxnQkFBZ0J6VyxLQUFLLEtBQ3REa1YsV0FBV3hSLE1BQU0sR0FBRyxDQUFDK1MsZ0JBQWdCelcsS0FBSztRQUM5QyxNQUFNK1YsWUFBWSxDQUFDN1Ysa0JBQWtCd1csZ0JBQWdCMVcsS0FBSyxLQUN0RGtWLFdBQVd4UixNQUFNLEdBQUcsQ0FBQ2dULGdCQUFnQjFXLEtBQUs7UUFDOUMsSUFBSTBWLGFBQWFLLFdBQVc7WUFDeEJOLGlCQUFpQkMsV0FBV2UsZ0JBQWdCbk0sT0FBTyxFQUFFb00sZ0JBQWdCcE0sT0FBTztZQUM1RSxJQUFJLENBQUN3RCwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCVCxLQUFLLENBQUNoSixLQUFLLENBQUMwSixPQUFPO2dCQUNyQyxPQUFPVjtZQUNYO1FBQ0o7SUFDSjtJQUNBLElBQUk5RSxXQUFXLENBQUN3USxXQUFXeE4sU0FBU29OLGFBQWE7UUFDN0MsTUFBTSxFQUFFbFYsT0FBTzJXLFlBQVksRUFBRXJNLE9BQU8sRUFBRSxHQUFHdUssbUJBQW1CL1A7UUFDNUQsSUFBSW9OLFFBQVF5RSxpQkFBaUIsQ0FBQ3pCLFdBQVcwQixLQUFLLENBQUNELGVBQWU7WUFDMUQvTSxLQUFLLENBQUNoSixLQUFLLEdBQUc7Z0JBQ1ZkLE1BQU0yRSx1QkFBdUJLLE9BQU87Z0JBQ3BDd0Y7Z0JBQ0FSO2dCQUNBLEdBQUd5TCxrQkFBa0I5USx1QkFBdUJLLE9BQU8sRUFBRXdGLFFBQVE7WUFDakU7WUFDQSxJQUFJLENBQUN3RCwwQkFBMEI7Z0JBQzNCekQsa0JBQWtCQztnQkFDbEIsT0FBT1Y7WUFDWDtRQUNKO0lBQ0o7SUFDQSxJQUFJNUUsVUFBVTtRQUNWLElBQUlvSyxXQUFXcEssV0FBVztZQUN0QixNQUFNbEMsU0FBUyxNQUFNa0MsU0FBU2tRLFlBQVlqTjtZQUMxQyxNQUFNNE8sZ0JBQWdCbEMsaUJBQWlCN1IsUUFBUXFTO1lBQy9DLElBQUkwQixlQUFlO2dCQUNmak4sS0FBSyxDQUFDaEosS0FBSyxHQUFHO29CQUNWLEdBQUdpVyxhQUFhO29CQUNoQixHQUFHdEIsa0JBQWtCOVEsdUJBQXVCTyxRQUFRLEVBQUU2UixjQUFjdk0sT0FBTyxDQUFDO2dCQUNoRjtnQkFDQSxJQUFJLENBQUN3RCwwQkFBMEI7b0JBQzNCekQsa0JBQWtCd00sY0FBY3ZNLE9BQU87b0JBQ3ZDLE9BQU9WO2dCQUNYO1lBQ0o7UUFDSixPQUNLLElBQUl4SixTQUFTNEUsV0FBVztZQUN6QixJQUFJOFIsbUJBQW1CLENBQUM7WUFDeEIsSUFBSyxNQUFNM1UsT0FBTzZDLFNBQVU7Z0JBQ3hCLElBQUksQ0FBQ2tLLGNBQWM0SCxxQkFBcUIsQ0FBQ2hKLDBCQUEwQjtvQkFDL0Q7Z0JBQ0o7Z0JBQ0EsTUFBTStJLGdCQUFnQmxDLGlCQUFpQixNQUFNM1AsUUFBUSxDQUFDN0MsSUFBSSxDQUFDK1MsWUFBWWpOLGFBQWFrTixVQUFVaFQ7Z0JBQzlGLElBQUkwVSxlQUFlO29CQUNmQyxtQkFBbUI7d0JBQ2YsR0FBR0QsYUFBYTt3QkFDaEIsR0FBR3RCLGtCQUFrQnBULEtBQUswVSxjQUFjdk0sT0FBTyxDQUFDO29CQUNwRDtvQkFDQUQsa0JBQWtCd00sY0FBY3ZNLE9BQU87b0JBQ3ZDLElBQUl3RCwwQkFBMEI7d0JBQzFCbEUsS0FBSyxDQUFDaEosS0FBSyxHQUFHa1c7b0JBQ2xCO2dCQUNKO1lBQ0o7WUFDQSxJQUFJLENBQUM1SCxjQUFjNEgsbUJBQW1CO2dCQUNsQ2xOLEtBQUssQ0FBQ2hKLEtBQUssR0FBRztvQkFDVmtKLEtBQUtxTDtvQkFDTCxHQUFHMkIsZ0JBQWdCO2dCQUN2QjtnQkFDQSxJQUFJLENBQUNoSiwwQkFBMEI7b0JBQzNCLE9BQU9sRTtnQkFDWDtZQUNKO1FBQ0o7SUFDSjtJQUNBUyxrQkFBa0I7SUFDbEIsT0FBT1Q7QUFDWDtBQUVBLE1BQU1tTixpQkFBaUI7SUFDbkJ2RSxNQUFNck8sZ0JBQWdCRyxRQUFRO0lBQzlCZ1EsZ0JBQWdCblEsZ0JBQWdCRSxRQUFRO0lBQ3hDMlMsa0JBQWtCO0FBQ3RCO0FBQ0EsU0FBU0Msa0JBQWtCM1IsUUFBUSxDQUFDLENBQUM7SUFDakMsSUFBSW1GLFdBQVc7UUFDWCxHQUFHc00sY0FBYztRQUNqQixHQUFHelIsS0FBSztJQUNaO0lBQ0EsSUFBSXdCLGFBQWE7UUFDYm9RLGFBQWE7UUFDYmpRLFNBQVM7UUFDVGtRLFNBQVM7UUFDVGpRLFdBQVdrSSxXQUFXM0UsU0FBUzFFLGFBQWE7UUFDNUN1QixjQUFjO1FBQ2QrTSxhQUFhO1FBQ2IrQyxjQUFjO1FBQ2QzSixvQkFBb0I7UUFDcEJsRyxTQUFTO1FBQ1RILGVBQWUsQ0FBQztRQUNoQkQsYUFBYSxDQUFDO1FBQ2RFLGtCQUFrQixDQUFDO1FBQ25CRyxRQUFRaUQsU0FBU2pELE1BQU0sSUFBSSxDQUFDO1FBQzVCZCxVQUFVK0QsU0FBUy9ELFFBQVEsSUFBSTtJQUNuQztJQUNBLE1BQU11RCxVQUFVLENBQUM7SUFDakIsSUFBSWpFLGlCQUFpQjVGLFNBQVNxSyxTQUFTMUUsYUFBYSxLQUFLM0YsU0FBU3FLLFNBQVM3QixNQUFNLElBQzNFaEgsWUFBWTZJLFNBQVMxRSxhQUFhLElBQUkwRSxTQUFTN0IsTUFBTSxLQUFLLENBQUMsSUFDM0QsQ0FBQztJQUNQLElBQUlDLGNBQWM0QixTQUFTekIsZ0JBQWdCLEdBQ3JDLENBQUMsSUFDRHBILFlBQVlvRTtJQUNsQixJQUFJNEUsU0FBUztRQUNUQyxRQUFRO1FBQ1JGLE9BQU87UUFDUHhDLE9BQU87SUFDWDtJQUNBLElBQUlILFNBQVM7UUFDVDJDLE9BQU8sSUFBSTFJO1FBQ1h5RSxVQUFVLElBQUl6RTtRQUNkb1YsU0FBUyxJQUFJcFY7UUFDYmlILE9BQU8sSUFBSWpIO1FBQ1hrRyxPQUFPLElBQUlsRztJQUNmO0lBQ0EsSUFBSXFWO0lBQ0osSUFBSUMsUUFBUTtJQUNaLE1BQU1uUixrQkFBa0I7UUFDcEJhLFNBQVM7UUFDVEUsYUFBYTtRQUNiRSxrQkFBa0I7UUFDbEJELGVBQWU7UUFDZkUsY0FBYztRQUNkQyxTQUFTO1FBQ1RDLFFBQVE7SUFDWjtJQUNBLElBQUlnUSwyQkFBMkI7UUFDM0IsR0FBR3BSLGVBQWU7SUFDdEI7SUFDQSxNQUFNa0gsWUFBWTtRQUNkcEUsT0FBTytFO1FBQ1BWLE9BQU9VO0lBQ1g7SUFDQSxNQUFNd0osbUNBQW1DaE4sU0FBU3VILFlBQVksS0FBSzdOLGdCQUFnQkssR0FBRztJQUN0RixNQUFNa1QsV0FBVyxDQUFDL1AsV0FBYSxDQUFDZ1E7WUFDNUJDLGFBQWFMO1lBQ2JBLFFBQVFNLFdBQVdsUSxVQUFVZ1E7UUFDakM7SUFDQSxNQUFNL1AsWUFBWSxPQUFPa1E7UUFDckIsSUFBSSxDQUFDck4sU0FBUy9ELFFBQVEsSUFDakJOLENBQUFBLGdCQUFnQm1CLE9BQU8sSUFDcEJpUSx5QkFBeUJqUSxPQUFPLElBQ2hDdVEsaUJBQWdCLEdBQUk7WUFDeEIsTUFBTXZRLFVBQVVrRCxTQUFTc04sUUFBUSxHQUMzQjdJLGNBQWMsQ0FBQyxNQUFNOEksWUFBVyxFQUFHeFEsTUFBTSxJQUN6QyxNQUFNeVEseUJBQXlCaE8sU0FBUztZQUM5QyxJQUFJMUMsWUFBWVQsV0FBV1MsT0FBTyxFQUFFO2dCQUNoQytGLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUNqQmpHO2dCQUNKO1lBQ0o7UUFDSjtJQUNKO0lBQ0EsTUFBTTJRLHNCQUFzQixDQUFDbFgsT0FBT3NHO1FBQ2hDLElBQUksQ0FBQ21ELFNBQVMvRCxRQUFRLElBQ2pCTixDQUFBQSxnQkFBZ0JrQixZQUFZLElBQ3pCbEIsZ0JBQWdCaUIsZ0JBQWdCLElBQ2hDbVEseUJBQXlCbFEsWUFBWSxJQUNyQ2tRLHlCQUF5Qm5RLGdCQUFnQixHQUFHO1lBQy9DckcsQ0FBQUEsU0FBU1gsTUFBTThYLElBQUksQ0FBQ25RLE9BQU8yQyxLQUFLLEdBQUd5TixPQUFPLENBQUMsQ0FBQ3hYO2dCQUN6QyxJQUFJQSxNQUFNO29CQUNOMEcsZUFDTS9ELElBQUl1RCxXQUFXTyxnQkFBZ0IsRUFBRXpHLE1BQU0wRyxnQkFDdkM0SSxNQUFNcEosV0FBV08sZ0JBQWdCLEVBQUV6RztnQkFDN0M7WUFDSjtZQUNBME0sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCbkcsa0JBQWtCUCxXQUFXTyxnQkFBZ0I7Z0JBQzdDQyxjQUFjLENBQUM0SCxjQUFjcEksV0FBV08sZ0JBQWdCO1lBQzVEO1FBQ0o7SUFDSjtJQUNBLE1BQU1nUixpQkFBaUIsQ0FBQ3pYLE1BQU1nSSxTQUFTLEVBQUUsRUFBRWdELFFBQVEwTSxNQUFNQyxrQkFBa0IsSUFBSSxFQUFFQyw2QkFBNkIsSUFBSTtRQUM5RyxJQUFJRixRQUFRMU0sVUFBVSxDQUFDbkIsU0FBUy9ELFFBQVEsRUFBRTtZQUN0Q2tFLE9BQU9DLE1BQU0sR0FBRztZQUNoQixJQUFJMk4sOEJBQThCblksTUFBTUMsT0FBTyxDQUFDb0MsSUFBSXVILFNBQVNySixRQUFRO2dCQUNqRSxNQUFNNlgsY0FBYzdNLE9BQU9sSixJQUFJdUgsU0FBU3JKLE9BQU8wWCxLQUFLSSxJQUFJLEVBQUVKLEtBQUtLLElBQUk7Z0JBQ25FSixtQkFBbUJoVixJQUFJMEcsU0FBU3JKLE1BQU02WDtZQUMxQztZQUNBLElBQUlELDhCQUNBblksTUFBTUMsT0FBTyxDQUFDb0MsSUFBSW9FLFdBQVdVLE1BQU0sRUFBRTVHLFFBQVE7Z0JBQzdDLE1BQU00RyxTQUFTb0UsT0FBT2xKLElBQUlvRSxXQUFXVSxNQUFNLEVBQUU1RyxPQUFPMFgsS0FBS0ksSUFBSSxFQUFFSixLQUFLSyxJQUFJO2dCQUN4RUosbUJBQW1CaFYsSUFBSXVELFdBQVdVLE1BQU0sRUFBRTVHLE1BQU00RztnQkFDaEQrTSxnQkFBZ0J6TixXQUFXVSxNQUFNLEVBQUU1RztZQUN2QztZQUNBLElBQUksQ0FBQ3dGLGdCQUFnQmdCLGFBQWEsSUFDOUJvUSx5QkFBeUJwUSxhQUFhLEtBQ3RDb1IsOEJBQ0FuWSxNQUFNQyxPQUFPLENBQUNvQyxJQUFJb0UsV0FBV00sYUFBYSxFQUFFeEcsUUFBUTtnQkFDcEQsTUFBTXdHLGdCQUFnQndFLE9BQU9sSixJQUFJb0UsV0FBV00sYUFBYSxFQUFFeEcsT0FBTzBYLEtBQUtJLElBQUksRUFBRUosS0FBS0ssSUFBSTtnQkFDdEZKLG1CQUFtQmhWLElBQUl1RCxXQUFXTSxhQUFhLEVBQUV4RyxNQUFNd0c7WUFDM0Q7WUFDQSxJQUFJaEIsZ0JBQWdCZSxXQUFXLElBQUlxUSx5QkFBeUJyUSxXQUFXLEVBQUU7Z0JBQ3JFTCxXQUFXSyxXQUFXLEdBQUd3SixlQUFlM0ssZ0JBQWdCNkM7WUFDNUQ7WUFDQXlFLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjVNO2dCQUNBcUcsU0FBUzJSLFVBQVVoWSxNQUFNZ0k7Z0JBQ3pCekIsYUFBYUwsV0FBV0ssV0FBVztnQkFDbkNLLFFBQVFWLFdBQVdVLE1BQU07Z0JBQ3pCRCxTQUFTVCxXQUFXUyxPQUFPO1lBQy9CO1FBQ0osT0FDSztZQUNEaEUsSUFBSXNGLGFBQWFqSSxNQUFNZ0k7UUFDM0I7SUFDSjtJQUNBLE1BQU1pUSxlQUFlLENBQUNqWSxNQUFNZ0o7UUFDeEJyRyxJQUFJdUQsV0FBV1UsTUFBTSxFQUFFNUcsTUFBTWdKO1FBQzdCMEQsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakJoRyxRQUFRVixXQUFXVSxNQUFNO1FBQzdCO0lBQ0o7SUFDQSxNQUFNc1IsYUFBYSxDQUFDdFI7UUFDaEJWLFdBQVdVLE1BQU0sR0FBR0E7UUFDcEI4RixVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQmhHLFFBQVFWLFdBQVdVLE1BQU07WUFDekJELFNBQVM7UUFDYjtJQUNKO0lBQ0EsTUFBTXdSLHNCQUFzQixDQUFDblksTUFBTW9ZLHNCQUFzQmhaLFFBQU84SjtRQUM1RCxNQUFNRSxRQUFRdEgsSUFBSXVILFNBQVNySjtRQUMzQixJQUFJb0osT0FBTztZQUNQLE1BQU1uSCxlQUFlSCxJQUFJbUcsYUFBYWpJLE1BQU0yQixZQUFZdkMsVUFBUzBDLElBQUlzRCxnQkFBZ0JwRixRQUFRWjtZQUM3RnVDLFlBQVlNLGlCQUNQaUgsT0FBT0EsSUFBSW1QLGNBQWMsSUFDMUJELHVCQUNFelYsSUFBSXNGLGFBQWFqSSxNQUFNb1ksdUJBQXVCblcsZUFBZTZPLGNBQWMxSCxNQUFNRSxFQUFFLEtBQ25GZ1AsY0FBY3RZLE1BQU1pQztZQUMxQitILE9BQU9ELEtBQUssSUFBSS9DO1FBQ3BCO0lBQ0o7SUFDQSxNQUFNdVIsc0JBQXNCLENBQUN2WSxNQUFNd1ksWUFBWS9GLGFBQWFnRyxhQUFhQztRQUNyRSxJQUFJQyxvQkFBb0I7UUFDeEIsSUFBSUMsa0JBQWtCO1FBQ3RCLE1BQU1wTyxTQUFTO1lBQ1h4SztRQUNKO1FBQ0EsSUFBSSxDQUFDNkosU0FBUy9ELFFBQVEsRUFBRTtZQUNwQixJQUFJLENBQUMyTSxlQUFlZ0csYUFBYTtnQkFDN0IsSUFBSWpULGdCQUFnQmEsT0FBTyxJQUFJdVEseUJBQXlCdlEsT0FBTyxFQUFFO29CQUM3RHVTLGtCQUFrQjFTLFdBQVdHLE9BQU87b0JBQ3BDSCxXQUFXRyxPQUFPLEdBQUdtRSxPQUFPbkUsT0FBTyxHQUFHMlI7b0JBQ3RDVyxvQkFBb0JDLG9CQUFvQnBPLE9BQU9uRSxPQUFPO2dCQUMxRDtnQkFDQSxNQUFNd1MseUJBQXlCL0ssVUFBVWhNLElBQUlzRCxnQkFBZ0JwRixPQUFPd1k7Z0JBQ3BFSSxrQkFBa0IsQ0FBQyxDQUFDOVcsSUFBSW9FLFdBQVdLLFdBQVcsRUFBRXZHO2dCQUNoRDZZLHlCQUNNdkosTUFBTXBKLFdBQVdLLFdBQVcsRUFBRXZHLFFBQzlCMkMsSUFBSXVELFdBQVdLLFdBQVcsRUFBRXZHLE1BQU07Z0JBQ3hDd0ssT0FBT2pFLFdBQVcsR0FBR0wsV0FBV0ssV0FBVztnQkFDM0NvUyxvQkFDSUEscUJBQ0ssQ0FBQ25ULGdCQUFnQmUsV0FBVyxJQUN6QnFRLHlCQUF5QnJRLFdBQVcsS0FDcENxUyxvQkFBb0IsQ0FBQ0M7WUFDckM7WUFDQSxJQUFJcEcsYUFBYTtnQkFDYixNQUFNcUcseUJBQXlCaFgsSUFBSW9FLFdBQVdNLGFBQWEsRUFBRXhHO2dCQUM3RCxJQUFJLENBQUM4WSx3QkFBd0I7b0JBQ3pCblcsSUFBSXVELFdBQVdNLGFBQWEsRUFBRXhHLE1BQU15UztvQkFDcENqSSxPQUFPaEUsYUFBYSxHQUFHTixXQUFXTSxhQUFhO29CQUMvQ21TLG9CQUNJQSxxQkFDSyxDQUFDblQsZ0JBQWdCZ0IsYUFBYSxJQUMzQm9RLHlCQUF5QnBRLGFBQWEsS0FDdENzUywyQkFBMkJyRztnQkFDM0M7WUFDSjtZQUNBa0cscUJBQXFCRCxnQkFBZ0JoTSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQ3BDO1FBQzlEO1FBQ0EsT0FBT21PLG9CQUFvQm5PLFNBQVMsQ0FBQztJQUN6QztJQUNBLE1BQU11TyxzQkFBc0IsQ0FBQy9ZLE1BQU0yRyxTQUFTcUMsT0FBT0w7UUFDL0MsTUFBTXFRLHFCQUFxQmxYLElBQUlvRSxXQUFXVSxNQUFNLEVBQUU1RztRQUNsRCxNQUFNa1gsb0JBQW9CLENBQUMxUixnQkFBZ0JtQixPQUFPLElBQUlpUSx5QkFBeUJqUSxPQUFPLEtBQ2xGdEUsVUFBVXNFLFlBQ1ZULFdBQVdTLE9BQU8sS0FBS0E7UUFDM0IsSUFBSWtELFNBQVNvUCxVQUFVLElBQUlqUSxPQUFPO1lBQzlCME4scUJBQXFCSSxTQUFTLElBQU1tQixhQUFhalksTUFBTWdKO1lBQ3ZEME4sbUJBQW1CN00sU0FBU29QLFVBQVU7UUFDMUMsT0FDSztZQUNEakMsYUFBYUw7WUFDYkQscUJBQXFCO1lBQ3JCMU4sUUFDTXJHLElBQUl1RCxXQUFXVSxNQUFNLEVBQUU1RyxNQUFNZ0osU0FDN0JzRyxNQUFNcEosV0FBV1UsTUFBTSxFQUFFNUc7UUFDbkM7UUFDQSxJQUFJLENBQUNnSixRQUFRLENBQUM4RSxVQUFVa0wsb0JBQW9CaFEsU0FBU2dRLGtCQUFpQixLQUNsRSxDQUFDMUssY0FBYzNGLGVBQ2Z1TyxtQkFBbUI7WUFDbkIsTUFBTWdDLG1CQUFtQjtnQkFDckIsR0FBR3ZRLFVBQVU7Z0JBQ2IsR0FBSXVPLHFCQUFxQjdVLFVBQVVzRSxXQUFXO29CQUFFQTtnQkFBUSxJQUFJLENBQUMsQ0FBQztnQkFDOURDLFFBQVFWLFdBQVdVLE1BQU07Z0JBQ3pCNUc7WUFDSjtZQUNBa0csYUFBYTtnQkFDVCxHQUFHQSxVQUFVO2dCQUNiLEdBQUdnVCxnQkFBZ0I7WUFDdkI7WUFDQXhNLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDc007UUFDekI7SUFDSjtJQUNBLE1BQU05QixhQUFhLE9BQU9wWDtRQUN0QnNYLG9CQUFvQnRYLE1BQU07UUFDMUIsTUFBTWtDLFNBQVMsTUFBTTJILFNBQVNzTixRQUFRLENBQUNsUCxhQUFhNEIsU0FBU3NQLE9BQU8sRUFBRWpJLG1CQUFtQmxSLFFBQVFvSCxPQUFPMkMsS0FBSyxFQUFFVixTQUFTUSxTQUFTdUgsWUFBWSxFQUFFdkgsU0FBU3dILHlCQUF5QjtRQUNqTGlHLG9CQUFvQnRYO1FBQ3BCLE9BQU9rQztJQUNYO0lBQ0EsTUFBTWtYLDhCQUE4QixPQUFPaFo7UUFDdkMsTUFBTSxFQUFFd0csTUFBTSxFQUFFLEdBQUcsTUFBTXdRLFdBQVdoWDtRQUNwQyxJQUFJQSxPQUFPO1lBQ1AsS0FBSyxNQUFNSixRQUFRSSxNQUFPO2dCQUN0QixNQUFNNEksUUFBUWxILElBQUk4RSxRQUFRNUc7Z0JBQzFCZ0osUUFDTXJHLElBQUl1RCxXQUFXVSxNQUFNLEVBQUU1RyxNQUFNZ0osU0FDN0JzRyxNQUFNcEosV0FBV1UsTUFBTSxFQUFFNUc7WUFDbkM7UUFDSixPQUNLO1lBQ0RrRyxXQUFXVSxNQUFNLEdBQUdBO1FBQ3hCO1FBQ0EsT0FBT0E7SUFDWDtJQUNBLE1BQU15USwyQkFBMkIsT0FBTzFILFFBQVEwSixzQkFBc0JGLFVBQVU7UUFDNUVHLE9BQU87SUFDWCxDQUFDO1FBQ0csSUFBSyxNQUFNdFosUUFBUTJQLE9BQVE7WUFDdkIsTUFBTXZHLFFBQVF1RyxNQUFNLENBQUMzUCxLQUFLO1lBQzFCLElBQUlvSixPQUFPO2dCQUNQLE1BQU0sRUFBRUUsRUFBRSxFQUFFLEdBQUdrUCxZQUFZLEdBQUdwUDtnQkFDOUIsSUFBSUUsSUFBSTtvQkFDSixNQUFNaVEsbUJBQW1CblMsT0FBT2tCLEtBQUssQ0FBQ2pJLEdBQUcsQ0FBQ2lKLEdBQUd0SixJQUFJO29CQUNqRCxNQUFNd1osb0JBQW9CcFEsTUFBTUUsRUFBRSxJQUFJNkkscUJBQXFCL0ksTUFBTUUsRUFBRTtvQkFDbkUsSUFBSWtRLHFCQUFxQmhVLGdCQUFnQmlCLGdCQUFnQixFQUFFO3dCQUN2RDZRLG9CQUFvQjs0QkFBQ3RYO3lCQUFLLEVBQUU7b0JBQ2hDO29CQUNBLE1BQU15WixhQUFhLE1BQU10RixjQUFjL0ssT0FBT2hDLE9BQU90QixRQUFRLEVBQUVtQyxhQUFhNE8sa0NBQWtDaE4sU0FBU3dILHlCQUF5QixJQUFJLENBQUNnSSxzQkFBc0JFO29CQUMzSyxJQUFJQyxxQkFBcUJoVSxnQkFBZ0JpQixnQkFBZ0IsRUFBRTt3QkFDdkQ2USxvQkFBb0I7NEJBQUN0WDt5QkFBSztvQkFDOUI7b0JBQ0EsSUFBSXlaLFVBQVUsQ0FBQ25RLEdBQUd0SixJQUFJLENBQUMsRUFBRTt3QkFDckJtWixRQUFRRyxLQUFLLEdBQUc7d0JBQ2hCLElBQUlELHNCQUFzQjs0QkFDdEI7d0JBQ0o7b0JBQ0o7b0JBQ0EsQ0FBQ0Esd0JBQ0l2WCxDQUFBQSxJQUFJMlgsWUFBWW5RLEdBQUd0SixJQUFJLElBQ2xCdVosbUJBQ0kzRiwwQkFBMEIxTixXQUFXVSxNQUFNLEVBQUU2UyxZQUFZblEsR0FBR3RKLElBQUksSUFDaEUyQyxJQUFJdUQsV0FBV1UsTUFBTSxFQUFFMEMsR0FBR3RKLElBQUksRUFBRXlaLFVBQVUsQ0FBQ25RLEdBQUd0SixJQUFJLENBQUMsSUFDdkRzUCxNQUFNcEosV0FBV1UsTUFBTSxFQUFFMEMsR0FBR3RKLElBQUk7Z0JBQzlDO2dCQUNBLENBQUNzTyxjQUFja0ssZUFDVixNQUFNbkIseUJBQXlCbUIsWUFBWWEsc0JBQXNCRjtZQUMxRTtRQUNKO1FBQ0EsT0FBT0EsUUFBUUcsS0FBSztJQUN4QjtJQUNBLE1BQU1wUixtQkFBbUI7UUFDckIsS0FBSyxNQUFNbEksUUFBUW9ILE9BQU9xUCxPQUFPLENBQUU7WUFDL0IsTUFBTXJOLFFBQVF0SCxJQUFJdUgsU0FBU3JKO1lBQzNCb0osU0FDS0EsQ0FBQUEsTUFBTUUsRUFBRSxDQUFDMEgsSUFBSSxHQUNSNUgsTUFBTUUsRUFBRSxDQUFDMEgsSUFBSSxDQUFDZ0QsS0FBSyxDQUFDLENBQUM5SyxNQUFRLENBQUM4RixLQUFLOUYsUUFDbkMsQ0FBQzhGLEtBQUs1RixNQUFNRSxFQUFFLENBQUNKLEdBQUcsTUFDeEJnQixXQUFXbEs7UUFDbkI7UUFDQW9ILE9BQU9xUCxPQUFPLEdBQUcsSUFBSXBWO0lBQ3pCO0lBQ0EsTUFBTTJXLFlBQVksQ0FBQ2hZLE1BQU1pQixPQUFTLENBQUM0SSxTQUFTL0QsUUFBUSxJQUMvQzlGLENBQUFBLFFBQVFpQixRQUFRMEIsSUFBSXNGLGFBQWFqSSxNQUFNaUIsT0FDcEMsQ0FBQzZNLFVBQVU0TCxhQUFhdFUsZUFBYztJQUM5QyxNQUFNMkMsWUFBWSxDQUFDM0gsT0FBTzZCLGNBQWNxRixXQUFhSCxvQkFBb0IvRyxPQUFPZ0gsUUFBUTtZQUNwRixHQUFJNEMsT0FBT0QsS0FBSyxHQUNWOUIsY0FDQXRHLFlBQVlNLGdCQUNSbUQsaUJBQ0E4QixTQUFTOUcsU0FDTDtnQkFBRSxDQUFDQSxNQUFNLEVBQUU2QjtZQUFhLElBQ3hCQSxZQUFZO1FBQzlCLEdBQUdxRixVQUFVckY7SUFDYixNQUFNMFgsaUJBQWlCLENBQUMzWixPQUFTd0IsUUFBUU0sSUFBSWtJLE9BQU9ELEtBQUssR0FBRzlCLGNBQWM3QyxnQkFBZ0JwRixNQUFNNkosU0FBU3pCLGdCQUFnQixHQUFHdEcsSUFBSXNELGdCQUFnQnBGLE1BQU0sRUFBRSxJQUFJLEVBQUU7SUFDOUosTUFBTXNZLGdCQUFnQixDQUFDdFksTUFBTVosUUFBTytRLFVBQVUsQ0FBQyxDQUFDO1FBQzVDLE1BQU0vRyxRQUFRdEgsSUFBSXVILFNBQVNySjtRQUMzQixJQUFJd1ksYUFBYXBaO1FBQ2pCLElBQUlnSyxPQUFPO1lBQ1AsTUFBTWdKLGlCQUFpQmhKLE1BQU1FLEVBQUU7WUFDL0IsSUFBSThJLGdCQUFnQjtnQkFDaEIsQ0FBQ0EsZUFBZXRNLFFBQVEsSUFDcEJuRCxJQUFJc0YsYUFBYWpJLE1BQU1zUSxnQkFBZ0JsUixRQUFPZ1Q7Z0JBQ2xEb0csYUFDSS9KLGNBQWMyRCxlQUFlbEosR0FBRyxLQUFLNUosa0JBQWtCRixVQUNqRCxLQUNBQTtnQkFDVixJQUFJeVAsaUJBQWlCdUQsZUFBZWxKLEdBQUcsR0FBRztvQkFDdEM7MkJBQUlrSixlQUFlbEosR0FBRyxDQUFDaUgsT0FBTztxQkFBQyxDQUFDcUgsT0FBTyxDQUFDLENBQUNvQyxZQUFlQSxVQUFVQyxRQUFRLEdBQUdyQixXQUFXcE0sUUFBUSxDQUFDd04sVUFBVXhhLEtBQUs7Z0JBQ3BILE9BQ0ssSUFBSWdULGVBQWVwQixJQUFJLEVBQUU7b0JBQzFCLElBQUloUyxnQkFBZ0JvVCxlQUFlbEosR0FBRyxHQUFHO3dCQUNyQ2tKLGVBQWVwQixJQUFJLENBQUN3RyxPQUFPLENBQUMsQ0FBQ3NDOzRCQUN6QixJQUFJLENBQUNBLFlBQVl6QixjQUFjLElBQUksQ0FBQ3lCLFlBQVloVSxRQUFRLEVBQUU7Z0NBQ3RELElBQUlyRyxNQUFNQyxPQUFPLENBQUM4WSxhQUFhO29DQUMzQnNCLFlBQVloYSxPQUFPLEdBQUcsQ0FBQyxDQUFDMFksV0FBV25HLElBQUksQ0FBQyxDQUFDcFIsT0FBU0EsU0FBUzZZLFlBQVkxYSxLQUFLO2dDQUNoRixPQUNLO29DQUNEMGEsWUFBWWhhLE9BQU8sR0FDZjBZLGVBQWVzQixZQUFZMWEsS0FBSyxJQUFJLENBQUMsQ0FBQ29aO2dDQUM5Qzs0QkFDSjt3QkFDSjtvQkFDSixPQUNLO3dCQUNEcEcsZUFBZXBCLElBQUksQ0FBQ3dHLE9BQU8sQ0FBQyxDQUFDdUMsV0FBY0EsU0FBU2phLE9BQU8sR0FBR2lhLFNBQVMzYSxLQUFLLEtBQUtvWjtvQkFDckY7Z0JBQ0osT0FDSyxJQUFJakssWUFBWTZELGVBQWVsSixHQUFHLEdBQUc7b0JBQ3RDa0osZUFBZWxKLEdBQUcsQ0FBQzlKLEtBQUssR0FBRztnQkFDL0IsT0FDSztvQkFDRGdULGVBQWVsSixHQUFHLENBQUM5SixLQUFLLEdBQUdvWjtvQkFDM0IsSUFBSSxDQUFDcEcsZUFBZWxKLEdBQUcsQ0FBQ2hLLElBQUksRUFBRTt3QkFDMUJ3TixVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQzs0QkFDakI1TTs0QkFDQWdJLFFBQVFoSCxZQUFZaUg7d0JBQ3hCO29CQUNKO2dCQUNKO1lBQ0o7UUFDSjtRQUNDa0ksQ0FBQUEsUUFBUXNJLFdBQVcsSUFBSXRJLFFBQVE2SixXQUFXLEtBQ3ZDekIsb0JBQW9CdlksTUFBTXdZLFlBQVlySSxRQUFRNkosV0FBVyxFQUFFN0osUUFBUXNJLFdBQVcsRUFBRTtRQUNwRnRJLFFBQVE4SixjQUFjLElBQUlDLFFBQVFsYTtJQUN0QztJQUNBLE1BQU1tYSxZQUFZLENBQUNuYSxNQUFNWixRQUFPK1E7UUFDNUIsSUFBSyxNQUFNaUssWUFBWWhiLE9BQU87WUFDMUIsSUFBSSxDQUFDQSxPQUFNdUIsY0FBYyxDQUFDeVosV0FBVztnQkFDakM7WUFDSjtZQUNBLE1BQU01QixhQUFhcFosTUFBSyxDQUFDZ2IsU0FBUztZQUNsQyxNQUFNMVMsWUFBWSxDQUFDLEVBQUUxSCxLQUFLLENBQUMsRUFBRW9hLFNBQVMsQ0FBQztZQUN2QyxNQUFNaFIsUUFBUXRILElBQUl1SCxTQUFTM0I7WUFDMUJOLENBQUFBLE9BQU9rQixLQUFLLENBQUNqSSxHQUFHLENBQUNMLFNBQ2RSLFNBQVNnWixlQUNScFAsU0FBUyxDQUFDQSxNQUFNRSxFQUFFLEtBQ25CLENBQUNuSyxhQUFhcVosY0FDWjJCLFVBQVV6UyxXQUFXOFEsWUFBWXJJLFdBQ2pDbUksY0FBYzVRLFdBQVc4USxZQUFZckk7UUFDL0M7SUFDSjtJQUNBLE1BQU1rSyxXQUFXLENBQUNyYSxNQUFNWixRQUFPK1EsVUFBVSxDQUFDLENBQUM7UUFDdkMsTUFBTS9HLFFBQVF0SCxJQUFJdUgsU0FBU3JKO1FBQzNCLE1BQU1xVSxlQUFlak4sT0FBT2tCLEtBQUssQ0FBQ2pJLEdBQUcsQ0FBQ0w7UUFDdEMsTUFBTXNhLGFBQWF0WixZQUFZNUI7UUFDL0J1RCxJQUFJc0YsYUFBYWpJLE1BQU1zYTtRQUN2QixJQUFJakcsY0FBYztZQUNkM0gsVUFBVXBFLEtBQUssQ0FBQ3NFLElBQUksQ0FBQztnQkFDakI1TTtnQkFDQWdJLFFBQVFoSCxZQUFZaUg7WUFDeEI7WUFDQSxJQUFJLENBQUN6QyxnQkFBZ0JhLE9BQU8sSUFDeEJiLGdCQUFnQmUsV0FBVyxJQUMzQnFRLHlCQUF5QnZRLE9BQU8sSUFDaEN1USx5QkFBeUJyUSxXQUFXLEtBQ3BDNEosUUFBUXNJLFdBQVcsRUFBRTtnQkFDckIvTCxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztvQkFDakI1TTtvQkFDQXVHLGFBQWF3SixlQUFlM0ssZ0JBQWdCNkM7b0JBQzVDNUIsU0FBUzJSLFVBQVVoWSxNQUFNc2E7Z0JBQzdCO1lBQ0o7UUFDSixPQUNLO1lBQ0RsUixTQUFTLENBQUNBLE1BQU1FLEVBQUUsSUFBSSxDQUFDaEssa0JBQWtCZ2IsY0FDbkNILFVBQVVuYSxNQUFNc2EsWUFBWW5LLFdBQzVCbUksY0FBY3RZLE1BQU1zYSxZQUFZbks7UUFDMUM7UUFDQXFDLFVBQVV4UyxNQUFNb0gsV0FBV3NGLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQUUsR0FBRzFHLFVBQVU7UUFBQztRQUNoRXdHLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCNU0sTUFBTWdLLE9BQU9ELEtBQUssR0FBRy9KLE9BQU82QjtZQUM1Qm1HLFFBQVFoSCxZQUFZaUg7UUFDeEI7SUFDSjtJQUNBLE1BQU14RSxXQUFXLE9BQU83RDtRQUNwQm9LLE9BQU9ELEtBQUssR0FBRztRQUNmLE1BQU1sSyxTQUFTRCxNQUFNQyxNQUFNO1FBQzNCLElBQUlHLE9BQU9ILE9BQU9HLElBQUk7UUFDdEIsSUFBSXVhLHNCQUFzQjtRQUMxQixNQUFNblIsUUFBUXRILElBQUl1SCxTQUFTcko7UUFDM0IsTUFBTXdhLDZCQUE2QixDQUFDaEM7WUFDaEMrQixzQkFDSUUsT0FBT3ZYLEtBQUssQ0FBQ3NWLGVBQ1JyWixhQUFhcVosZUFBZXRWLE1BQU1zVixXQUFXdkssT0FBTyxPQUNyREgsVUFBVTBLLFlBQVkxVyxJQUFJbUcsYUFBYWpJLE1BQU13WTtRQUN6RDtRQUNBLE1BQU1rQyw2QkFBNkIvSSxtQkFBbUI5SCxTQUFTK0gsSUFBSTtRQUNuRSxNQUFNK0ksNEJBQTRCaEosbUJBQW1COUgsU0FBUzZKLGNBQWM7UUFDNUUsSUFBSXRLLE9BQU87WUFDUCxJQUFJSjtZQUNKLElBQUlyQztZQUNKLE1BQU02UixhQUFhM1ksT0FBT1gsSUFBSSxHQUN4QjRSLGNBQWMxSCxNQUFNRSxFQUFFLElBQ3RCM0osY0FBY0M7WUFDcEIsTUFBTTZTLGNBQWM3UyxNQUFNVixJQUFJLEtBQUtpRSxPQUFPQyxJQUFJLElBQUl4RCxNQUFNVixJQUFJLEtBQUtpRSxPQUFPRSxTQUFTO1lBQ2pGLE1BQU11WCx1QkFBdUIsQ0FBRXJJLGNBQWNuSixNQUFNRSxFQUFFLEtBQ2pELENBQUNPLFNBQVNzTixRQUFRLElBQ2xCLENBQUNyVixJQUFJb0UsV0FBV1UsTUFBTSxFQUFFNUcsU0FDeEIsQ0FBQ29KLE1BQU1FLEVBQUUsQ0FBQ3VSLElBQUksSUFDZHJILGVBQWVmLGFBQWEzUSxJQUFJb0UsV0FBV00sYUFBYSxFQUFFeEcsT0FBT2tHLFdBQVd1TixXQUFXLEVBQUVrSCwyQkFBMkJEO1lBQ3hILE1BQU1JLFVBQVV0SSxVQUFVeFMsTUFBTW9ILFFBQVFxTDtZQUN4QzlQLElBQUlzRixhQUFhakksTUFBTXdZO1lBQ3ZCLElBQUkvRixhQUFhO2dCQUNickosTUFBTUUsRUFBRSxDQUFDOUYsTUFBTSxJQUFJNEYsTUFBTUUsRUFBRSxDQUFDOUYsTUFBTSxDQUFDNUQ7Z0JBQ25DOFcsc0JBQXNCQSxtQkFBbUI7WUFDN0MsT0FDSyxJQUFJdE4sTUFBTUUsRUFBRSxDQUFDN0YsUUFBUSxFQUFFO2dCQUN4QjJGLE1BQU1FLEVBQUUsQ0FBQzdGLFFBQVEsQ0FBQzdEO1lBQ3RCO1lBQ0EsTUFBTStJLGFBQWE0UCxvQkFBb0J2WSxNQUFNd1ksWUFBWS9GO1lBQ3pELE1BQU1pRyxlQUFlLENBQUNwSyxjQUFjM0YsZUFBZW1TO1lBQ25ELENBQUNySSxlQUNHL0YsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCNU07Z0JBQ0FkLE1BQU1VLE1BQU1WLElBQUk7Z0JBQ2hCOEksUUFBUWhILFlBQVlpSDtZQUN4QjtZQUNKLElBQUkyUyxzQkFBc0I7Z0JBQ3RCLElBQUlwVixnQkFBZ0JtQixPQUFPLElBQUlpUSx5QkFBeUJqUSxPQUFPLEVBQUU7b0JBQzdELElBQUlrRCxTQUFTK0gsSUFBSSxLQUFLLFVBQVU7d0JBQzVCLElBQUlhLGFBQWE7NEJBQ2J6TDt3QkFDSjtvQkFDSixPQUNLLElBQUksQ0FBQ3lMLGFBQWE7d0JBQ25Cekw7b0JBQ0o7Z0JBQ0o7Z0JBQ0EsT0FBUTBSLGdCQUNKaE0sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7b0JBQUU1TTtvQkFBTSxHQUFJOGEsVUFBVSxDQUFDLElBQUluUyxVQUFVO2dCQUFFO1lBQ3BFO1lBQ0EsQ0FBQzhKLGVBQWVxSSxXQUFXcE8sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQUUsR0FBRzFHLFVBQVU7WUFBQztZQUNoRSxJQUFJMkQsU0FBU3NOLFFBQVEsRUFBRTtnQkFDbkIsTUFBTSxFQUFFdlEsTUFBTSxFQUFFLEdBQUcsTUFBTXdRLFdBQVc7b0JBQUNwWDtpQkFBSztnQkFDMUN3YSwyQkFBMkJoQztnQkFDM0IsSUFBSStCLHFCQUFxQjtvQkFDckIsTUFBTVEsNEJBQTRCaEksa0JBQWtCN00sV0FBV1UsTUFBTSxFQUFFeUMsU0FBU3JKO29CQUNoRixNQUFNZ2Isb0JBQW9Cakksa0JBQWtCbk0sUUFBUXlDLFNBQVMwUiwwQkFBMEIvYSxJQUFJLElBQUlBO29CQUMvRmdKLFFBQVFnUyxrQkFBa0JoUyxLQUFLO29CQUMvQmhKLE9BQU9nYixrQkFBa0JoYixJQUFJO29CQUM3QjJHLFVBQVUySCxjQUFjMUg7Z0JBQzVCO1lBQ0osT0FDSztnQkFDRDBRLG9CQUFvQjtvQkFBQ3RYO2lCQUFLLEVBQUU7Z0JBQzVCZ0osUUFBUSxDQUFDLE1BQU1tTCxjQUFjL0ssT0FBT2hDLE9BQU90QixRQUFRLEVBQUVtQyxhQUFhNE8sa0NBQWtDaE4sU0FBU3dILHlCQUF5QixFQUFFLENBQUNyUixLQUFLO2dCQUM5SXNYLG9CQUFvQjtvQkFBQ3RYO2lCQUFLO2dCQUMxQndhLDJCQUEyQmhDO2dCQUMzQixJQUFJK0IscUJBQXFCO29CQUNyQixJQUFJdlIsT0FBTzt3QkFDUHJDLFVBQVU7b0JBQ2QsT0FDSyxJQUFJbkIsZ0JBQWdCbUIsT0FBTyxJQUM1QmlRLHlCQUF5QmpRLE9BQU8sRUFBRTt3QkFDbENBLFVBQVUsTUFBTTBRLHlCQUF5QmhPLFNBQVM7b0JBQ3REO2dCQUNKO1lBQ0o7WUFDQSxJQUFJa1IscUJBQXFCO2dCQUNyQm5SLE1BQU1FLEVBQUUsQ0FBQ3VSLElBQUksSUFDVFgsUUFBUTlRLE1BQU1FLEVBQUUsQ0FBQ3VSLElBQUk7Z0JBQ3pCOUIsb0JBQW9CL1ksTUFBTTJHLFNBQVNxQyxPQUFPTDtZQUM5QztRQUNKO0lBQ0o7SUFDQSxNQUFNc1MsY0FBYyxDQUFDL1IsS0FBSzNIO1FBQ3RCLElBQUlPLElBQUlvRSxXQUFXVSxNQUFNLEVBQUVyRixRQUFRMkgsSUFBSUssS0FBSyxFQUFFO1lBQzFDTCxJQUFJSyxLQUFLO1lBQ1QsT0FBTztRQUNYO1FBQ0E7SUFDSjtJQUNBLE1BQU0yUSxVQUFVLE9BQU9sYSxNQUFNbVEsVUFBVSxDQUFDLENBQUM7UUFDckMsSUFBSXhKO1FBQ0osSUFBSXVQO1FBQ0osTUFBTWdGLGFBQWE5TixzQkFBc0JwTjtRQUN6QyxJQUFJNkosU0FBU3NOLFFBQVEsRUFBRTtZQUNuQixNQUFNdlEsU0FBUyxNQUFNd1MsNEJBQTRCelgsWUFBWTNCLFFBQVFBLE9BQU9rYjtZQUM1RXZVLFVBQVUySCxjQUFjMUg7WUFDeEJzUCxtQkFBbUJsVyxPQUNiLENBQUNrYixXQUFXL08sSUFBSSxDQUFDLENBQUNuTSxPQUFTOEIsSUFBSThFLFFBQVE1RyxTQUN2QzJHO1FBQ1YsT0FDSyxJQUFJM0csTUFBTTtZQUNYa1csbUJBQW1CLENBQUMsTUFBTWlGLFFBQVF2WCxHQUFHLENBQUNzWCxXQUFXelQsR0FBRyxDQUFDLE9BQU9DO2dCQUN4RCxNQUFNMEIsUUFBUXRILElBQUl1SCxTQUFTM0I7Z0JBQzNCLE9BQU8sTUFBTTJQLHlCQUF5QmpPLFNBQVNBLE1BQU1FLEVBQUUsR0FBRztvQkFBRSxDQUFDNUIsVUFBVSxFQUFFMEI7Z0JBQU0sSUFBSUE7WUFDdkYsR0FBRSxFQUFHNEssS0FBSyxDQUFDdFM7WUFDWCxDQUFFLEVBQUN3VSxvQkFBb0IsQ0FBQ2hRLFdBQVdTLE9BQU8sS0FBS0s7UUFDbkQsT0FDSztZQUNEa1AsbUJBQW1CdlAsVUFBVSxNQUFNMFEseUJBQXlCaE87UUFDaEU7UUFDQXFELFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCLEdBQUksQ0FBQzFGLFNBQVNsSCxTQUNULENBQUN3RixnQkFBZ0JtQixPQUFPLElBQUlpUSx5QkFBeUJqUSxPQUFPLEtBQ3pEQSxZQUFZVCxXQUFXUyxPQUFPLEdBQ2hDLENBQUMsSUFDRDtnQkFBRTNHO1lBQUssQ0FBQztZQUNkLEdBQUk2SixTQUFTc04sUUFBUSxJQUFJLENBQUNuWCxPQUFPO2dCQUFFMkc7WUFBUSxJQUFJLENBQUMsQ0FBQztZQUNqREMsUUFBUVYsV0FBV1UsTUFBTTtRQUM3QjtRQUNBdUosUUFBUWlMLFdBQVcsSUFDZixDQUFDbEYsb0JBQ0R0RCxzQkFBc0J2SixTQUFTNFIsYUFBYWpiLE9BQU9rYixhQUFhOVQsT0FBTzJDLEtBQUs7UUFDaEYsT0FBT21NO0lBQ1g7SUFDQSxNQUFNd0QsWUFBWSxDQUFDd0I7UUFDZixNQUFNbFQsU0FBUztZQUNYLEdBQUlnQyxPQUFPRCxLQUFLLEdBQUc5QixjQUFjN0MsY0FBYztRQUNuRDtRQUNBLE9BQU96RCxZQUFZdVosY0FDYmxULFNBQ0FkLFNBQVNnVSxjQUNMcFosSUFBSWtHLFFBQVFrVCxjQUNaQSxXQUFXelQsR0FBRyxDQUFDLENBQUN6SCxPQUFTOEIsSUFBSWtHLFFBQVFoSTtJQUNuRDtJQUNBLE1BQU1xYixnQkFBZ0IsQ0FBQ3JiLE1BQU0rRSxZQUFlO1lBQ3hDOEQsU0FBUyxDQUFDLENBQUMvRyxJQUFJLENBQUNpRCxhQUFhbUIsVUFBUyxFQUFHVSxNQUFNLEVBQUU1RztZQUNqRHFHLFNBQVMsQ0FBQyxDQUFDdkUsSUFBSSxDQUFDaUQsYUFBYW1CLFVBQVMsRUFBR0ssV0FBVyxFQUFFdkc7WUFDdERnSixPQUFPbEgsSUFBSSxDQUFDaUQsYUFBYW1CLFVBQVMsRUFBR1UsTUFBTSxFQUFFNUc7WUFDN0MwRyxjQUFjLENBQUMsQ0FBQzVFLElBQUlvRSxXQUFXTyxnQkFBZ0IsRUFBRXpHO1lBQ2pEK0ksV0FBVyxDQUFDLENBQUNqSCxJQUFJLENBQUNpRCxhQUFhbUIsVUFBUyxFQUFHTSxhQUFhLEVBQUV4RztRQUM5RDtJQUNBLE1BQU1zYixjQUFjLENBQUN0YjtRQUNqQkEsUUFDSW9OLHNCQUFzQnBOLE1BQU13WCxPQUFPLENBQUMsQ0FBQytELFlBQWNqTSxNQUFNcEosV0FBV1UsTUFBTSxFQUFFMlU7UUFDaEY3TyxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQmhHLFFBQVE1RyxPQUFPa0csV0FBV1UsTUFBTSxHQUFHLENBQUM7UUFDeEM7SUFDSjtJQUNBLE1BQU1rRyxXQUFXLENBQUM5TSxNQUFNZ0osT0FBT21IO1FBQzNCLE1BQU1qSCxNQUFNLENBQUNwSCxJQUFJdUgsU0FBU3JKLE1BQU07WUFBRXNKLElBQUksQ0FBQztRQUFFLEdBQUdBLEVBQUUsSUFBSSxDQUFDLEdBQUdKLEdBQUc7UUFDekQsTUFBTXNTLGVBQWUxWixJQUFJb0UsV0FBV1UsTUFBTSxFQUFFNUcsU0FBUyxDQUFDO1FBQ3RELHVFQUF1RTtRQUN2RSxNQUFNLEVBQUVrSixLQUFLdVMsVUFBVSxFQUFFL1IsT0FBTyxFQUFFeEssSUFBSSxFQUFFLEdBQUd3YyxpQkFBaUIsR0FBR0Y7UUFDL0Q3WSxJQUFJdUQsV0FBV1UsTUFBTSxFQUFFNUcsTUFBTTtZQUN6QixHQUFHMGIsZUFBZTtZQUNsQixHQUFHMVMsS0FBSztZQUNSRTtRQUNKO1FBQ0F3RCxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQjVNO1lBQ0E0RyxRQUFRVixXQUFXVSxNQUFNO1lBQ3pCRCxTQUFTO1FBQ2I7UUFDQXdKLFdBQVdBLFFBQVFpTCxXQUFXLElBQUlsUyxPQUFPQSxJQUFJSyxLQUFLLElBQUlMLElBQUlLLEtBQUs7SUFDbkU7SUFDQSxNQUFNaEMsUUFBUSxDQUFDdkgsTUFBTWlDLGVBQWlCdU0sV0FBV3hPLFFBQzNDME0sVUFBVUMsS0FBSyxDQUFDYSxTQUFTLENBQUM7WUFDeEJaLE1BQU0sQ0FBQytPLFVBQVkzYixLQUFLK0gsVUFBVWxHLFdBQVdJLGVBQWUwWjtRQUNoRSxLQUNFNVQsVUFBVS9ILE1BQU1pQyxjQUFjO0lBQ3BDLE1BQU00RSxhQUFhLENBQUNuQyxRQUFVZ0ksVUFBVUMsS0FBSyxDQUFDYSxTQUFTLENBQUM7WUFDcERaLE1BQU0sQ0FBQzdIO2dCQUNILElBQUlzTyxzQkFBc0IzTyxNQUFNMUUsSUFBSSxFQUFFK0UsVUFBVS9FLElBQUksRUFBRTBFLE1BQU1xQixLQUFLLEtBQzdEb04sc0JBQXNCcE8sV0FBV0wsTUFBTUssU0FBUyxJQUFJUyxpQkFBaUJvVyxlQUFlbFgsTUFBTW1YLFlBQVksR0FBRztvQkFDekduWCxNQUFNcUMsUUFBUSxDQUFDO3dCQUNYaUIsUUFBUTs0QkFBRSxHQUFHQyxXQUFXO3dCQUFDO3dCQUN6QixHQUFHL0IsVUFBVTt3QkFDYixHQUFHbkIsU0FBUztvQkFDaEI7Z0JBQ0o7WUFDSjtRQUNKLEdBQUcySSxXQUFXO0lBQ2QsTUFBTUYsWUFBWSxDQUFDOUk7UUFDZnNGLE9BQU9ELEtBQUssR0FBRztRQUNmNk0sMkJBQTJCO1lBQ3ZCLEdBQUdBLHdCQUF3QjtZQUMzQixHQUFHbFMsTUFBTUssU0FBUztRQUN0QjtRQUNBLE9BQU84QixXQUFXO1lBQ2QsR0FBR25DLEtBQUs7WUFDUkssV0FBVzZSO1FBQ2Y7SUFDSjtJQUNBLE1BQU0xTSxhQUFhLENBQUNsSyxNQUFNbVEsVUFBVSxDQUFDLENBQUM7UUFDbEMsS0FBSyxNQUFNekksYUFBYTFILE9BQU9vTixzQkFBc0JwTixRQUFRb0gsT0FBTzJDLEtBQUssQ0FBRTtZQUN2RTNDLE9BQU8yQyxLQUFLLENBQUMrUixNQUFNLENBQUNwVTtZQUNwQk4sT0FBT2tCLEtBQUssQ0FBQ3dULE1BQU0sQ0FBQ3BVO1lBQ3BCLElBQUksQ0FBQ3lJLFFBQVE0TCxTQUFTLEVBQUU7Z0JBQ3BCek0sTUFBTWpHLFNBQVMzQjtnQkFDZjRILE1BQU1ySCxhQUFhUDtZQUN2QjtZQUNBLENBQUN5SSxRQUFRNkwsU0FBUyxJQUFJMU0sTUFBTXBKLFdBQVdVLE1BQU0sRUFBRWM7WUFDL0MsQ0FBQ3lJLFFBQVE4TCxTQUFTLElBQUkzTSxNQUFNcEosV0FBV0ssV0FBVyxFQUFFbUI7WUFDcEQsQ0FBQ3lJLFFBQVErTCxXQUFXLElBQUk1TSxNQUFNcEosV0FBV00sYUFBYSxFQUFFa0I7WUFDeEQsQ0FBQ3lJLFFBQVFnTSxnQkFBZ0IsSUFDckI3TSxNQUFNcEosV0FBV08sZ0JBQWdCLEVBQUVpQjtZQUN2QyxDQUFDbUMsU0FBU3pCLGdCQUFnQixJQUN0QixDQUFDK0gsUUFBUWlNLGdCQUFnQixJQUN6QjlNLE1BQU1sSyxnQkFBZ0JzQztRQUM5QjtRQUNBZ0YsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakI1RSxRQUFRaEgsWUFBWWlIO1FBQ3hCO1FBQ0F5RSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQixHQUFHMUcsVUFBVTtZQUNiLEdBQUksQ0FBQ2lLLFFBQVE4TCxTQUFTLEdBQUcsQ0FBQyxJQUFJO2dCQUFFNVYsU0FBUzJSO1lBQVksQ0FBQztRQUMxRDtRQUNBLENBQUM3SCxRQUFRa00sV0FBVyxJQUFJclY7SUFDNUI7SUFDQSxNQUFNbUQsb0JBQW9CLENBQUMsRUFBRXJFLFFBQVEsRUFBRTlGLElBQUksRUFBRztRQUMxQyxJQUFJLFVBQVc4RixhQUFha0UsT0FBT0QsS0FBSyxJQUNwQyxDQUFDLENBQUNqRSxZQUNGc0IsT0FBT3RCLFFBQVEsQ0FBQ3pGLEdBQUcsQ0FBQ0wsT0FBTztZQUMzQjhGLFdBQVdzQixPQUFPdEIsUUFBUSxDQUFDMEIsR0FBRyxDQUFDeEgsUUFBUW9ILE9BQU90QixRQUFRLENBQUNnVyxNQUFNLENBQUM5YjtRQUNsRTtJQUNKO0lBQ0EsTUFBTXlJLFdBQVcsQ0FBQ3pJLE1BQU1tUSxVQUFVLENBQUMsQ0FBQztRQUNoQyxJQUFJL0csUUFBUXRILElBQUl1SCxTQUFTcko7UUFDekIsTUFBTXNjLG9CQUFvQmphLFVBQVU4TixRQUFRckssUUFBUSxLQUFLekQsVUFBVXdILFNBQVMvRCxRQUFRO1FBQ3BGbkQsSUFBSTBHLFNBQVNySixNQUFNO1lBQ2YsR0FBSW9KLFNBQVMsQ0FBQyxDQUFDO1lBQ2ZFLElBQUk7Z0JBQ0EsR0FBSUYsU0FBU0EsTUFBTUUsRUFBRSxHQUFHRixNQUFNRSxFQUFFLEdBQUc7b0JBQUVKLEtBQUs7d0JBQUVsSjtvQkFBSztnQkFBRSxDQUFDO2dCQUNwREE7Z0JBQ0ErSixPQUFPO2dCQUNQLEdBQUdvRyxPQUFPO1lBQ2Q7UUFDSjtRQUNBL0ksT0FBTzJDLEtBQUssQ0FBQ3ZDLEdBQUcsQ0FBQ3hIO1FBQ2pCLElBQUlvSixPQUFPO1lBQ1BlLGtCQUFrQjtnQkFDZHJFLFVBQVV6RCxVQUFVOE4sUUFBUXJLLFFBQVEsSUFDOUJxSyxRQUFRckssUUFBUSxHQUNoQitELFNBQVMvRCxRQUFRO2dCQUN2QjlGO1lBQ0o7UUFDSixPQUNLO1lBQ0RtWSxvQkFBb0JuWSxNQUFNLE1BQU1tUSxRQUFRL1EsS0FBSztRQUNqRDtRQUNBLE9BQU87WUFDSCxHQUFJa2Qsb0JBQ0U7Z0JBQUV4VyxVQUFVcUssUUFBUXJLLFFBQVEsSUFBSStELFNBQVMvRCxRQUFRO1lBQUMsSUFDbEQsQ0FBQyxDQUFDO1lBQ1IsR0FBSStELFNBQVMwUyxXQUFXLEdBQ2xCO2dCQUNFcFksVUFBVSxDQUFDLENBQUNnTSxRQUFRaE0sUUFBUTtnQkFDNUJKLEtBQUt5TixhQUFhckIsUUFBUXBNLEdBQUc7Z0JBQzdCRCxLQUFLME4sYUFBYXJCLFFBQVFyTSxHQUFHO2dCQUM3QkcsV0FBV3VOLGFBQWFyQixRQUFRbE0sU0FBUztnQkFDekNELFdBQVd3TixhQUFhckIsUUFBUW5NLFNBQVM7Z0JBQ3pDRSxTQUFTc04sYUFBYXJCLFFBQVFqTSxPQUFPO1lBQ3pDLElBQ0UsQ0FBQyxDQUFDO1lBQ1JsRTtZQUNBeUQ7WUFDQUQsUUFBUUM7WUFDUnlGLEtBQUssQ0FBQ0E7Z0JBQ0YsSUFBSUEsS0FBSztvQkFDTFQsU0FBU3pJLE1BQU1tUTtvQkFDZi9HLFFBQVF0SCxJQUFJdUgsU0FBU3JKO29CQUNyQixNQUFNd2MsV0FBVzdhLFlBQVl1SCxJQUFJOUosS0FBSyxJQUNoQzhKLElBQUl1VCxnQkFBZ0IsR0FDaEJ2VCxJQUFJdVQsZ0JBQWdCLENBQUMsd0JBQXdCLENBQUMsRUFBRSxJQUFJdlQsTUFDcERBLE1BQ0pBO29CQUNOLE1BQU13VCxrQkFBa0IzTixrQkFBa0J5TjtvQkFDMUMsTUFBTXhMLE9BQU81SCxNQUFNRSxFQUFFLENBQUMwSCxJQUFJLElBQUksRUFBRTtvQkFDaEMsSUFBSTBMLGtCQUNFMUwsS0FBS3FCLElBQUksQ0FBQyxDQUFDakMsU0FBV0EsV0FBV29NLFlBQ2pDQSxhQUFhcFQsTUFBTUUsRUFBRSxDQUFDSixHQUFHLEVBQUU7d0JBQzdCO29CQUNKO29CQUNBdkcsSUFBSTBHLFNBQVNySixNQUFNO3dCQUNmc0osSUFBSTs0QkFDQSxHQUFHRixNQUFNRSxFQUFFOzRCQUNYLEdBQUlvVCxrQkFDRTtnQ0FDRTFMLE1BQU07dUNBQ0NBLEtBQUt2UCxNQUFNLENBQUN1TjtvQ0FDZndOO3VDQUNJL2MsTUFBTUMsT0FBTyxDQUFDb0MsSUFBSXNELGdCQUFnQnBGLFNBQVM7d0NBQUMsQ0FBQztxQ0FBRSxHQUFHLEVBQUU7aUNBQzNEO2dDQUNEa0osS0FBSztvQ0FBRWhLLE1BQU1zZCxTQUFTdGQsSUFBSTtvQ0FBRWM7Z0NBQUs7NEJBQ3JDLElBQ0U7Z0NBQUVrSixLQUFLc1Q7NEJBQVMsQ0FBQzt3QkFDM0I7b0JBQ0o7b0JBQ0FyRSxvQkFBb0JuWSxNQUFNLE9BQU82QixXQUFXMmE7Z0JBQ2hELE9BQ0s7b0JBQ0RwVCxRQUFRdEgsSUFBSXVILFNBQVNySixNQUFNLENBQUM7b0JBQzVCLElBQUlvSixNQUFNRSxFQUFFLEVBQUU7d0JBQ1ZGLE1BQU1FLEVBQUUsQ0FBQ1MsS0FBSyxHQUFHO29CQUNyQjtvQkFDQ0YsQ0FBQUEsU0FBU3pCLGdCQUFnQixJQUFJK0gsUUFBUS9ILGdCQUFnQixLQUNsRCxDQUFFakksQ0FBQUEsbUJBQW1CaUgsT0FBT2tCLEtBQUssRUFBRXRJLFNBQVNnSyxPQUFPQyxNQUFNLEtBQ3pEN0MsT0FBT3FQLE9BQU8sQ0FBQ2pQLEdBQUcsQ0FBQ3hIO2dCQUMzQjtZQUNKO1FBQ0o7SUFDSjtJQUNBLE1BQU0yYyxjQUFjLElBQU05UyxTQUFTdU0sZ0JBQWdCLElBQy9DeEQsc0JBQXNCdkosU0FBUzRSLGFBQWE3VCxPQUFPMkMsS0FBSztJQUM1RCxNQUFNNlMsZUFBZSxDQUFDOVc7UUFDbEIsSUFBSXpELFVBQVV5RCxXQUFXO1lBQ3JCNEcsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQUU5RztZQUFTO1lBQ2hDOE0sc0JBQXNCdkosU0FBUyxDQUFDSCxLQUFLbEo7Z0JBQ2pDLE1BQU04UyxlQUFlaFIsSUFBSXVILFNBQVNySjtnQkFDbEMsSUFBSThTLGNBQWM7b0JBQ2Q1SixJQUFJcEQsUUFBUSxHQUFHZ04sYUFBYXhKLEVBQUUsQ0FBQ3hELFFBQVEsSUFBSUE7b0JBQzNDLElBQUlyRyxNQUFNQyxPQUFPLENBQUNvVCxhQUFheEosRUFBRSxDQUFDMEgsSUFBSSxHQUFHO3dCQUNyQzhCLGFBQWF4SixFQUFFLENBQUMwSCxJQUFJLENBQUN3RyxPQUFPLENBQUMsQ0FBQ2pEOzRCQUMxQkEsU0FBU3pPLFFBQVEsR0FBR2dOLGFBQWF4SixFQUFFLENBQUN4RCxRQUFRLElBQUlBO3dCQUNwRDtvQkFDSjtnQkFDSjtZQUNKLEdBQUcsR0FBRztRQUNWO0lBQ0o7SUFDQSxNQUFNMkYsZUFBZSxDQUFDb1IsU0FBU0MsWUFBYyxPQUFPQztZQUNoRCxJQUFJQyxlQUFlbmI7WUFDbkIsSUFBSWtiLEdBQUc7Z0JBQ0hBLEVBQUVFLGNBQWMsSUFBSUYsRUFBRUUsY0FBYztnQkFDcENGLEVBQUVHLE9BQU8sSUFDTEgsRUFBRUcsT0FBTztZQUNqQjtZQUNBLElBQUlyRixjQUFjN1csWUFBWWlIO1lBQzlCeUUsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCNEosY0FBYztZQUNsQjtZQUNBLElBQUkzTSxTQUFTc04sUUFBUSxFQUFFO2dCQUNuQixNQUFNLEVBQUV2USxNQUFNLEVBQUVvQixNQUFNLEVBQUUsR0FBRyxNQUFNb1A7Z0JBQ2pDbFIsV0FBV1UsTUFBTSxHQUFHQTtnQkFDcEJpUixjQUFjN1A7WUFDbEIsT0FDSztnQkFDRCxNQUFNcVAseUJBQXlCaE87WUFDbkM7WUFDQSxJQUFJakMsT0FBT3RCLFFBQVEsQ0FBQ3FYLElBQUksRUFBRTtnQkFDdEIsS0FBSyxNQUFNbmQsUUFBUW9ILE9BQU90QixRQUFRLENBQUU7b0JBQ2hDbkQsSUFBSWtWLGFBQWE3WCxNQUFNNkI7Z0JBQzNCO1lBQ0o7WUFDQXlOLE1BQU1wSixXQUFXVSxNQUFNLEVBQUU7WUFDekIsSUFBSTBILGNBQWNwSSxXQUFXVSxNQUFNLEdBQUc7Z0JBQ2xDOEYsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7b0JBQ2pCaEcsUUFBUSxDQUFDO2dCQUNiO2dCQUNBLElBQUk7b0JBQ0EsTUFBTWlXLFFBQVFoRixhQUFha0Y7Z0JBQy9CLEVBQ0EsT0FBTy9ULE9BQU87b0JBQ1ZnVSxlQUFlaFU7Z0JBQ25CO1lBQ0osT0FDSztnQkFDRCxJQUFJOFQsV0FBVztvQkFDWCxNQUFNQSxVQUFVO3dCQUFFLEdBQUc1VyxXQUFXVSxNQUFNO29CQUFDLEdBQUdtVztnQkFDOUM7Z0JBQ0FKO2dCQUNBMUYsV0FBVzBGO1lBQ2Y7WUFDQWpRLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjZHLGFBQWE7Z0JBQ2IrQyxjQUFjO2dCQUNkM0osb0JBQW9CeUIsY0FBY3BJLFdBQVdVLE1BQU0sS0FBSyxDQUFDb1c7Z0JBQ3pEMUcsYUFBYXBRLFdBQVdvUSxXQUFXLEdBQUc7Z0JBQ3RDMVAsUUFBUVYsV0FBV1UsTUFBTTtZQUM3QjtZQUNBLElBQUlvVyxjQUFjO2dCQUNkLE1BQU1BO1lBQ1Y7UUFDSjtJQUNBLE1BQU1JLGFBQWEsQ0FBQ3BkLE1BQU1tUSxVQUFVLENBQUMsQ0FBQztRQUNsQyxJQUFJck8sSUFBSXVILFNBQVNySixPQUFPO1lBQ3BCLElBQUkyQixZQUFZd08sUUFBUWxPLFlBQVksR0FBRztnQkFDbkNvWSxTQUFTcmEsTUFBTWdCLFlBQVljLElBQUlzRCxnQkFBZ0JwRjtZQUNuRCxPQUNLO2dCQUNEcWEsU0FBU3JhLE1BQU1tUSxRQUFRbE8sWUFBWTtnQkFDbkNVLElBQUl5QyxnQkFBZ0JwRixNQUFNZ0IsWUFBWW1QLFFBQVFsTyxZQUFZO1lBQzlEO1lBQ0EsSUFBSSxDQUFDa08sUUFBUStMLFdBQVcsRUFBRTtnQkFDdEI1TSxNQUFNcEosV0FBV00sYUFBYSxFQUFFeEc7WUFDcEM7WUFDQSxJQUFJLENBQUNtUSxRQUFROEwsU0FBUyxFQUFFO2dCQUNwQjNNLE1BQU1wSixXQUFXSyxXQUFXLEVBQUV2RztnQkFDOUJrRyxXQUFXRyxPQUFPLEdBQUc4SixRQUFRbE8sWUFBWSxHQUNuQytWLFVBQVVoWSxNQUFNZ0IsWUFBWWMsSUFBSXNELGdCQUFnQnBGLFVBQ2hEZ1k7WUFDVjtZQUNBLElBQUksQ0FBQzdILFFBQVE2TCxTQUFTLEVBQUU7Z0JBQ3BCMU0sTUFBTXBKLFdBQVdVLE1BQU0sRUFBRTVHO2dCQUN6QndGLGdCQUFnQm1CLE9BQU8sSUFBSUs7WUFDL0I7WUFDQTBGLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUFFLEdBQUcxRyxVQUFVO1lBQUM7UUFDekM7SUFDSjtJQUNBLE1BQU1tWCxTQUFTLENBQUNoVyxZQUFZaVcsbUJBQW1CLENBQUMsQ0FBQztRQUM3QyxNQUFNQyxnQkFBZ0JsVyxhQUFhckcsWUFBWXFHLGNBQWNqQztRQUM3RCxNQUFNb1kscUJBQXFCeGMsWUFBWXVjO1FBQ3ZDLE1BQU1FLHFCQUFxQm5QLGNBQWNqSDtRQUN6QyxNQUFNVyxTQUFTeVYscUJBQXFCclksaUJBQWlCb1k7UUFDckQsSUFBSSxDQUFDRixpQkFBaUJJLGlCQUFpQixFQUFFO1lBQ3JDdFksaUJBQWlCbVk7UUFDckI7UUFDQSxJQUFJLENBQUNELGlCQUFpQkssVUFBVSxFQUFFO1lBQzlCLElBQUlMLGlCQUFpQk0sZUFBZSxFQUFFO2dCQUNsQyxNQUFNQyxnQkFBZ0IsSUFBSXhjLElBQUk7dUJBQ3ZCK0YsT0FBTzJDLEtBQUs7dUJBQ1oxRSxPQUFPb0YsSUFBSSxDQUFDc0YsZUFBZTNLLGdCQUFnQjZDO2lCQUNqRDtnQkFDRCxLQUFLLE1BQU1QLGFBQWFqSSxNQUFNOFgsSUFBSSxDQUFDc0csZUFBZ0I7b0JBQy9DL2IsSUFBSW9FLFdBQVdLLFdBQVcsRUFBRW1CLGFBQ3RCL0UsSUFBSXFGLFFBQVFOLFdBQVc1RixJQUFJbUcsYUFBYVAsY0FDeEMyUyxTQUFTM1MsV0FBVzVGLElBQUlrRyxRQUFRTjtnQkFDMUM7WUFDSixPQUNLO2dCQUNELElBQUk5RyxTQUFTZSxZQUFZMEYsYUFBYTtvQkFDbEMsS0FBSyxNQUFNckgsUUFBUW9ILE9BQU8yQyxLQUFLLENBQUU7d0JBQzdCLE1BQU1YLFFBQVF0SCxJQUFJdUgsU0FBU3JKO3dCQUMzQixJQUFJb0osU0FBU0EsTUFBTUUsRUFBRSxFQUFFOzRCQUNuQixNQUFNOEksaUJBQWlCM1MsTUFBTUMsT0FBTyxDQUFDMEosTUFBTUUsRUFBRSxDQUFDMEgsSUFBSSxJQUM1QzVILE1BQU1FLEVBQUUsQ0FBQzBILElBQUksQ0FBQyxFQUFFLEdBQ2hCNUgsTUFBTUUsRUFBRSxDQUFDSixHQUFHOzRCQUNsQixJQUFJdUYsY0FBYzJELGlCQUFpQjtnQ0FDL0IsTUFBTTBMLE9BQU8xTCxlQUFlMkwsT0FBTyxDQUFDO2dDQUNwQyxJQUFJRCxNQUFNO29DQUNOQSxLQUFLRSxLQUFLO29DQUNWO2dDQUNKOzRCQUNKO3dCQUNKO29CQUNKO2dCQUNKO2dCQUNBLEtBQUssTUFBTXRXLGFBQWFOLE9BQU8yQyxLQUFLLENBQUU7b0JBQ2xDc1EsU0FBUzNTLFdBQVc1RixJQUFJa0csUUFBUU47Z0JBQ3BDO1lBQ0o7WUFDQU8sY0FBY2pILFlBQVlnSDtZQUMxQjBFLFVBQVVwRSxLQUFLLENBQUNzRSxJQUFJLENBQUM7Z0JBQ2pCNUUsUUFBUTtvQkFBRSxHQUFHQSxNQUFNO2dCQUFDO1lBQ3hCO1lBQ0EwRSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFDakI1RSxRQUFRO29CQUFFLEdBQUdBLE1BQU07Z0JBQUM7WUFDeEI7UUFDSjtRQUNBWixTQUFTO1lBQ0wyQyxPQUFPdVQsaUJBQWlCTSxlQUFlLEdBQUd4VyxPQUFPMkMsS0FBSyxHQUFHLElBQUkxSTtZQUM3RG9WLFNBQVMsSUFBSXBWO1lBQ2JpSCxPQUFPLElBQUlqSDtZQUNYeUUsVUFBVSxJQUFJekU7WUFDZGtHLE9BQU8sSUFBSWxHO1lBQ1hzRyxVQUFVO1lBQ1Y0QixPQUFPO1FBQ1g7UUFDQVMsT0FBT0QsS0FBSyxHQUNSLENBQUN2RSxnQkFBZ0JtQixPQUFPLElBQ3BCLENBQUMsQ0FBQzJXLGlCQUFpQmpCLFdBQVcsSUFDOUIsQ0FBQyxDQUFDaUIsaUJBQWlCTSxlQUFlO1FBQzFDNVQsT0FBT3pDLEtBQUssR0FBRyxDQUFDLENBQUNzQyxTQUFTekIsZ0JBQWdCO1FBQzFDc0UsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakIwSixhQUFhZ0gsaUJBQWlCVyxlQUFlLEdBQ3ZDL1gsV0FBV29RLFdBQVcsR0FDdEI7WUFDTmpRLFNBQVNvWCxxQkFDSCxRQUNBSCxpQkFBaUJyQixTQUFTLEdBQ3RCL1YsV0FBV0csT0FBTyxHQUNsQixDQUFDLENBQUVpWCxDQUFBQSxpQkFBaUJJLGlCQUFpQixJQUNuQyxDQUFDNVAsVUFBVXpHLFlBQVlqQyxlQUFjO1lBQ2pEcU8sYUFBYTZKLGlCQUFpQlksZUFBZSxHQUN2Q2hZLFdBQVd1TixXQUFXLEdBQ3RCO1lBQ05sTixhQUFha1gscUJBQ1AsQ0FBQyxJQUNESCxpQkFBaUJNLGVBQWUsR0FDNUJOLGlCQUFpQkksaUJBQWlCLElBQUl6VixjQUNsQzhILGVBQWUzSyxnQkFBZ0I2QyxlQUMvQi9CLFdBQVdLLFdBQVcsR0FDMUIrVyxpQkFBaUJJLGlCQUFpQixJQUFJclcsYUFDbEMwSSxlQUFlM0ssZ0JBQWdCaUMsY0FDL0JpVyxpQkFBaUJyQixTQUFTLEdBQ3RCL1YsV0FBV0ssV0FBVyxHQUN0QixDQUFDO1lBQ25CQyxlQUFlOFcsaUJBQWlCcEIsV0FBVyxHQUNyQ2hXLFdBQVdNLGFBQWEsR0FDeEIsQ0FBQztZQUNQSSxRQUFRMFcsaUJBQWlCYSxVQUFVLEdBQUdqWSxXQUFXVSxNQUFNLEdBQUcsQ0FBQztZQUMzRGlHLG9CQUFvQnlRLGlCQUFpQmMsc0JBQXNCLEdBQ3JEbFksV0FBVzJHLGtCQUFrQixHQUM3QjtZQUNOMkosY0FBYztRQUNsQjtJQUNKO0lBQ0EsTUFBTXdILFFBQVEsQ0FBQzNXLFlBQVlpVyxtQkFBcUJELE9BQU83TyxXQUFXbkgsY0FDNURBLFdBQVdZLGVBQ1haLFlBQVlpVztJQUNsQixNQUFNZSxXQUFXLENBQUNyZSxNQUFNbVEsVUFBVSxDQUFDLENBQUM7UUFDaEMsTUFBTS9HLFFBQVF0SCxJQUFJdUgsU0FBU3JKO1FBQzNCLE1BQU1vUyxpQkFBaUJoSixTQUFTQSxNQUFNRSxFQUFFO1FBQ3hDLElBQUk4SSxnQkFBZ0I7WUFDaEIsTUFBTW9LLFdBQVdwSyxlQUFlcEIsSUFBSSxHQUM5Qm9CLGVBQWVwQixJQUFJLENBQUMsRUFBRSxHQUN0Qm9CLGVBQWVsSixHQUFHO1lBQ3hCLElBQUlzVCxTQUFTalQsS0FBSyxFQUFFO2dCQUNoQmlULFNBQVNqVCxLQUFLO2dCQUNkNEcsUUFBUW1PLFlBQVksSUFDaEI5UCxXQUFXZ08sU0FBU2hULE1BQU0sS0FDMUJnVCxTQUFTaFQsTUFBTTtZQUN2QjtRQUNKO0lBQ0o7SUFDQSxNQUFNb1MsZ0JBQWdCLENBQUMxQztRQUNuQmhULGFBQWE7WUFDVCxHQUFHQSxVQUFVO1lBQ2IsR0FBR2dULGdCQUFnQjtRQUN2QjtJQUNKO0lBQ0EsTUFBTXFGLHNCQUFzQixJQUFNL1AsV0FBVzNFLFNBQVMxRSxhQUFhLEtBQy9EMEUsU0FBUzFFLGFBQWEsR0FBR3FaLElBQUksQ0FBQyxDQUFDeFc7WUFDM0JnVyxNQUFNaFcsUUFBUTZCLFNBQVM0VSxZQUFZO1lBQ25DL1IsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCdEcsV0FBVztZQUNmO1FBQ0o7SUFDSixNQUFNVCxVQUFVO1FBQ1piLFNBQVM7WUFDTHlEO1lBQ0F5QjtZQUNBbVI7WUFDQTVQO1lBQ0FxQjtZQUNBakc7WUFDQXVRO1lBQ0FyUDtZQUNBaVE7WUFDQWhSO1lBQ0F5UTtZQUNBdE47WUFDQStOO1lBQ0F5QjtZQUNBMEQ7WUFDQWtCO1lBQ0FyVztZQUNBMFU7WUFDQWxRO1lBQ0FsSDtZQUNBLElBQUk2RCxXQUFVO2dCQUNWLE9BQU9BO1lBQ1g7WUFDQSxJQUFJcEIsZUFBYztnQkFDZCxPQUFPQTtZQUNYO1lBQ0EsSUFBSStCLFVBQVM7Z0JBQ1QsT0FBT0E7WUFDWDtZQUNBLElBQUlBLFFBQU81SyxNQUFPO2dCQUNkNEssU0FBUzVLO1lBQ2I7WUFDQSxJQUFJZ0csa0JBQWlCO2dCQUNqQixPQUFPQTtZQUNYO1lBQ0EsSUFBSWdDLFVBQVM7Z0JBQ1QsT0FBT0E7WUFDWDtZQUNBLElBQUlBLFFBQU9oSSxNQUFPO2dCQUNkZ0ksU0FBU2hJO1lBQ2I7WUFDQSxJQUFJOEcsY0FBYTtnQkFDYixPQUFPQTtZQUNYO1lBQ0EsSUFBSTJELFlBQVc7Z0JBQ1gsT0FBT0E7WUFDWDtZQUNBLElBQUlBLFVBQVN6SyxNQUFPO2dCQUNoQnlLLFdBQVc7b0JBQ1AsR0FBR0EsUUFBUTtvQkFDWCxHQUFHekssS0FBSztnQkFDWjtZQUNKO1FBQ0o7UUFDQW9PO1FBQ0EwTTtRQUNBelI7UUFDQWdEO1FBQ0FsRTtRQUNBOFM7UUFDQVg7UUFDQXNFO1FBQ0FaO1FBQ0E5QjtRQUNBcFI7UUFDQTRDO1FBQ0F1UjtRQUNBaEQ7SUFDSjtJQUNBLE9BQU87UUFDSCxHQUFHeFYsT0FBTztRQUNWNlksYUFBYTdZO0lBQ2pCO0FBQ0o7QUFFQSxJQUFJOFksYUFBYTtJQUNiLE1BQU1DLElBQUksT0FBT0MsZ0JBQWdCLGNBQWN4ZixLQUFLeWYsR0FBRyxLQUFLRCxZQUFZQyxHQUFHLEtBQUs7SUFDaEYsT0FBTyx1Q0FBdUNwYyxPQUFPLENBQUMsU0FBUyxDQUFDcWM7UUFDNUQsTUFBTUMsSUFBSSxDQUFDQyxLQUFLQyxNQUFNLEtBQUssS0FBS04sQ0FBQUEsSUFBSyxLQUFLO1FBQzFDLE9BQU8sQ0FBQ0csS0FBSyxNQUFNQyxJQUFJLElBQUssTUFBTyxHQUFFLEVBQUdHLFFBQVEsQ0FBQztJQUNyRDtBQUNKO0FBRUEsSUFBSUMsb0JBQW9CLENBQUNwZixNQUFNNEMsT0FBT3VOLFVBQVUsQ0FBQyxDQUFDLEdBQUtBLFFBQVFpTCxXQUFXLElBQUl6WixZQUFZd08sUUFBUWlMLFdBQVcsSUFDdkdqTCxRQUFRa1AsU0FBUyxJQUNmLENBQUMsRUFBRXJmLEtBQUssQ0FBQyxFQUFFMkIsWUFBWXdPLFFBQVFtUCxVQUFVLElBQUkxYyxRQUFRdU4sUUFBUW1QLFVBQVUsQ0FBQyxDQUFDLENBQUMsR0FDNUU7QUFFTixJQUFJQyxXQUFXLENBQUN0ZSxNQUFNN0IsU0FBVTtXQUN6QjZCO1dBQ0FtTSxzQkFBc0JoTztLQUM1QjtBQUVELElBQUlvZ0IsaUJBQWlCLENBQUNwZ0IsU0FBVUssTUFBTUMsT0FBTyxDQUFDTixVQUFTQSxPQUFNcUksR0FBRyxDQUFDLElBQU01RixhQUFhQTtBQUVwRixTQUFTNGQsT0FBT3hlLElBQUksRUFBRTJCLEtBQUssRUFBRXhELE1BQUs7SUFDOUIsT0FBTztXQUNBNkIsS0FBS21PLEtBQUssQ0FBQyxHQUFHeE07V0FDZHdLLHNCQUFzQmhPO1dBQ3RCNkIsS0FBS21PLEtBQUssQ0FBQ3hNO0tBQ2pCO0FBQ0w7QUFFQSxJQUFJOGMsY0FBYyxDQUFDemUsTUFBTXNXLE1BQU1vSTtJQUMzQixJQUFJLENBQUNsZ0IsTUFBTUMsT0FBTyxDQUFDdUIsT0FBTztRQUN0QixPQUFPLEVBQUU7SUFDYjtJQUNBLElBQUlVLFlBQVlWLElBQUksQ0FBQzBlLEdBQUcsR0FBRztRQUN2QjFlLElBQUksQ0FBQzBlLEdBQUcsR0FBRzlkO0lBQ2Y7SUFDQVosS0FBSzJlLE1BQU0sQ0FBQ0QsSUFBSSxHQUFHMWUsS0FBSzJlLE1BQU0sQ0FBQ3JJLE1BQU0sRUFBRSxDQUFDLEVBQUU7SUFDMUMsT0FBT3RXO0FBQ1g7QUFFQSxJQUFJNGUsWUFBWSxDQUFDNWUsTUFBTTdCLFNBQVU7V0FDMUJnTyxzQkFBc0JoTztXQUN0QmdPLHNCQUFzQm5NO0tBQzVCO0FBRUQsU0FBUzZlLGdCQUFnQjdlLElBQUksRUFBRThlLE9BQU87SUFDbEMsSUFBSUMsSUFBSTtJQUNSLE1BQU1DLE9BQU87V0FBSWhmO0tBQUs7SUFDdEIsS0FBSyxNQUFNMkIsU0FBU21kLFFBQVM7UUFDekJFLEtBQUtMLE1BQU0sQ0FBQ2hkLFFBQVFvZCxHQUFHO1FBQ3ZCQTtJQUNKO0lBQ0EsT0FBT3hlLFFBQVF5ZSxNQUFNbmQsTUFBTSxHQUFHbWQsT0FBTyxFQUFFO0FBQzNDO0FBQ0EsSUFBSUMsZ0JBQWdCLENBQUNqZixNQUFNMkIsUUFBVWpCLFlBQVlpQixTQUMzQyxFQUFFLEdBQ0ZrZCxnQkFBZ0I3ZSxNQUFNbU0sc0JBQXNCeEssT0FBT3VkLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxJQUFJQztBQUU1RSxJQUFJQyxjQUFjLENBQUNyZixNQUFNc2YsUUFBUUM7SUFDN0IsQ0FBQ3ZmLElBQUksQ0FBQ3NmLE9BQU8sRUFBRXRmLElBQUksQ0FBQ3VmLE9BQU8sQ0FBQyxHQUFHO1FBQUN2ZixJQUFJLENBQUN1ZixPQUFPO1FBQUV2ZixJQUFJLENBQUNzZixPQUFPO0tBQUM7QUFDL0Q7QUFFQSxJQUFJRSxXQUFXLENBQUM1SSxhQUFhalYsT0FBT3hEO0lBQ2hDeVksV0FBVyxDQUFDalYsTUFBTSxHQUFHeEQ7SUFDckIsT0FBT3lZO0FBQ1g7QUFFQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBb0NDLEdBQ0QsU0FBUzZJLGNBQWNoYyxLQUFLO0lBQ3hCLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxFQUFFUyxVQUFVYSxRQUFRYixPQUFPLEVBQUVoRixJQUFJLEVBQUUyZ0IsVUFBVSxJQUFJLEVBQUV2WSxnQkFBZ0IsRUFBRU0sS0FBSyxFQUFHLEdBQUdoRTtJQUN0RixNQUFNLENBQUNpTCxRQUFRaVIsVUFBVSxHQUFHN2hCLDJDQUF1QixDQUFDaUcsUUFBUTJVLGNBQWMsQ0FBQzNaO0lBQzNFLE1BQU02Z0IsTUFBTTloQix5Q0FBcUIsQ0FBQ2lHLFFBQVEyVSxjQUFjLENBQUMzWixNQUFNeUgsR0FBRyxDQUFDa1g7SUFDbkUsTUFBTW1DLFlBQVkvaEIseUNBQXFCLENBQUM0UTtJQUN4QyxNQUFNb1IsUUFBUWhpQix5Q0FBcUIsQ0FBQ2lCO0lBQ3BDLE1BQU1naEIsWUFBWWppQix5Q0FBcUIsQ0FBQztJQUN4Q2dpQixNQUFNamEsT0FBTyxHQUFHOUc7SUFDaEI4Z0IsVUFBVWhhLE9BQU8sR0FBRzZJO0lBQ3BCM0ssUUFBUW9DLE1BQU0sQ0FBQ2tCLEtBQUssQ0FBQ2QsR0FBRyxDQUFDeEg7SUFDekIwSSxTQUNJMUQsUUFBUXlELFFBQVEsQ0FBQ3pJLE1BQU0wSTtJQUMzQjNKLDRDQUF3QixDQUFDLElBQU1pRyxRQUFRMEgsU0FBUyxDQUFDcEUsS0FBSyxDQUFDa0YsU0FBUyxDQUFDO1lBQzdEWixNQUFNLENBQUMsRUFBRTVFLE1BQU0sRUFBRWhJLE1BQU1paEIsY0FBYyxFQUFHO2dCQUNwQyxJQUFJQSxtQkFBbUJGLE1BQU1qYSxPQUFPLElBQUksQ0FBQ21hLGdCQUFnQjtvQkFDckQsTUFBTXBKLGNBQWMvVixJQUFJa0csUUFBUStZLE1BQU1qYSxPQUFPO29CQUM3QyxJQUFJckgsTUFBTUMsT0FBTyxDQUFDbVksY0FBYzt3QkFDNUIrSSxVQUFVL0k7d0JBQ1ZnSixJQUFJL1osT0FBTyxHQUFHK1EsWUFBWXBRLEdBQUcsQ0FBQ2tYO29CQUNsQztnQkFDSjtZQUNKO1FBQ0osR0FBR2pSLFdBQVcsRUFBRTtRQUFDMUk7S0FBUTtJQUN6QixNQUFNa2MsZUFBZW5pQiw4Q0FBMEIsQ0FBQyxDQUFDb2lCO1FBQzdDSCxVQUFVbGEsT0FBTyxHQUFHO1FBQ3BCOUIsUUFBUXlTLGNBQWMsQ0FBQ3pYLE1BQU1taEI7SUFDakMsR0FBRztRQUFDbmM7UUFBU2hGO0tBQUs7SUFDbEIsTUFBTWlNLFNBQVMsQ0FBQzdNLFFBQU8rUTtRQUNuQixNQUFNaVIsY0FBY2hVLHNCQUFzQnBNLFlBQVk1QjtRQUN0RCxNQUFNK2hCLDBCQUEwQjVCLFNBQVN2YSxRQUFRMlUsY0FBYyxDQUFDM1osT0FBT29oQjtRQUN2RXBjLFFBQVFvQyxNQUFNLENBQUNtQyxLQUFLLEdBQUc2VixrQkFBa0JwZixNQUFNbWhCLHdCQUF3QnJlLE1BQU0sR0FBRyxHQUFHcU47UUFDbkYwUSxJQUFJL1osT0FBTyxHQUFHeVksU0FBU3NCLElBQUkvWixPQUFPLEVBQUVzYSxZQUFZM1osR0FBRyxDQUFDa1g7UUFDcER1QyxhQUFhQztRQUNiUCxVQUFVTztRQUNWbmMsUUFBUXlTLGNBQWMsQ0FBQ3pYLE1BQU1taEIseUJBQXlCNUIsVUFBVTtZQUM1RHpILE1BQU0wSCxlQUFlcGdCO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNaWlCLFVBQVUsQ0FBQ2ppQixRQUFPK1E7UUFDcEIsTUFBTW1SLGVBQWVsVSxzQkFBc0JwTSxZQUFZNUI7UUFDdkQsTUFBTStoQiwwQkFBMEJ0QixVQUFVN2EsUUFBUTJVLGNBQWMsQ0FBQzNaLE9BQU9zaEI7UUFDeEV0YyxRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxHQUFHNlYsa0JBQWtCcGYsTUFBTSxHQUFHbVE7UUFDbEQwUSxJQUFJL1osT0FBTyxHQUFHK1ksVUFBVWdCLElBQUkvWixPQUFPLEVBQUV3YSxhQUFhN1osR0FBRyxDQUFDa1g7UUFDdER1QyxhQUFhQztRQUNiUCxVQUFVTztRQUNWbmMsUUFBUXlTLGNBQWMsQ0FBQ3pYLE1BQU1taEIseUJBQXlCdEIsV0FBVztZQUM3RC9ILE1BQU0wSCxlQUFlcGdCO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNbWlCLFNBQVMsQ0FBQzNlO1FBQ1osTUFBTXVlLDBCQUEwQmpCLGNBQWNsYixRQUFRMlUsY0FBYyxDQUFDM1osT0FBTzRDO1FBQzVFaWUsSUFBSS9aLE9BQU8sR0FBR29aLGNBQWNXLElBQUkvWixPQUFPLEVBQUVsRTtRQUN6Q3NlLGFBQWFDO1FBQ2JQLFVBQVVPO1FBQ1YsQ0FBQzFoQixNQUFNQyxPQUFPLENBQUNvQyxJQUFJa0QsUUFBUXFFLE9BQU8sRUFBRXJKLFVBQ2hDMkMsSUFBSXFDLFFBQVFxRSxPQUFPLEVBQUVySixNQUFNNkI7UUFDL0JtRCxRQUFReVMsY0FBYyxDQUFDelgsTUFBTW1oQix5QkFBeUJqQixlQUFlO1lBQ2pFcEksTUFBTWxWO1FBQ1Y7SUFDSjtJQUNBLE1BQU00ZSxXQUFXLENBQUM1ZSxPQUFPeEQsUUFBTytRO1FBQzVCLE1BQU1zUixjQUFjclUsc0JBQXNCcE0sWUFBWTVCO1FBQ3RELE1BQU0raEIsMEJBQTBCMUIsT0FBT3phLFFBQVEyVSxjQUFjLENBQUMzWixPQUFPNEMsT0FBTzZlO1FBQzVFemMsUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssR0FBRzZWLGtCQUFrQnBmLE1BQU00QyxPQUFPdU47UUFDdEQwUSxJQUFJL1osT0FBTyxHQUFHMlksT0FBT29CLElBQUkvWixPQUFPLEVBQUVsRSxPQUFPNmUsWUFBWWhhLEdBQUcsQ0FBQ2tYO1FBQ3pEdUMsYUFBYUM7UUFDYlAsVUFBVU87UUFDVm5jLFFBQVF5UyxjQUFjLENBQUN6WCxNQUFNbWhCLHlCQUF5QjFCLFFBQVE7WUFDMUQzSCxNQUFNbFY7WUFDTm1WLE1BQU15SCxlQUFlcGdCO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNc2lCLE9BQU8sQ0FBQ25CLFFBQVFDO1FBQ2xCLE1BQU1XLDBCQUEwQm5jLFFBQVEyVSxjQUFjLENBQUMzWjtRQUN2RHNnQixZQUFZYSx5QkFBeUJaLFFBQVFDO1FBQzdDRixZQUFZTyxJQUFJL1osT0FBTyxFQUFFeVosUUFBUUM7UUFDakNVLGFBQWFDO1FBQ2JQLFVBQVVPO1FBQ1ZuYyxRQUFReVMsY0FBYyxDQUFDelgsTUFBTW1oQix5QkFBeUJiLGFBQWE7WUFDL0R4SSxNQUFNeUk7WUFDTnhJLE1BQU15STtRQUNWLEdBQUc7SUFDUDtJQUNBLE1BQU1tQixPQUFPLENBQUNwSyxNQUFNb0k7UUFDaEIsTUFBTXdCLDBCQUEwQm5jLFFBQVEyVSxjQUFjLENBQUMzWjtRQUN2RDBmLFlBQVl5Qix5QkFBeUI1SixNQUFNb0k7UUFDM0NELFlBQVltQixJQUFJL1osT0FBTyxFQUFFeVEsTUFBTW9JO1FBQy9CdUIsYUFBYUM7UUFDYlAsVUFBVU87UUFDVm5jLFFBQVF5UyxjQUFjLENBQUN6WCxNQUFNbWhCLHlCQUF5QnpCLGFBQWE7WUFDL0Q1SCxNQUFNUDtZQUNOUSxNQUFNNEg7UUFDVixHQUFHO0lBQ1A7SUFDQSxNQUFNaUMsU0FBUyxDQUFDaGYsT0FBT3hEO1FBQ25CLE1BQU0wSSxjQUFjOUcsWUFBWTVCO1FBQ2hDLE1BQU0raEIsMEJBQTBCVixTQUFTemIsUUFBUTJVLGNBQWMsQ0FBQzNaLE9BQU80QyxPQUFPa0Y7UUFDOUUrWSxJQUFJL1osT0FBTyxHQUFHO2VBQUlxYTtTQUF3QixDQUFDMVosR0FBRyxDQUFDLENBQUNvYSxNQUFNN0IsSUFBTSxDQUFDNkIsUUFBUTdCLE1BQU1wZCxRQUFRK2IsZUFBZWtDLElBQUkvWixPQUFPLENBQUNrWixFQUFFO1FBQ2hIa0IsYUFBYUM7UUFDYlAsVUFBVTtlQUFJTztTQUF3QjtRQUN0Q25jLFFBQVF5UyxjQUFjLENBQUN6WCxNQUFNbWhCLHlCQUF5QlYsVUFBVTtZQUM1RDNJLE1BQU1sVjtZQUNObVYsTUFBTWpRO1FBQ1YsR0FBRyxNQUFNO0lBQ2I7SUFDQSxNQUFNcEYsVUFBVSxDQUFDdEQ7UUFDYixNQUFNK2hCLDBCQUEwQi9ULHNCQUFzQnBNLFlBQVk1QjtRQUNsRXloQixJQUFJL1osT0FBTyxHQUFHcWEsd0JBQXdCMVosR0FBRyxDQUFDa1g7UUFDMUN1QyxhQUFhO2VBQUlDO1NBQXdCO1FBQ3pDUCxVQUFVO2VBQUlPO1NBQXdCO1FBQ3RDbmMsUUFBUXlTLGNBQWMsQ0FBQ3pYLE1BQU07ZUFBSW1oQjtTQUF3QixFQUFFLENBQUNsZ0IsT0FBU0EsTUFBTSxDQUFDLEdBQUcsTUFBTTtJQUN6RjtJQUNBbEMsNENBQXdCLENBQUM7UUFDckJpRyxRQUFRZ0YsTUFBTSxDQUFDQyxNQUFNLEdBQUc7UUFDeEJ1SSxVQUFVeFMsTUFBTWdGLFFBQVFvQyxNQUFNLEtBQzFCcEMsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDekIsR0FBRzVILFFBQVFrQixVQUFVO1FBQ3pCO1FBQ0osSUFBSThhLFVBQVVsYSxPQUFPLElBQ2hCLEVBQUM2SyxtQkFBbUIzTSxRQUFRNkUsUUFBUSxDQUFDK0gsSUFBSSxFQUFFQyxVQUFVLElBQ2xEN00sUUFBUWtCLFVBQVUsQ0FBQ3VOLFdBQVcsS0FDbEMsQ0FBQzlCLG1CQUFtQjNNLFFBQVE2RSxRQUFRLENBQUM2SixjQUFjLEVBQUU3QixVQUFVLEVBQUU7WUFDakUsSUFBSTdNLFFBQVE2RSxRQUFRLENBQUNzTixRQUFRLEVBQUU7Z0JBQzNCblMsUUFBUW9TLFVBQVUsQ0FBQztvQkFBQ3BYO2lCQUFLLEVBQUV3ZSxJQUFJLENBQUMsQ0FBQ3RjO29CQUM3QixNQUFNOEcsUUFBUWxILElBQUlJLE9BQU8wRSxNQUFNLEVBQUU1RztvQkFDakMsTUFBTThoQixnQkFBZ0JoZ0IsSUFBSWtELFFBQVFrQixVQUFVLENBQUNVLE1BQU0sRUFBRTVHO29CQUNyRCxJQUFJOGhCLGdCQUNFLENBQUU5WSxTQUFTOFksY0FBYzVpQixJQUFJLElBQzFCOEosU0FDSThZLENBQUFBLGNBQWM1aUIsSUFBSSxLQUFLOEosTUFBTTlKLElBQUksSUFDOUI0aUIsY0FBY3BZLE9BQU8sS0FBS1YsTUFBTVUsT0FBTyxJQUNqRFYsU0FBU0EsTUFBTTlKLElBQUksRUFBRTt3QkFDdkI4SixRQUNNckcsSUFBSXFDLFFBQVFrQixVQUFVLENBQUNVLE1BQU0sRUFBRTVHLE1BQU1nSixTQUNyQ3NHLE1BQU10SyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUU1Rzt3QkFDdkNnRixRQUFRMEgsU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQzs0QkFDekJoRyxRQUFRNUIsUUFBUWtCLFVBQVUsQ0FBQ1UsTUFBTTt3QkFDckM7b0JBQ0o7Z0JBQ0o7WUFDSixPQUNLO2dCQUNELE1BQU13QyxRQUFRdEgsSUFBSWtELFFBQVFxRSxPQUFPLEVBQUVySjtnQkFDbkMsSUFBSW9KLFNBQ0FBLE1BQU1FLEVBQUUsSUFDUixDQUFFcUksQ0FBQUEsbUJBQW1CM00sUUFBUTZFLFFBQVEsQ0FBQzZKLGNBQWMsRUFBRTdCLFVBQVUsSUFDNURGLG1CQUFtQjNNLFFBQVE2RSxRQUFRLENBQUMrSCxJQUFJLEVBQUVDLFVBQVUsR0FBRztvQkFDM0RzQyxjQUFjL0ssT0FBT3BFLFFBQVFvQyxNQUFNLENBQUN0QixRQUFRLEVBQUVkLFFBQVFpRCxXQUFXLEVBQUVqRCxRQUFRNkUsUUFBUSxDQUFDdUgsWUFBWSxLQUFLN04sZ0JBQWdCSyxHQUFHLEVBQUVvQixRQUFRNkUsUUFBUSxDQUFDd0gseUJBQXlCLEVBQUUsTUFBTW1OLElBQUksQ0FBQyxDQUFDeFYsUUFBVSxDQUFDc0YsY0FBY3RGLFVBQ3ZNaEUsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7NEJBQ3pCaEcsUUFBUWdOLDBCQUEwQjVPLFFBQVFrQixVQUFVLENBQUNVLE1BQU0sRUFBRW9DLE9BQU9oSjt3QkFDeEU7Z0JBQ1I7WUFDSjtRQUNKO1FBQ0FnRixRQUFRMEgsU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUN6QjVNO1lBQ0FnSSxRQUFRaEgsWUFBWWdFLFFBQVFpRCxXQUFXO1FBQzNDO1FBQ0FqRCxRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxJQUNoQnFKLHNCQUFzQjVOLFFBQVFxRSxPQUFPLEVBQUUsQ0FBQ0gsS0FBSzNIO1lBQ3pDLElBQUl5RCxRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxJQUNwQmhJLElBQUlvUixVQUFVLENBQUMzTixRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxLQUNuQ0wsSUFBSUssS0FBSyxFQUFFO2dCQUNYTCxJQUFJSyxLQUFLO2dCQUNULE9BQU87WUFDWDtZQUNBO1FBQ0o7UUFDSnZFLFFBQVFvQyxNQUFNLENBQUNtQyxLQUFLLEdBQUc7UUFDdkJ2RSxRQUFRZ0MsU0FBUztRQUNqQmdhLFVBQVVsYSxPQUFPLEdBQUc7SUFDeEIsR0FBRztRQUFDNkk7UUFBUTNQO1FBQU1nRjtLQUFRO0lBQzFCakcsNENBQXdCLENBQUM7UUFDckIsQ0FBQytDLElBQUlrRCxRQUFRaUQsV0FBVyxFQUFFakksU0FBU2dGLFFBQVF5UyxjQUFjLENBQUN6WDtRQUMxRCxPQUFPO1lBQ0gsTUFBTThKLGdCQUFnQixDQUFDOUosTUFBTVo7Z0JBQ3pCLE1BQU1nSyxRQUFRdEgsSUFBSWtELFFBQVFxRSxPQUFPLEVBQUVySjtnQkFDbkMsSUFBSW9KLFNBQVNBLE1BQU1FLEVBQUUsRUFBRTtvQkFDbkJGLE1BQU1FLEVBQUUsQ0FBQ1MsS0FBSyxHQUFHM0s7Z0JBQ3JCO1lBQ0o7WUFDQTRGLFFBQVE2RSxRQUFRLENBQUN6QixnQkFBZ0IsSUFBSUEsbUJBQy9CcEQsUUFBUWtGLFVBQVUsQ0FBQ2xLLFFBQ25COEosY0FBYzlKLE1BQU07UUFDOUI7SUFDSixHQUFHO1FBQUNBO1FBQU1nRjtRQUFTMmI7UUFBU3ZZO0tBQWlCO0lBQzdDLE9BQU87UUFDSHNaLE1BQU0zaUIsOENBQTBCLENBQUMyaUIsTUFBTTtZQUFDUjtZQUFjbGhCO1lBQU1nRjtTQUFRO1FBQ3BFMmMsTUFBTTVpQiw4Q0FBMEIsQ0FBQzRpQixNQUFNO1lBQUNUO1lBQWNsaEI7WUFBTWdGO1NBQVE7UUFDcEVxYyxTQUFTdGlCLDhDQUEwQixDQUFDc2lCLFNBQVM7WUFBQ0g7WUFBY2xoQjtZQUFNZ0Y7U0FBUTtRQUMxRWlILFFBQVFsTiw4Q0FBMEIsQ0FBQ2tOLFFBQVE7WUFBQ2lWO1lBQWNsaEI7WUFBTWdGO1NBQVE7UUFDeEV1YyxRQUFReGlCLDhDQUEwQixDQUFDd2lCLFFBQVE7WUFBQ0w7WUFBY2xoQjtZQUFNZ0Y7U0FBUTtRQUN4RXlhLFFBQVExZ0IsOENBQTBCLENBQUN5aUIsVUFBVTtZQUFDTjtZQUFjbGhCO1lBQU1nRjtTQUFRO1FBQzFFNGMsUUFBUTdpQiw4Q0FBMEIsQ0FBQzZpQixRQUFRO1lBQUNWO1lBQWNsaEI7WUFBTWdGO1NBQVE7UUFDeEV0QyxTQUFTM0QsOENBQTBCLENBQUMyRCxTQUFTO1lBQUN3ZTtZQUFjbGhCO1lBQU1nRjtTQUFRO1FBQzFFMkssUUFBUTVRLDBDQUFzQixDQUFDLElBQU00USxPQUFPbEksR0FBRyxDQUFDLENBQUMyQixPQUFPeEcsUUFBVztvQkFDL0QsR0FBR3dHLEtBQUs7b0JBQ1IsQ0FBQ3VYLFFBQVEsRUFBRUUsSUFBSS9aLE9BQU8sQ0FBQ2xFLE1BQU0sSUFBSStiO2dCQUNyQyxLQUFLO1lBQUNoUDtZQUFRZ1I7U0FBUTtJQUMxQjtBQUNKO0FBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0E0QkMsR0FDRCxTQUFTb0IsUUFBUXJkLFFBQVEsQ0FBQyxDQUFDO0lBQ3ZCLE1BQU1zZCxlQUFlampCLHlDQUFxQixDQUFDOEM7SUFDM0MsTUFBTW9nQixVQUFVbGpCLHlDQUFxQixDQUFDOEM7SUFDdEMsTUFBTSxDQUFDa0QsV0FBV2lCLGdCQUFnQixHQUFHakgsMkNBQXVCLENBQUM7UUFDekRzSCxTQUFTO1FBQ1RLLGNBQWM7UUFDZEosV0FBV2tJLFdBQVc5SixNQUFNUyxhQUFhO1FBQ3pDc08sYUFBYTtRQUNiK0MsY0FBYztRQUNkM0osb0JBQW9CO1FBQ3BCbEcsU0FBUztRQUNUMlAsYUFBYTtRQUNiL1AsYUFBYSxDQUFDO1FBQ2RDLGVBQWUsQ0FBQztRQUNoQkMsa0JBQWtCLENBQUM7UUFDbkJHLFFBQVFsQyxNQUFNa0MsTUFBTSxJQUFJLENBQUM7UUFDekJkLFVBQVVwQixNQUFNb0IsUUFBUSxJQUFJO1FBQzVCeVEsU0FBUztRQUNUcFIsZUFBZXFKLFdBQVc5SixNQUFNUyxhQUFhLElBQ3ZDdEQsWUFDQTZDLE1BQU1TLGFBQWE7SUFDN0I7SUFDQSxJQUFJLENBQUM2YyxhQUFhbGIsT0FBTyxFQUFFO1FBQ3ZCa2IsYUFBYWxiLE9BQU8sR0FBRztZQUNuQixHQUFJcEMsTUFBTWdhLFdBQVcsR0FBR2hhLE1BQU1nYSxXQUFXLEdBQUdySSxrQkFBa0IzUixNQUFNO1lBQ3BFSztRQUNKO1FBQ0EsSUFBSUwsTUFBTWdhLFdBQVcsSUFDakJoYSxNQUFNUyxhQUFhLElBQ25CLENBQUNxSixXQUFXOUosTUFBTVMsYUFBYSxHQUFHO1lBQ2xDVCxNQUFNZ2EsV0FBVyxDQUFDVixLQUFLLENBQUN0WixNQUFNUyxhQUFhLEVBQUVULE1BQU0rWixZQUFZO1FBQ25FO0lBQ0o7SUFDQSxNQUFNelosVUFBVWdkLGFBQWFsYixPQUFPLENBQUM5QixPQUFPO0lBQzVDQSxRQUFRNkUsUUFBUSxHQUFHbkY7SUFDbkJlLDBCQUEwQjtRQUN0QixNQUFNeWMsTUFBTWxkLFFBQVE2QixVQUFVLENBQUM7WUFDM0I5QixXQUFXQyxRQUFRUSxlQUFlO1lBQ2xDdUIsVUFBVSxJQUFNZixnQkFBZ0I7b0JBQUUsR0FBR2hCLFFBQVFrQixVQUFVO2dCQUFDO1lBQ3hEMlYsY0FBYztRQUNsQjtRQUNBN1YsZ0JBQWdCLENBQUMvRSxPQUFVO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQc1YsU0FBUztZQUNiO1FBQ0F2UixRQUFRa0IsVUFBVSxDQUFDcVEsT0FBTyxHQUFHO1FBQzdCLE9BQU8yTDtJQUNYLEdBQUc7UUFBQ2xkO0tBQVE7SUFDWmpHLDRDQUF3QixDQUFDLElBQU1pRyxRQUFRNFgsWUFBWSxDQUFDbFksTUFBTW9CLFFBQVEsR0FBRztRQUFDZDtRQUFTTixNQUFNb0IsUUFBUTtLQUFDO0lBQzlGL0csNENBQXdCLENBQUM7UUFDckIsSUFBSTJGLE1BQU1rTixJQUFJLEVBQUU7WUFDWjVNLFFBQVE2RSxRQUFRLENBQUMrSCxJQUFJLEdBQUdsTixNQUFNa04sSUFBSTtRQUN0QztRQUNBLElBQUlsTixNQUFNZ1AsY0FBYyxFQUFFO1lBQ3RCMU8sUUFBUTZFLFFBQVEsQ0FBQzZKLGNBQWMsR0FBR2hQLE1BQU1nUCxjQUFjO1FBQzFEO1FBQ0EsSUFBSWhQLE1BQU1rQyxNQUFNLElBQUksQ0FBQzBILGNBQWM1SixNQUFNa0MsTUFBTSxHQUFHO1lBQzlDNUIsUUFBUWtULFVBQVUsQ0FBQ3hULE1BQU1rQyxNQUFNO1FBQ25DO0lBQ0osR0FBRztRQUFDNUI7UUFBU04sTUFBTWtDLE1BQU07UUFBRWxDLE1BQU1rTixJQUFJO1FBQUVsTixNQUFNZ1AsY0FBYztLQUFDO0lBQzVEM1UsNENBQXdCLENBQUM7UUFDckIyRixNQUFNMEQsZ0JBQWdCLElBQ2xCcEQsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDekI1RSxRQUFRaEQsUUFBUStDLFNBQVM7UUFDN0I7SUFDUixHQUFHO1FBQUMvQztRQUFTTixNQUFNMEQsZ0JBQWdCO0tBQUM7SUFDcENySiw0Q0FBd0IsQ0FBQztRQUNyQixJQUFJaUcsUUFBUVEsZUFBZSxDQUFDYSxPQUFPLEVBQUU7WUFDakMsTUFBTUEsVUFBVXJCLFFBQVFnVCxTQUFTO1lBQ2pDLElBQUkzUixZQUFZdEIsVUFBVXNCLE9BQU8sRUFBRTtnQkFDL0JyQixRQUFRMEgsU0FBUyxDQUFDQyxLQUFLLENBQUNDLElBQUksQ0FBQztvQkFDekJ2RztnQkFDSjtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQUNyQjtRQUFTRCxVQUFVc0IsT0FBTztLQUFDO0lBQy9CdEgsNENBQXdCLENBQUM7UUFDckIsSUFBSTJGLE1BQU1zRCxNQUFNLElBQUksQ0FBQzhGLFVBQVVwSixNQUFNc0QsTUFBTSxFQUFFaWEsUUFBUW5iLE9BQU8sR0FBRztZQUMzRDlCLFFBQVFxWSxNQUFNLENBQUMzWSxNQUFNc0QsTUFBTSxFQUFFaEQsUUFBUTZFLFFBQVEsQ0FBQzRVLFlBQVk7WUFDMUR3RCxRQUFRbmIsT0FBTyxHQUFHcEMsTUFBTXNELE1BQU07WUFDOUJoQyxnQkFBZ0IsQ0FBQzJHLFFBQVc7b0JBQUUsR0FBR0EsS0FBSztnQkFBQztRQUMzQyxPQUNLO1lBQ0QzSCxRQUFRdVosbUJBQW1CO1FBQy9CO0lBQ0osR0FBRztRQUFDdlo7UUFBU04sTUFBTXNELE1BQU07S0FBQztJQUMxQmpKLDRDQUF3QixDQUFDO1FBQ3JCLElBQUksQ0FBQ2lHLFFBQVFnRixNQUFNLENBQUNELEtBQUssRUFBRTtZQUN2Qi9FLFFBQVFnQyxTQUFTO1lBQ2pCaEMsUUFBUWdGLE1BQU0sQ0FBQ0QsS0FBSyxHQUFHO1FBQzNCO1FBQ0EsSUFBSS9FLFFBQVFnRixNQUFNLENBQUN6QyxLQUFLLEVBQUU7WUFDdEJ2QyxRQUFRZ0YsTUFBTSxDQUFDekMsS0FBSyxHQUFHO1lBQ3ZCdkMsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQUUsR0FBRzVILFFBQVFrQixVQUFVO1lBQUM7UUFDekQ7UUFDQWxCLFFBQVFrRCxnQkFBZ0I7SUFDNUI7SUFDQThaLGFBQWFsYixPQUFPLENBQUMvQixTQUFTLEdBQUdELGtCQUFrQkMsV0FBV0M7SUFDOUQsT0FBT2dkLGFBQWFsYixPQUFPO0FBQy9CO0FBRW9LLENBQ3BLLHNDQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL3RyYWRpbmctcGxhdGZvcm0tbmV4dGpzLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWhvb2stZm9ybS9kaXN0L2luZGV4LmVzbS5tanM/OWRjMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUmVhY3RfX2RlZmF1bHQgZnJvbSAncmVhY3QnO1xuXG52YXIgaXNDaGVja0JveElucHV0ID0gKGVsZW1lbnQpID0+IGVsZW1lbnQudHlwZSA9PT0gJ2NoZWNrYm94JztcblxudmFyIGlzRGF0ZU9iamVjdCA9ICh2YWx1ZSkgPT4gdmFsdWUgaW5zdGFuY2VvZiBEYXRlO1xuXG52YXIgaXNOdWxsT3JVbmRlZmluZWQgPSAodmFsdWUpID0+IHZhbHVlID09IG51bGw7XG5cbmNvbnN0IGlzT2JqZWN0VHlwZSA9ICh2YWx1ZSkgPT4gdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JztcbnZhciBpc09iamVjdCA9ICh2YWx1ZSkgPT4gIWlzTnVsbE9yVW5kZWZpbmVkKHZhbHVlKSAmJlxuICAgICFBcnJheS5pc0FycmF5KHZhbHVlKSAmJlxuICAgIGlzT2JqZWN0VHlwZSh2YWx1ZSkgJiZcbiAgICAhaXNEYXRlT2JqZWN0KHZhbHVlKTtcblxudmFyIGdldEV2ZW50VmFsdWUgPSAoZXZlbnQpID0+IGlzT2JqZWN0KGV2ZW50KSAmJiBldmVudC50YXJnZXRcbiAgICA/IGlzQ2hlY2tCb3hJbnB1dChldmVudC50YXJnZXQpXG4gICAgICAgID8gZXZlbnQudGFyZ2V0LmNoZWNrZWRcbiAgICAgICAgOiBldmVudC50YXJnZXQudmFsdWVcbiAgICA6IGV2ZW50O1xuXG52YXIgZ2V0Tm9kZVBhcmVudE5hbWUgPSAobmFtZSkgPT4gbmFtZS5zdWJzdHJpbmcoMCwgbmFtZS5zZWFyY2goL1xcLlxcZCsoXFwufCQpLykpIHx8IG5hbWU7XG5cbnZhciBpc05hbWVJbkZpZWxkQXJyYXkgPSAobmFtZXMsIG5hbWUpID0+IG5hbWVzLmhhcyhnZXROb2RlUGFyZW50TmFtZShuYW1lKSk7XG5cbnZhciBpc1BsYWluT2JqZWN0ID0gKHRlbXBPYmplY3QpID0+IHtcbiAgICBjb25zdCBwcm90b3R5cGVDb3B5ID0gdGVtcE9iamVjdC5jb25zdHJ1Y3RvciAmJiB0ZW1wT2JqZWN0LmNvbnN0cnVjdG9yLnByb3RvdHlwZTtcbiAgICByZXR1cm4gKGlzT2JqZWN0KHByb3RvdHlwZUNvcHkpICYmIHByb3RvdHlwZUNvcHkuaGFzT3duUHJvcGVydHkoJ2lzUHJvdG90eXBlT2YnKSk7XG59O1xuXG52YXIgaXNXZWIgPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJlxuICAgIHR5cGVvZiB3aW5kb3cuSFRNTEVsZW1lbnQgIT09ICd1bmRlZmluZWQnICYmXG4gICAgdHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJztcblxuZnVuY3Rpb24gY2xvbmVPYmplY3QoZGF0YSkge1xuICAgIGxldCBjb3B5O1xuICAgIGNvbnN0IGlzQXJyYXkgPSBBcnJheS5pc0FycmF5KGRhdGEpO1xuICAgIGNvbnN0IGlzRmlsZUxpc3RJbnN0YW5jZSA9IHR5cGVvZiBGaWxlTGlzdCAhPT0gJ3VuZGVmaW5lZCcgPyBkYXRhIGluc3RhbmNlb2YgRmlsZUxpc3QgOiBmYWxzZTtcbiAgICBpZiAoZGF0YSBpbnN0YW5jZW9mIERhdGUpIHtcbiAgICAgICAgY29weSA9IG5ldyBEYXRlKGRhdGEpO1xuICAgIH1cbiAgICBlbHNlIGlmIChkYXRhIGluc3RhbmNlb2YgU2V0KSB7XG4gICAgICAgIGNvcHkgPSBuZXcgU2V0KGRhdGEpO1xuICAgIH1cbiAgICBlbHNlIGlmICghKGlzV2ViICYmIChkYXRhIGluc3RhbmNlb2YgQmxvYiB8fCBpc0ZpbGVMaXN0SW5zdGFuY2UpKSAmJlxuICAgICAgICAoaXNBcnJheSB8fCBpc09iamVjdChkYXRhKSkpIHtcbiAgICAgICAgY29weSA9IGlzQXJyYXkgPyBbXSA6IHt9O1xuICAgICAgICBpZiAoIWlzQXJyYXkgJiYgIWlzUGxhaW5PYmplY3QoZGF0YSkpIHtcbiAgICAgICAgICAgIGNvcHkgPSBkYXRhO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZGF0YSkge1xuICAgICAgICAgICAgICAgIGlmIChkYXRhLmhhc093blByb3BlcnR5KGtleSkpIHtcbiAgICAgICAgICAgICAgICAgICAgY29weVtrZXldID0gY2xvbmVPYmplY3QoZGF0YVtrZXldKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBkYXRhO1xuICAgIH1cbiAgICByZXR1cm4gY29weTtcbn1cblxudmFyIGNvbXBhY3QgPSAodmFsdWUpID0+IEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUuZmlsdGVyKEJvb2xlYW4pIDogW107XG5cbnZhciBpc1VuZGVmaW5lZCA9ICh2YWwpID0+IHZhbCA9PT0gdW5kZWZpbmVkO1xuXG52YXIgZ2V0ID0gKG9iamVjdCwgcGF0aCwgZGVmYXVsdFZhbHVlKSA9PiB7XG4gICAgaWYgKCFwYXRoIHx8ICFpc09iamVjdChvYmplY3QpKSB7XG4gICAgICAgIHJldHVybiBkZWZhdWx0VmFsdWU7XG4gICAgfVxuICAgIGNvbnN0IHJlc3VsdCA9IGNvbXBhY3QocGF0aC5zcGxpdCgvWyxbXFxdLl0rPy8pKS5yZWR1Y2UoKHJlc3VsdCwga2V5KSA9PiBpc051bGxPclVuZGVmaW5lZChyZXN1bHQpID8gcmVzdWx0IDogcmVzdWx0W2tleV0sIG9iamVjdCk7XG4gICAgcmV0dXJuIGlzVW5kZWZpbmVkKHJlc3VsdCkgfHwgcmVzdWx0ID09PSBvYmplY3RcbiAgICAgICAgPyBpc1VuZGVmaW5lZChvYmplY3RbcGF0aF0pXG4gICAgICAgICAgICA/IGRlZmF1bHRWYWx1ZVxuICAgICAgICAgICAgOiBvYmplY3RbcGF0aF1cbiAgICAgICAgOiByZXN1bHQ7XG59O1xuXG52YXIgaXNCb29sZWFuID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJztcblxudmFyIGlzS2V5ID0gKHZhbHVlKSA9PiAvXlxcdyokLy50ZXN0KHZhbHVlKTtcblxudmFyIHN0cmluZ1RvUGF0aCA9IChpbnB1dCkgPT4gY29tcGFjdChpbnB1dC5yZXBsYWNlKC9bXCJ8J118XFxdL2csICcnKS5zcGxpdCgvXFwufFxcWy8pKTtcblxudmFyIHNldCA9IChvYmplY3QsIHBhdGgsIHZhbHVlKSA9PiB7XG4gICAgbGV0IGluZGV4ID0gLTE7XG4gICAgY29uc3QgdGVtcFBhdGggPSBpc0tleShwYXRoKSA/IFtwYXRoXSA6IHN0cmluZ1RvUGF0aChwYXRoKTtcbiAgICBjb25zdCBsZW5ndGggPSB0ZW1wUGF0aC5sZW5ndGg7XG4gICAgY29uc3QgbGFzdEluZGV4ID0gbGVuZ3RoIC0gMTtcbiAgICB3aGlsZSAoKytpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICBjb25zdCBrZXkgPSB0ZW1wUGF0aFtpbmRleF07XG4gICAgICAgIGxldCBuZXdWYWx1ZSA9IHZhbHVlO1xuICAgICAgICBpZiAoaW5kZXggIT09IGxhc3RJbmRleCkge1xuICAgICAgICAgICAgY29uc3Qgb2JqVmFsdWUgPSBvYmplY3Rba2V5XTtcbiAgICAgICAgICAgIG5ld1ZhbHVlID1cbiAgICAgICAgICAgICAgICBpc09iamVjdChvYmpWYWx1ZSkgfHwgQXJyYXkuaXNBcnJheShvYmpWYWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgPyBvYmpWYWx1ZVxuICAgICAgICAgICAgICAgICAgICA6ICFpc05hTigrdGVtcFBhdGhbaW5kZXggKyAxXSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gW11cbiAgICAgICAgICAgICAgICAgICAgICAgIDoge307XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGtleSA9PT0gJ19fcHJvdG9fXycgfHwga2V5ID09PSAnY29uc3RydWN0b3InIHx8IGtleSA9PT0gJ3Byb3RvdHlwZScpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBvYmplY3Rba2V5XSA9IG5ld1ZhbHVlO1xuICAgICAgICBvYmplY3QgPSBvYmplY3Rba2V5XTtcbiAgICB9XG59O1xuXG5jb25zdCBFVkVOVFMgPSB7XG4gICAgQkxVUjogJ2JsdXInLFxuICAgIEZPQ1VTX09VVDogJ2ZvY3Vzb3V0JyxcbiAgICBDSEFOR0U6ICdjaGFuZ2UnLFxufTtcbmNvbnN0IFZBTElEQVRJT05fTU9ERSA9IHtcbiAgICBvbkJsdXI6ICdvbkJsdXInLFxuICAgIG9uQ2hhbmdlOiAnb25DaGFuZ2UnLFxuICAgIG9uU3VibWl0OiAnb25TdWJtaXQnLFxuICAgIG9uVG91Y2hlZDogJ29uVG91Y2hlZCcsXG4gICAgYWxsOiAnYWxsJyxcbn07XG5jb25zdCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTID0ge1xuICAgIG1heDogJ21heCcsXG4gICAgbWluOiAnbWluJyxcbiAgICBtYXhMZW5ndGg6ICdtYXhMZW5ndGgnLFxuICAgIG1pbkxlbmd0aDogJ21pbkxlbmd0aCcsXG4gICAgcGF0dGVybjogJ3BhdHRlcm4nLFxuICAgIHJlcXVpcmVkOiAncmVxdWlyZWQnLFxuICAgIHZhbGlkYXRlOiAndmFsaWRhdGUnLFxufTtcblxuY29uc3QgSG9va0Zvcm1Db250ZXh0ID0gUmVhY3RfX2RlZmF1bHQuY3JlYXRlQ29udGV4dChudWxsKTtcbi8qKlxuICogVGhpcyBjdXN0b20gaG9vayBhbGxvd3MgeW91IHRvIGFjY2VzcyB0aGUgZm9ybSBjb250ZXh0LiB1c2VGb3JtQ29udGV4dCBpcyBpbnRlbmRlZCB0byBiZSB1c2VkIGluIGRlZXBseSBuZXN0ZWQgc3RydWN0dXJlcywgd2hlcmUgaXQgd291bGQgYmVjb21lIGluY29udmVuaWVudCB0byBwYXNzIHRoZSBjb250ZXh0IGFzIGEgcHJvcC4gVG8gYmUgdXNlZCB3aXRoIHtAbGluayBGb3JtUHJvdmlkZXJ9LlxuICpcbiAqIEByZW1hcmtzXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2Vmb3JtY29udGV4dCkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXY3LWZvcm0tY29udGV4dC15dHVkaSlcbiAqXG4gKiBAcmV0dXJucyByZXR1cm4gYWxsIHVzZUZvcm0gbWV0aG9kc1xuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm0oKTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSBkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Rm9ybVByb3ZpZGVyIHsuLi5tZXRob2RzfSA+XG4gKiAgICAgICA8Zm9ybSBvblN1Ym1pdD17bWV0aG9kcy5oYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgICAgPE5lc3RlZElucHV0IC8+XG4gKiAgICAgICAgIDxpbnB1dCB0eXBlPVwic3VibWl0XCIgLz5cbiAqICAgICAgIDwvZm9ybT5cbiAqICAgICA8L0Zvcm1Qcm92aWRlcj5cbiAqICAgKTtcbiAqIH1cbiAqXG4gKiAgZnVuY3Rpb24gTmVzdGVkSW5wdXQoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIgfSA9IHVzZUZvcm1Db250ZXh0KCk7IC8vIHJldHJpZXZlIGFsbCBob29rIG1ldGhvZHNcbiAqICAgcmV0dXJuIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJ0ZXN0XCIpfSAvPjtcbiAqIH1cbiAqIGBgYFxuICovXG5jb25zdCB1c2VGb3JtQ29udGV4dCA9ICgpID0+IFJlYWN0X19kZWZhdWx0LnVzZUNvbnRleHQoSG9va0Zvcm1Db250ZXh0KTtcbi8qKlxuICogQSBwcm92aWRlciBjb21wb25lbnQgdGhhdCBwcm9wYWdhdGVzIHRoZSBgdXNlRm9ybWAgbWV0aG9kcyB0byBhbGwgY2hpbGRyZW4gY29tcG9uZW50cyB2aWEgW1JlYWN0IENvbnRleHRdKGh0dHBzOi8vcmVhY3Rqcy5vcmcvZG9jcy9jb250ZXh0Lmh0bWwpIEFQSS4gVG8gYmUgdXNlZCB3aXRoIHtAbGluayB1c2VGb3JtQ29udGV4dH0uXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWZvcm1jb250ZXh0KSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tdjctZm9ybS1jb250ZXh0LXl0dWRpKVxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIGFsbCB1c2VGb3JtIG1ldGhvZHNcbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBmdW5jdGlvbiBBcHAoKSB7XG4gKiAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtKCk7XG4gKiAgIGNvbnN0IG9uU3VibWl0ID0gZGF0YSA9PiBjb25zb2xlLmxvZyhkYXRhKTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPEZvcm1Qcm92aWRlciB7Li4ubWV0aG9kc30gPlxuICogICAgICAgPGZvcm0gb25TdWJtaXQ9e21ldGhvZHMuaGFuZGxlU3VibWl0KG9uU3VibWl0KX0+XG4gKiAgICAgICAgIDxOZXN0ZWRJbnB1dCAvPlxuICogICAgICAgICA8aW5wdXQgdHlwZT1cInN1Ym1pdFwiIC8+XG4gKiAgICAgICA8L2Zvcm0+XG4gKiAgICAgPC9Gb3JtUHJvdmlkZXI+XG4gKiAgICk7XG4gKiB9XG4gKlxuICogIGZ1bmN0aW9uIE5lc3RlZElucHV0KCkge1xuICogICBjb25zdCB7IHJlZ2lzdGVyIH0gPSB1c2VGb3JtQ29udGV4dCgpOyAvLyByZXRyaWV2ZSBhbGwgaG9vayBtZXRob2RzXG4gKiAgIHJldHVybiA8aW5wdXQgey4uLnJlZ2lzdGVyKFwidGVzdFwiKX0gLz47XG4gKiB9XG4gKiBgYGBcbiAqL1xuY29uc3QgRm9ybVByb3ZpZGVyID0gKHByb3BzKSA9PiB7XG4gICAgY29uc3QgeyBjaGlsZHJlbiwgLi4uZGF0YSB9ID0gcHJvcHM7XG4gICAgcmV0dXJuIChSZWFjdF9fZGVmYXVsdC5jcmVhdGVFbGVtZW50KEhvb2tGb3JtQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogZGF0YSB9LCBjaGlsZHJlbikpO1xufTtcblxudmFyIGdldFByb3h5Rm9ybVN0YXRlID0gKGZvcm1TdGF0ZSwgY29udHJvbCwgbG9jYWxQcm94eUZvcm1TdGF0ZSwgaXNSb290ID0gdHJ1ZSkgPT4ge1xuICAgIGNvbnN0IHJlc3VsdCA9IHtcbiAgICAgICAgZGVmYXVsdFZhbHVlczogY29udHJvbC5fZGVmYXVsdFZhbHVlcyxcbiAgICB9O1xuICAgIGZvciAoY29uc3Qga2V5IGluIGZvcm1TdGF0ZSkge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocmVzdWx0LCBrZXksIHtcbiAgICAgICAgICAgIGdldDogKCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IF9rZXkgPSBrZXk7XG4gICAgICAgICAgICAgICAgaWYgKGNvbnRyb2wuX3Byb3h5Rm9ybVN0YXRlW19rZXldICE9PSBWQUxJREFUSU9OX01PREUuYWxsKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2wuX3Byb3h5Rm9ybVN0YXRlW19rZXldID0gIWlzUm9vdCB8fCBWQUxJREFUSU9OX01PREUuYWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBsb2NhbFByb3h5Rm9ybVN0YXRlICYmIChsb2NhbFByb3h5Rm9ybVN0YXRlW19rZXldID0gdHJ1ZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZvcm1TdGF0ZVtfa2V5XTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufTtcblxuY29uc3QgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogUmVhY3QudXNlRWZmZWN0O1xuXG4vKipcbiAqIFRoaXMgY3VzdG9tIGhvb2sgYWxsb3dzIHlvdSB0byBzdWJzY3JpYmUgdG8gZWFjaCBmb3JtIHN0YXRlLCBhbmQgaXNvbGF0ZSB0aGUgcmUtcmVuZGVyIGF0IHRoZSBjdXN0b20gaG9vayBsZXZlbC4gSXQgaGFzIGl0cyBzY29wZSBpbiB0ZXJtcyBvZiBmb3JtIHN0YXRlIHN1YnNjcmlwdGlvbiwgc28gaXQgd291bGQgbm90IGFmZmVjdCBvdGhlciB1c2VGb3JtU3RhdGUgYW5kIHVzZUZvcm0uIFVzaW5nIHRoaXMgaG9vayBjYW4gcmVkdWNlIHRoZSByZS1yZW5kZXIgaW1wYWN0IG9uIGxhcmdlIGFuZCBjb21wbGV4IGZvcm0gYXBwbGljYXRpb24uXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWZvcm1zdGF0ZSkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvdXNlZm9ybXN0YXRlLTc1eGx5KVxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIGluY2x1ZGUgb3B0aW9ucyBvbiBzcGVjaWZ5IGZpZWxkcyB0byBzdWJzY3JpYmUuIHtAbGluayBVc2VGb3JtU3RhdGVSZXR1cm59XG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IHJlZ2lzdGVyLCBoYW5kbGVTdWJtaXQsIGNvbnRyb2wgfSA9IHVzZUZvcm0oe1xuICogICAgIGRlZmF1bHRWYWx1ZXM6IHtcbiAqICAgICBmaXJzdE5hbWU6IFwiZmlyc3ROYW1lXCJcbiAqICAgfX0pO1xuICogICBjb25zdCB7IGRpcnR5RmllbGRzIH0gPSB1c2VGb3JtU3RhdGUoe1xuICogICAgIGNvbnRyb2xcbiAqICAgfSk7XG4gKiAgIGNvbnN0IG9uU3VibWl0ID0gKGRhdGEpID0+IGNvbnNvbGUubG9nKGRhdGEpO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0KG9uU3VibWl0KX0+XG4gKiAgICAgICA8aW5wdXQgey4uLnJlZ2lzdGVyKFwiZmlyc3ROYW1lXCIpfSBwbGFjZWhvbGRlcj1cIkZpcnN0IE5hbWVcIiAvPlxuICogICAgICAge2RpcnR5RmllbGRzLmZpcnN0TmFtZSAmJiA8cD5GaWVsZCBpcyBkaXJ0eS48L3A+fVxuICogICAgICAgPGlucHV0IHR5cGU9XCJzdWJtaXRcIiAvPlxuICogICAgIDwvZm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VGb3JtU3RhdGUocHJvcHMpIHtcbiAgICBjb25zdCBtZXRob2RzID0gdXNlRm9ybUNvbnRleHQoKTtcbiAgICBjb25zdCB7IGNvbnRyb2wgPSBtZXRob2RzLmNvbnRyb2wsIGRpc2FibGVkLCBuYW1lLCBleGFjdCB9ID0gcHJvcHMgfHwge307XG4gICAgY29uc3QgW2Zvcm1TdGF0ZSwgdXBkYXRlRm9ybVN0YXRlXSA9IFJlYWN0X19kZWZhdWx0LnVzZVN0YXRlKGNvbnRyb2wuX2Zvcm1TdGF0ZSk7XG4gICAgY29uc3QgX2xvY2FsUHJveHlGb3JtU3RhdGUgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYoe1xuICAgICAgICBpc0RpcnR5OiBmYWxzZSxcbiAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgZGlydHlGaWVsZHM6IGZhbHNlLFxuICAgICAgICB0b3VjaGVkRmllbGRzOiBmYWxzZSxcbiAgICAgICAgdmFsaWRhdGluZ0ZpZWxkczogZmFsc2UsXG4gICAgICAgIGlzVmFsaWRhdGluZzogZmFsc2UsXG4gICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICBlcnJvcnM6IGZhbHNlLFxuICAgIH0pO1xuICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4gY29udHJvbC5fc3Vic2NyaWJlKHtcbiAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgZm9ybVN0YXRlOiBfbG9jYWxQcm94eUZvcm1TdGF0ZS5jdXJyZW50LFxuICAgICAgICBleGFjdCxcbiAgICAgICAgY2FsbGJhY2s6IChmb3JtU3RhdGUpID0+IHtcbiAgICAgICAgICAgICFkaXNhYmxlZCAmJlxuICAgICAgICAgICAgICAgIHVwZGF0ZUZvcm1TdGF0ZSh7XG4gICAgICAgICAgICAgICAgICAgIC4uLmNvbnRyb2wuX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgLi4uZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgIH0pLCBbbmFtZSwgZGlzYWJsZWQsIGV4YWN0XSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgX2xvY2FsUHJveHlGb3JtU3RhdGUuY3VycmVudC5pc1ZhbGlkICYmIGNvbnRyb2wuX3NldFZhbGlkKHRydWUpO1xuICAgIH0sIFtjb250cm9sXSk7XG4gICAgcmV0dXJuIFJlYWN0X19kZWZhdWx0LnVzZU1lbW8oKCkgPT4gZ2V0UHJveHlGb3JtU3RhdGUoZm9ybVN0YXRlLCBjb250cm9sLCBfbG9jYWxQcm94eUZvcm1TdGF0ZS5jdXJyZW50LCBmYWxzZSksIFtmb3JtU3RhdGUsIGNvbnRyb2xdKTtcbn1cblxudmFyIGlzU3RyaW5nID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnO1xuXG52YXIgZ2VuZXJhdGVXYXRjaE91dHB1dCA9IChuYW1lcywgX25hbWVzLCBmb3JtVmFsdWVzLCBpc0dsb2JhbCwgZGVmYXVsdFZhbHVlKSA9PiB7XG4gICAgaWYgKGlzU3RyaW5nKG5hbWVzKSkge1xuICAgICAgICBpc0dsb2JhbCAmJiBfbmFtZXMud2F0Y2guYWRkKG5hbWVzKTtcbiAgICAgICAgcmV0dXJuIGdldChmb3JtVmFsdWVzLCBuYW1lcywgZGVmYXVsdFZhbHVlKTtcbiAgICB9XG4gICAgaWYgKEFycmF5LmlzQXJyYXkobmFtZXMpKSB7XG4gICAgICAgIHJldHVybiBuYW1lcy5tYXAoKGZpZWxkTmFtZSkgPT4gKGlzR2xvYmFsICYmIF9uYW1lcy53YXRjaC5hZGQoZmllbGROYW1lKSwgZ2V0KGZvcm1WYWx1ZXMsIGZpZWxkTmFtZSkpKTtcbiAgICB9XG4gICAgaXNHbG9iYWwgJiYgKF9uYW1lcy53YXRjaEFsbCA9IHRydWUpO1xuICAgIHJldHVybiBmb3JtVmFsdWVzO1xufTtcblxuLyoqXG4gKiBDdXN0b20gaG9vayB0byBzdWJzY3JpYmUgdG8gZmllbGQgY2hhbmdlIGFuZCBpc29sYXRlIHJlLXJlbmRlcmluZyBhdCB0aGUgY29tcG9uZW50IGxldmVsLlxuICpcbiAqIEByZW1hcmtzXG4gKlxuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNld2F0Y2gpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS12Ny10cy11c2V3YXRjaC1oOWk1ZSlcbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBjb25zdCB7IGNvbnRyb2wgfSA9IHVzZUZvcm0oKTtcbiAqIGNvbnN0IHZhbHVlcyA9IHVzZVdhdGNoKHtcbiAqICAgbmFtZTogXCJmaWVsZE5hbWVcIlxuICogICBjb250cm9sLFxuICogfSlcbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VXYXRjaChwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IHsgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgbmFtZSwgZGVmYXVsdFZhbHVlLCBkaXNhYmxlZCwgZXhhY3QsIH0gPSBwcm9wcyB8fCB7fTtcbiAgICBjb25zdCBfZGVmYXVsdFZhbHVlID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKGRlZmF1bHRWYWx1ZSk7XG4gICAgY29uc3QgW3ZhbHVlLCB1cGRhdGVWYWx1ZV0gPSBSZWFjdF9fZGVmYXVsdC51c2VTdGF0ZShjb250cm9sLl9nZXRXYXRjaChuYW1lLCBfZGVmYXVsdFZhbHVlLmN1cnJlbnQpKTtcbiAgICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IGNvbnRyb2wuX3N1YnNjcmliZSh7XG4gICAgICAgIG5hbWU6IG5hbWUsXG4gICAgICAgIGZvcm1TdGF0ZToge1xuICAgICAgICAgICAgdmFsdWVzOiB0cnVlLFxuICAgICAgICB9LFxuICAgICAgICBleGFjdCxcbiAgICAgICAgY2FsbGJhY2s6IChmb3JtU3RhdGUpID0+ICFkaXNhYmxlZCAmJlxuICAgICAgICAgICAgdXBkYXRlVmFsdWUoZ2VuZXJhdGVXYXRjaE91dHB1dChuYW1lLCBjb250cm9sLl9uYW1lcywgZm9ybVN0YXRlLnZhbHVlcyB8fCBjb250cm9sLl9mb3JtVmFsdWVzLCBmYWxzZSwgX2RlZmF1bHRWYWx1ZS5jdXJyZW50KSksXG4gICAgfSksIFtuYW1lLCBjb250cm9sLCBkaXNhYmxlZCwgZXhhY3RdKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4gY29udHJvbC5fcmVtb3ZlVW5tb3VudGVkKCkpO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuLyoqXG4gKiBDdXN0b20gaG9vayB0byB3b3JrIHdpdGggY29udHJvbGxlZCBjb21wb25lbnQsIHRoaXMgZnVuY3Rpb24gcHJvdmlkZSB5b3Ugd2l0aCBib3RoIGZvcm0gYW5kIGZpZWxkIGxldmVsIHN0YXRlLiBSZS1yZW5kZXIgaXMgaXNvbGF0ZWQgYXQgdGhlIGhvb2sgbGV2ZWwuXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWNvbnRyb2xsZXIpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3VzZWNvbnRyb2xsZXItMG84cHgpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gdGhlIHBhdGggbmFtZSB0byB0aGUgZm9ybSBmaWVsZCB2YWx1ZSwgYW5kIHZhbGlkYXRpb24gcnVsZXMuXG4gKlxuICogQHJldHVybnMgZmllbGQgcHJvcGVydGllcywgZmllbGQgYW5kIGZvcm0gc3RhdGUuIHtAbGluayBVc2VDb250cm9sbGVyUmV0dXJufVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIElucHV0KHByb3BzKSB7XG4gKiAgIGNvbnN0IHsgZmllbGQsIGZpZWxkU3RhdGUsIGZvcm1TdGF0ZSB9ID0gdXNlQ29udHJvbGxlcihwcm9wcyk7XG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGRpdj5cbiAqICAgICAgIDxpbnB1dCB7Li4uZmllbGR9IHBsYWNlaG9sZGVyPXtwcm9wcy5uYW1lfSAvPlxuICogICAgICAgPHA+e2ZpZWxkU3RhdGUuaXNUb3VjaGVkICYmIFwiVG91Y2hlZFwifTwvcD5cbiAqICAgICAgIDxwPntmb3JtU3RhdGUuaXNTdWJtaXR0ZWQgPyBcInN1Ym1pdHRlZFwiIDogXCJcIn08L3A+XG4gKiAgICAgPC9kaXY+XG4gKiAgICk7XG4gKiB9XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gdXNlQ29udHJvbGxlcihwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IHsgbmFtZSwgZGlzYWJsZWQsIGNvbnRyb2wgPSBtZXRob2RzLmNvbnRyb2wsIHNob3VsZFVucmVnaXN0ZXIgfSA9IHByb3BzO1xuICAgIGNvbnN0IGlzQXJyYXlGaWVsZCA9IGlzTmFtZUluRmllbGRBcnJheShjb250cm9sLl9uYW1lcy5hcnJheSwgbmFtZSk7XG4gICAgY29uc3QgdmFsdWUgPSB1c2VXYXRjaCh7XG4gICAgICAgIGNvbnRyb2wsXG4gICAgICAgIG5hbWUsXG4gICAgICAgIGRlZmF1bHRWYWx1ZTogZ2V0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIG5hbWUsIGdldChjb250cm9sLl9kZWZhdWx0VmFsdWVzLCBuYW1lLCBwcm9wcy5kZWZhdWx0VmFsdWUpKSxcbiAgICAgICAgZXhhY3Q6IHRydWUsXG4gICAgfSk7XG4gICAgY29uc3QgZm9ybVN0YXRlID0gdXNlRm9ybVN0YXRlKHtcbiAgICAgICAgY29udHJvbCxcbiAgICAgICAgbmFtZSxcbiAgICAgICAgZXhhY3Q6IHRydWUsXG4gICAgfSk7XG4gICAgY29uc3QgX3Byb3BzID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKHByb3BzKTtcbiAgICBjb25zdCBfcmVnaXN0ZXJQcm9wcyA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZihjb250cm9sLnJlZ2lzdGVyKG5hbWUsIHtcbiAgICAgICAgLi4ucHJvcHMucnVsZXMsXG4gICAgICAgIHZhbHVlLFxuICAgICAgICAuLi4oaXNCb29sZWFuKHByb3BzLmRpc2FibGVkKSA/IHsgZGlzYWJsZWQ6IHByb3BzLmRpc2FibGVkIH0gOiB7fSksXG4gICAgfSkpO1xuICAgIGNvbnN0IGZpZWxkU3RhdGUgPSBSZWFjdF9fZGVmYXVsdC51c2VNZW1vKCgpID0+IE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHt9LCB7XG4gICAgICAgIGludmFsaWQ6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpLFxuICAgICAgICB9LFxuICAgICAgICBpc0RpcnR5OiB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiAhIWdldChmb3JtU3RhdGUuZGlydHlGaWVsZHMsIG5hbWUpLFxuICAgICAgICB9LFxuICAgICAgICBpc1RvdWNoZWQ6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lKSxcbiAgICAgICAgfSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiAhIWdldChmb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgZ2V0OiAoKSA9PiBnZXQoZm9ybVN0YXRlLmVycm9ycywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgfSksIFtmb3JtU3RhdGUsIG5hbWVdKTtcbiAgICBjb25zdCBvbkNoYW5nZSA9IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKChldmVudCkgPT4gX3JlZ2lzdGVyUHJvcHMuY3VycmVudC5vbkNoYW5nZSh7XG4gICAgICAgIHRhcmdldDoge1xuICAgICAgICAgICAgdmFsdWU6IGdldEV2ZW50VmFsdWUoZXZlbnQpLFxuICAgICAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgfSxcbiAgICAgICAgdHlwZTogRVZFTlRTLkNIQU5HRSxcbiAgICB9KSwgW25hbWVdKTtcbiAgICBjb25zdCBvbkJsdXIgPSBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjaygoKSA9PiBfcmVnaXN0ZXJQcm9wcy5jdXJyZW50Lm9uQmx1cih7XG4gICAgICAgIHRhcmdldDoge1xuICAgICAgICAgICAgdmFsdWU6IGdldChjb250cm9sLl9mb3JtVmFsdWVzLCBuYW1lKSxcbiAgICAgICAgICAgIG5hbWU6IG5hbWUsXG4gICAgICAgIH0sXG4gICAgICAgIHR5cGU6IEVWRU5UUy5CTFVSLFxuICAgIH0pLCBbbmFtZSwgY29udHJvbC5fZm9ybVZhbHVlc10pO1xuICAgIGNvbnN0IHJlZiA9IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKChlbG0pID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoY29udHJvbC5fZmllbGRzLCBuYW1lKTtcbiAgICAgICAgaWYgKGZpZWxkICYmIGVsbSkge1xuICAgICAgICAgICAgZmllbGQuX2YucmVmID0ge1xuICAgICAgICAgICAgICAgIGZvY3VzOiAoKSA9PiBlbG0uZm9jdXMoKSxcbiAgICAgICAgICAgICAgICBzZWxlY3Q6ICgpID0+IGVsbS5zZWxlY3QoKSxcbiAgICAgICAgICAgICAgICBzZXRDdXN0b21WYWxpZGl0eTogKG1lc3NhZ2UpID0+IGVsbS5zZXRDdXN0b21WYWxpZGl0eShtZXNzYWdlKSxcbiAgICAgICAgICAgICAgICByZXBvcnRWYWxpZGl0eTogKCkgPT4gZWxtLnJlcG9ydFZhbGlkaXR5KCksXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgfSwgW2NvbnRyb2wuX2ZpZWxkcywgbmFtZV0pO1xuICAgIGNvbnN0IGZpZWxkID0gUmVhY3RfX2RlZmF1bHQudXNlTWVtbygoKSA9PiAoe1xuICAgICAgICBuYW1lLFxuICAgICAgICB2YWx1ZSxcbiAgICAgICAgLi4uKGlzQm9vbGVhbihkaXNhYmxlZCkgfHwgZm9ybVN0YXRlLmRpc2FibGVkXG4gICAgICAgICAgICA/IHsgZGlzYWJsZWQ6IGZvcm1TdGF0ZS5kaXNhYmxlZCB8fCBkaXNhYmxlZCB9XG4gICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgb25DaGFuZ2UsXG4gICAgICAgIG9uQmx1cixcbiAgICAgICAgcmVmLFxuICAgIH0pLCBbbmFtZSwgZGlzYWJsZWQsIGZvcm1TdGF0ZS5kaXNhYmxlZCwgb25DaGFuZ2UsIG9uQmx1ciwgcmVmLCB2YWx1ZV0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IF9zaG91bGRVbnJlZ2lzdGVyRmllbGQgPSBjb250cm9sLl9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXIgfHwgc2hvdWxkVW5yZWdpc3RlcjtcbiAgICAgICAgY29udHJvbC5yZWdpc3RlcihuYW1lLCB7XG4gICAgICAgICAgICAuLi5fcHJvcHMuY3VycmVudC5ydWxlcyxcbiAgICAgICAgICAgIC4uLihpc0Jvb2xlYW4oX3Byb3BzLmN1cnJlbnQuZGlzYWJsZWQpXG4gICAgICAgICAgICAgICAgPyB7IGRpc2FibGVkOiBfcHJvcHMuY3VycmVudC5kaXNhYmxlZCB9XG4gICAgICAgICAgICAgICAgOiB7fSksXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCB1cGRhdGVNb3VudGVkID0gKG5hbWUsIHZhbHVlKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZCA9IGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2YubW91bnQgPSB2YWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgdXBkYXRlTW91bnRlZChuYW1lLCB0cnVlKTtcbiAgICAgICAgaWYgKF9zaG91bGRVbnJlZ2lzdGVyRmllbGQpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gY2xvbmVPYmplY3QoZ2V0KGNvbnRyb2wuX29wdGlvbnMuZGVmYXVsdFZhbHVlcywgbmFtZSkpO1xuICAgICAgICAgICAgc2V0KGNvbnRyb2wuX2RlZmF1bHRWYWx1ZXMsIG5hbWUsIHZhbHVlKTtcbiAgICAgICAgICAgIGlmIChpc1VuZGVmaW5lZChnZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSkpKSB7XG4gICAgICAgICAgICAgICAgc2V0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIG5hbWUsIHZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAhaXNBcnJheUZpZWxkICYmIGNvbnRyb2wucmVnaXN0ZXIobmFtZSk7XG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICAoaXNBcnJheUZpZWxkXG4gICAgICAgICAgICAgICAgPyBfc2hvdWxkVW5yZWdpc3RlckZpZWxkICYmICFjb250cm9sLl9zdGF0ZS5hY3Rpb25cbiAgICAgICAgICAgICAgICA6IF9zaG91bGRVbnJlZ2lzdGVyRmllbGQpXG4gICAgICAgICAgICAgICAgPyBjb250cm9sLnVucmVnaXN0ZXIobmFtZSlcbiAgICAgICAgICAgICAgICA6IHVwZGF0ZU1vdW50ZWQobmFtZSwgZmFsc2UpO1xuICAgICAgICB9O1xuICAgIH0sIFtuYW1lLCBjb250cm9sLCBpc0FycmF5RmllbGQsIHNob3VsZFVucmVnaXN0ZXJdKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBjb250cm9sLl9zZXREaXNhYmxlZEZpZWxkKHtcbiAgICAgICAgICAgIGRpc2FibGVkLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgfSk7XG4gICAgfSwgW2Rpc2FibGVkLCBuYW1lLCBjb250cm9sXSk7XG4gICAgcmV0dXJuIFJlYWN0X19kZWZhdWx0LnVzZU1lbW8oKCkgPT4gKHtcbiAgICAgICAgZmllbGQsXG4gICAgICAgIGZvcm1TdGF0ZSxcbiAgICAgICAgZmllbGRTdGF0ZSxcbiAgICB9KSwgW2ZpZWxkLCBmb3JtU3RhdGUsIGZpZWxkU3RhdGVdKTtcbn1cblxuLyoqXG4gKiBDb21wb25lbnQgYmFzZWQgb24gYHVzZUNvbnRyb2xsZXJgIGhvb2sgdG8gd29yayB3aXRoIGNvbnRyb2xsZWQgY29tcG9uZW50LlxuICpcbiAqIEByZW1hcmtzXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2Vjb250cm9sbGVyL2NvbnRyb2xsZXIpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS12Ni1jb250cm9sbGVyLXRzLWp3eXp3KSDigKIgW1ZpZGVvXShodHRwczovL3d3dy55b3V0dWJlLmNvbS93YXRjaD92PU4yVU5rX1VDVnlBKVxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIHRoZSBwYXRoIG5hbWUgdG8gdGhlIGZvcm0gZmllbGQgdmFsdWUsIGFuZCB2YWxpZGF0aW9uIHJ1bGVzLlxuICpcbiAqIEByZXR1cm5zIHByb3ZpZGUgZmllbGQgaGFuZGxlciBmdW5jdGlvbnMsIGZpZWxkIGFuZCBmb3JtIHN0YXRlLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyBjb250cm9sIH0gPSB1c2VGb3JtPEZvcm1WYWx1ZXM+KHtcbiAqICAgICBkZWZhdWx0VmFsdWVzOiB7XG4gKiAgICAgICB0ZXN0OiBcIlwiXG4gKiAgICAgfVxuICogICB9KTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGZvcm0+XG4gKiAgICAgICA8Q29udHJvbGxlclxuICogICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICogICAgICAgICBuYW1lPVwidGVzdFwiXG4gKiAgICAgICAgIHJlbmRlcj17KHsgZmllbGQ6IHsgb25DaGFuZ2UsIG9uQmx1ciwgdmFsdWUsIHJlZiB9LCBmb3JtU3RhdGUsIGZpZWxkU3RhdGUgfSkgPT4gKFxuICogICAgICAgICAgIDw+XG4gKiAgICAgICAgICAgICA8aW5wdXRcbiAqICAgICAgICAgICAgICAgb25DaGFuZ2U9e29uQ2hhbmdlfSAvLyBzZW5kIHZhbHVlIHRvIGhvb2sgZm9ybVxuICogICAgICAgICAgICAgICBvbkJsdXI9e29uQmx1cn0gLy8gbm90aWZ5IHdoZW4gaW5wdXQgaXMgdG91Y2hlZFxuICogICAgICAgICAgICAgICB2YWx1ZT17dmFsdWV9IC8vIHJldHVybiB1cGRhdGVkIHZhbHVlXG4gKiAgICAgICAgICAgICAgIHJlZj17cmVmfSAvLyBzZXQgcmVmIGZvciBmb2N1cyBtYW5hZ2VtZW50XG4gKiAgICAgICAgICAgICAvPlxuICogICAgICAgICAgICAgPHA+e2Zvcm1TdGF0ZS5pc1N1Ym1pdHRlZCA/IFwic3VibWl0dGVkXCIgOiBcIlwifTwvcD5cbiAqICAgICAgICAgICAgIDxwPntmaWVsZFN0YXRlLmlzVG91Y2hlZCA/IFwidG91Y2hlZFwiIDogXCJcIn08L3A+XG4gKiAgICAgICAgICAgPC8+XG4gKiAgICAgICAgICl9XG4gKiAgICAgICAvPlxuICogICAgIDwvZm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5jb25zdCBDb250cm9sbGVyID0gKHByb3BzKSA9PiBwcm9wcy5yZW5kZXIodXNlQ29udHJvbGxlcihwcm9wcykpO1xuXG5jb25zdCBmbGF0dGVuID0gKG9iaikgPT4ge1xuICAgIGNvbnN0IG91dHB1dCA9IHt9O1xuICAgIGZvciAoY29uc3Qga2V5IG9mIE9iamVjdC5rZXlzKG9iaikpIHtcbiAgICAgICAgaWYgKGlzT2JqZWN0VHlwZShvYmpba2V5XSkgJiYgb2JqW2tleV0gIT09IG51bGwpIHtcbiAgICAgICAgICAgIGNvbnN0IG5lc3RlZCA9IGZsYXR0ZW4ob2JqW2tleV0pO1xuICAgICAgICAgICAgZm9yIChjb25zdCBuZXN0ZWRLZXkgb2YgT2JqZWN0LmtleXMobmVzdGVkKSkge1xuICAgICAgICAgICAgICAgIG91dHB1dFtgJHtrZXl9LiR7bmVzdGVkS2V5fWBdID0gbmVzdGVkW25lc3RlZEtleV07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBvdXRwdXRba2V5XSA9IG9ialtrZXldO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBvdXRwdXQ7XG59O1xuXG5jb25zdCBQT1NUX1JFUVVFU1QgPSAncG9zdCc7XG4vKipcbiAqIEZvcm0gY29tcG9uZW50IHRvIG1hbmFnZSBzdWJtaXNzaW9uLlxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIHRvIHNldHVwIHN1Ym1pc3Npb24gZGV0YWlsLiB7QGxpbmsgRm9ybVByb3BzfVxuICpcbiAqIEByZXR1cm5zIGZvcm0gY29tcG9uZW50IG9yIGhlYWRsZXNzIHJlbmRlciBwcm9wLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyBjb250cm9sLCBmb3JtU3RhdGU6IHsgZXJyb3JzIH0gfSA9IHVzZUZvcm0oKTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPEZvcm0gYWN0aW9uPVwiL2FwaVwiIGNvbnRyb2w9e2NvbnRyb2x9PlxuICogICAgICAgPGlucHV0IHsuLi5yZWdpc3RlcihcIm5hbWVcIil9IC8+XG4gKiAgICAgICA8cD57ZXJyb3JzPy5yb290Py5zZXJ2ZXIgJiYgJ1NlcnZlciBlcnJvcid9PC9wPlxuICogICAgICAgPGJ1dHRvbj5TdWJtaXQ8L2J1dHRvbj5cbiAqICAgICA8L0Zvcm0+XG4gKiAgICk7XG4gKiB9XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gRm9ybShwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IFJlYWN0X19kZWZhdWx0LnVzZVN0YXRlKGZhbHNlKTtcbiAgICBjb25zdCB7IGNvbnRyb2wgPSBtZXRob2RzLmNvbnRyb2wsIG9uU3VibWl0LCBjaGlsZHJlbiwgYWN0aW9uLCBtZXRob2QgPSBQT1NUX1JFUVVFU1QsIGhlYWRlcnMsIGVuY1R5cGUsIG9uRXJyb3IsIHJlbmRlciwgb25TdWNjZXNzLCB2YWxpZGF0ZVN0YXR1cywgLi4ucmVzdCB9ID0gcHJvcHM7XG4gICAgY29uc3Qgc3VibWl0ID0gYXN5bmMgKGV2ZW50KSA9PiB7XG4gICAgICAgIGxldCBoYXNFcnJvciA9IGZhbHNlO1xuICAgICAgICBsZXQgdHlwZSA9ICcnO1xuICAgICAgICBhd2FpdCBjb250cm9sLmhhbmRsZVN1Ym1pdChhc3luYyAoZGF0YSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcbiAgICAgICAgICAgIGxldCBmb3JtRGF0YUpzb24gPSAnJztcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgZm9ybURhdGFKc29uID0gSlNPTi5zdHJpbmdpZnkoZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoX2EpIHsgfVxuICAgICAgICAgICAgY29uc3QgZmxhdHRlbkZvcm1WYWx1ZXMgPSBmbGF0dGVuKGNvbnRyb2wuX2Zvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZmxhdHRlbkZvcm1WYWx1ZXMpIHtcbiAgICAgICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoa2V5LCBmbGF0dGVuRm9ybVZhbHVlc1trZXldKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChvblN1Ym1pdCkge1xuICAgICAgICAgICAgICAgIGF3YWl0IG9uU3VibWl0KHtcbiAgICAgICAgICAgICAgICAgICAgZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgZXZlbnQsXG4gICAgICAgICAgICAgICAgICAgIG1ldGhvZCxcbiAgICAgICAgICAgICAgICAgICAgZm9ybURhdGEsXG4gICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhSnNvbixcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChhY3Rpb24pIHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzaG91bGRTdHJpbmdpZnlTdWJtaXNzaW9uRGF0YSA9IFtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnMgJiYgaGVhZGVyc1snQ29udGVudC1UeXBlJ10sXG4gICAgICAgICAgICAgICAgICAgICAgICBlbmNUeXBlLFxuICAgICAgICAgICAgICAgICAgICBdLnNvbWUoKHZhbHVlKSA9PiB2YWx1ZSAmJiB2YWx1ZS5pbmNsdWRlcygnanNvbicpKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChTdHJpbmcoYWN0aW9uKSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgbWV0aG9kLFxuICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uKGVuY1R5cGUgPyB7ICdDb250ZW50LVR5cGUnOiBlbmNUeXBlIH0gOiB7fSksXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9keTogc2hvdWxkU3RyaW5naWZ5U3VibWlzc2lvbkRhdGEgPyBmb3JtRGF0YUpzb24gOiBmb3JtRGF0YSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgKHZhbGlkYXRlU3RhdHVzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAhdmFsaWRhdGVTdGF0dXMocmVzcG9uc2Uuc3RhdHVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmVzcG9uc2Uuc3RhdHVzIDwgMjAwIHx8IHJlc3BvbnNlLnN0YXR1cyA+PSAzMDApKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBoYXNFcnJvciA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkVycm9yICYmIG9uRXJyb3IoeyByZXNwb25zZSB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGUgPSBTdHJpbmcocmVzcG9uc2Uuc3RhdHVzKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uU3VjY2VzcyAmJiBvblN1Y2Nlc3MoeyByZXNwb25zZSB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgaGFzRXJyb3IgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICBvbkVycm9yICYmIG9uRXJyb3IoeyBlcnJvciB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pKGV2ZW50KTtcbiAgICAgICAgaWYgKGhhc0Vycm9yICYmIHByb3BzLmNvbnRyb2wpIHtcbiAgICAgICAgICAgIHByb3BzLmNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIGlzU3VibWl0U3VjY2Vzc2Z1bDogZmFsc2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHByb3BzLmNvbnRyb2wuc2V0RXJyb3IoJ3Jvb3Quc2VydmVyJywge1xuICAgICAgICAgICAgICAgIHR5cGUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgc2V0TW91bnRlZCh0cnVlKTtcbiAgICB9LCBbXSk7XG4gICAgcmV0dXJuIHJlbmRlciA/IChSZWFjdF9fZGVmYXVsdC5jcmVhdGVFbGVtZW50KFJlYWN0X19kZWZhdWx0LkZyYWdtZW50LCBudWxsLCByZW5kZXIoe1xuICAgICAgICBzdWJtaXQsXG4gICAgfSkpKSA6IChSZWFjdF9fZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwiZm9ybVwiLCB7IG5vVmFsaWRhdGU6IG1vdW50ZWQsIGFjdGlvbjogYWN0aW9uLCBtZXRob2Q6IG1ldGhvZCwgZW5jVHlwZTogZW5jVHlwZSwgb25TdWJtaXQ6IHN1Ym1pdCwgLi4ucmVzdCB9LCBjaGlsZHJlbikpO1xufVxuXG52YXIgYXBwZW5kRXJyb3JzID0gKG5hbWUsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgZXJyb3JzLCB0eXBlLCBtZXNzYWdlKSA9PiB2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWFcbiAgICA/IHtcbiAgICAgICAgLi4uZXJyb3JzW25hbWVdLFxuICAgICAgICB0eXBlczoge1xuICAgICAgICAgICAgLi4uKGVycm9yc1tuYW1lXSAmJiBlcnJvcnNbbmFtZV0udHlwZXMgPyBlcnJvcnNbbmFtZV0udHlwZXMgOiB7fSksXG4gICAgICAgICAgICBbdHlwZV06IG1lc3NhZ2UgfHwgdHJ1ZSxcbiAgICAgICAgfSxcbiAgICB9XG4gICAgOiB7fTtcblxudmFyIGNvbnZlcnRUb0FycmF5UGF5bG9hZCA9ICh2YWx1ZSkgPT4gKEFycmF5LmlzQXJyYXkodmFsdWUpID8gdmFsdWUgOiBbdmFsdWVdKTtcblxudmFyIGNyZWF0ZVN1YmplY3QgPSAoKSA9PiB7XG4gICAgbGV0IF9vYnNlcnZlcnMgPSBbXTtcbiAgICBjb25zdCBuZXh0ID0gKHZhbHVlKSA9PiB7XG4gICAgICAgIGZvciAoY29uc3Qgb2JzZXJ2ZXIgb2YgX29ic2VydmVycykge1xuICAgICAgICAgICAgb2JzZXJ2ZXIubmV4dCAmJiBvYnNlcnZlci5uZXh0KHZhbHVlKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3Qgc3Vic2NyaWJlID0gKG9ic2VydmVyKSA9PiB7XG4gICAgICAgIF9vYnNlcnZlcnMucHVzaChvYnNlcnZlcik7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB1bnN1YnNjcmliZTogKCkgPT4ge1xuICAgICAgICAgICAgICAgIF9vYnNlcnZlcnMgPSBfb2JzZXJ2ZXJzLmZpbHRlcigobykgPT4gbyAhPT0gb2JzZXJ2ZXIpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9O1xuICAgIGNvbnN0IHVuc3Vic2NyaWJlID0gKCkgPT4ge1xuICAgICAgICBfb2JzZXJ2ZXJzID0gW107XG4gICAgfTtcbiAgICByZXR1cm4ge1xuICAgICAgICBnZXQgb2JzZXJ2ZXJzKCkge1xuICAgICAgICAgICAgcmV0dXJuIF9vYnNlcnZlcnM7XG4gICAgICAgIH0sXG4gICAgICAgIG5leHQsXG4gICAgICAgIHN1YnNjcmliZSxcbiAgICAgICAgdW5zdWJzY3JpYmUsXG4gICAgfTtcbn07XG5cbnZhciBpc1ByaW1pdGl2ZSA9ICh2YWx1ZSkgPT4gaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpIHx8ICFpc09iamVjdFR5cGUodmFsdWUpO1xuXG5mdW5jdGlvbiBkZWVwRXF1YWwob2JqZWN0MSwgb2JqZWN0Mikge1xuICAgIGlmIChpc1ByaW1pdGl2ZShvYmplY3QxKSB8fCBpc1ByaW1pdGl2ZShvYmplY3QyKSkge1xuICAgICAgICByZXR1cm4gb2JqZWN0MSA9PT0gb2JqZWN0MjtcbiAgICB9XG4gICAgaWYgKGlzRGF0ZU9iamVjdChvYmplY3QxKSAmJiBpc0RhdGVPYmplY3Qob2JqZWN0MikpIHtcbiAgICAgICAgcmV0dXJuIG9iamVjdDEuZ2V0VGltZSgpID09PSBvYmplY3QyLmdldFRpbWUoKTtcbiAgICB9XG4gICAgY29uc3Qga2V5czEgPSBPYmplY3Qua2V5cyhvYmplY3QxKTtcbiAgICBjb25zdCBrZXlzMiA9IE9iamVjdC5rZXlzKG9iamVjdDIpO1xuICAgIGlmIChrZXlzMS5sZW5ndGggIT09IGtleXMyLmxlbmd0aCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGZvciAoY29uc3Qga2V5IG9mIGtleXMxKSB7XG4gICAgICAgIGNvbnN0IHZhbDEgPSBvYmplY3QxW2tleV07XG4gICAgICAgIGlmICgha2V5czIuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChrZXkgIT09ICdyZWYnKSB7XG4gICAgICAgICAgICBjb25zdCB2YWwyID0gb2JqZWN0MltrZXldO1xuICAgICAgICAgICAgaWYgKChpc0RhdGVPYmplY3QodmFsMSkgJiYgaXNEYXRlT2JqZWN0KHZhbDIpKSB8fFxuICAgICAgICAgICAgICAgIChpc09iamVjdCh2YWwxKSAmJiBpc09iamVjdCh2YWwyKSkgfHxcbiAgICAgICAgICAgICAgICAoQXJyYXkuaXNBcnJheSh2YWwxKSAmJiBBcnJheS5pc0FycmF5KHZhbDIpKVxuICAgICAgICAgICAgICAgID8gIWRlZXBFcXVhbCh2YWwxLCB2YWwyKVxuICAgICAgICAgICAgICAgIDogdmFsMSAhPT0gdmFsMikge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cblxudmFyIGlzRW1wdHlPYmplY3QgPSAodmFsdWUpID0+IGlzT2JqZWN0KHZhbHVlKSAmJiAhT2JqZWN0LmtleXModmFsdWUpLmxlbmd0aDtcblxudmFyIGlzRmlsZUlucHV0ID0gKGVsZW1lbnQpID0+IGVsZW1lbnQudHlwZSA9PT0gJ2ZpbGUnO1xuXG52YXIgaXNGdW5jdGlvbiA9ICh2YWx1ZSkgPT4gdHlwZW9mIHZhbHVlID09PSAnZnVuY3Rpb24nO1xuXG52YXIgaXNIVE1MRWxlbWVudCA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICghaXNXZWIpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBjb25zdCBvd25lciA9IHZhbHVlID8gdmFsdWUub3duZXJEb2N1bWVudCA6IDA7XG4gICAgcmV0dXJuICh2YWx1ZSBpbnN0YW5jZW9mXG4gICAgICAgIChvd25lciAmJiBvd25lci5kZWZhdWx0VmlldyA/IG93bmVyLmRlZmF1bHRWaWV3LkhUTUxFbGVtZW50IDogSFRNTEVsZW1lbnQpKTtcbn07XG5cbnZhciBpc011bHRpcGxlU2VsZWN0ID0gKGVsZW1lbnQpID0+IGVsZW1lbnQudHlwZSA9PT0gYHNlbGVjdC1tdWx0aXBsZWA7XG5cbnZhciBpc1JhZGlvSW5wdXQgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSAncmFkaW8nO1xuXG52YXIgaXNSYWRpb09yQ2hlY2tib3ggPSAocmVmKSA9PiBpc1JhZGlvSW5wdXQocmVmKSB8fCBpc0NoZWNrQm94SW5wdXQocmVmKTtcblxudmFyIGxpdmUgPSAocmVmKSA9PiBpc0hUTUxFbGVtZW50KHJlZikgJiYgcmVmLmlzQ29ubmVjdGVkO1xuXG5mdW5jdGlvbiBiYXNlR2V0KG9iamVjdCwgdXBkYXRlUGF0aCkge1xuICAgIGNvbnN0IGxlbmd0aCA9IHVwZGF0ZVBhdGguc2xpY2UoMCwgLTEpLmxlbmd0aDtcbiAgICBsZXQgaW5kZXggPSAwO1xuICAgIHdoaWxlIChpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICBvYmplY3QgPSBpc1VuZGVmaW5lZChvYmplY3QpID8gaW5kZXgrKyA6IG9iamVjdFt1cGRhdGVQYXRoW2luZGV4KytdXTtcbiAgICB9XG4gICAgcmV0dXJuIG9iamVjdDtcbn1cbmZ1bmN0aW9uIGlzRW1wdHlBcnJheShvYmopIHtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBvYmopIHtcbiAgICAgICAgaWYgKG9iai5oYXNPd25Qcm9wZXJ0eShrZXkpICYmICFpc1VuZGVmaW5lZChvYmpba2V5XSkpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cbmZ1bmN0aW9uIHVuc2V0KG9iamVjdCwgcGF0aCkge1xuICAgIGNvbnN0IHBhdGhzID0gQXJyYXkuaXNBcnJheShwYXRoKVxuICAgICAgICA/IHBhdGhcbiAgICAgICAgOiBpc0tleShwYXRoKVxuICAgICAgICAgICAgPyBbcGF0aF1cbiAgICAgICAgICAgIDogc3RyaW5nVG9QYXRoKHBhdGgpO1xuICAgIGNvbnN0IGNoaWxkT2JqZWN0ID0gcGF0aHMubGVuZ3RoID09PSAxID8gb2JqZWN0IDogYmFzZUdldChvYmplY3QsIHBhdGhzKTtcbiAgICBjb25zdCBpbmRleCA9IHBhdGhzLmxlbmd0aCAtIDE7XG4gICAgY29uc3Qga2V5ID0gcGF0aHNbaW5kZXhdO1xuICAgIGlmIChjaGlsZE9iamVjdCkge1xuICAgICAgICBkZWxldGUgY2hpbGRPYmplY3Rba2V5XTtcbiAgICB9XG4gICAgaWYgKGluZGV4ICE9PSAwICYmXG4gICAgICAgICgoaXNPYmplY3QoY2hpbGRPYmplY3QpICYmIGlzRW1wdHlPYmplY3QoY2hpbGRPYmplY3QpKSB8fFxuICAgICAgICAgICAgKEFycmF5LmlzQXJyYXkoY2hpbGRPYmplY3QpICYmIGlzRW1wdHlBcnJheShjaGlsZE9iamVjdCkpKSkge1xuICAgICAgICB1bnNldChvYmplY3QsIHBhdGhzLnNsaWNlKDAsIC0xKSk7XG4gICAgfVxuICAgIHJldHVybiBvYmplY3Q7XG59XG5cbnZhciBvYmplY3RIYXNGdW5jdGlvbiA9IChkYXRhKSA9PiB7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gZGF0YSkge1xuICAgICAgICBpZiAoaXNGdW5jdGlvbihkYXRhW2tleV0pKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59O1xuXG5mdW5jdGlvbiBtYXJrRmllbGRzRGlydHkoZGF0YSwgZmllbGRzID0ge30pIHtcbiAgICBjb25zdCBpc1BhcmVudE5vZGVBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSk7XG4gICAgaWYgKGlzT2JqZWN0KGRhdGEpIHx8IGlzUGFyZW50Tm9kZUFycmF5KSB7XG4gICAgICAgIGZvciAoY29uc3Qga2V5IGluIGRhdGEpIHtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGFba2V5XSkgfHxcbiAgICAgICAgICAgICAgICAoaXNPYmplY3QoZGF0YVtrZXldKSAmJiAhb2JqZWN0SGFzRnVuY3Rpb24oZGF0YVtrZXldKSkpIHtcbiAgICAgICAgICAgICAgICBmaWVsZHNba2V5XSA9IEFycmF5LmlzQXJyYXkoZGF0YVtrZXldKSA/IFtdIDoge307XG4gICAgICAgICAgICAgICAgbWFya0ZpZWxkc0RpcnR5KGRhdGFba2V5XSwgZmllbGRzW2tleV0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoIWlzTnVsbE9yVW5kZWZpbmVkKGRhdGFba2V5XSkpIHtcbiAgICAgICAgICAgICAgICBmaWVsZHNba2V5XSA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZpZWxkcztcbn1cbmZ1bmN0aW9uIGdldERpcnR5RmllbGRzRnJvbURlZmF1bHRWYWx1ZXMoZGF0YSwgZm9ybVZhbHVlcywgZGlydHlGaWVsZHNGcm9tVmFsdWVzKSB7XG4gICAgY29uc3QgaXNQYXJlbnROb2RlQXJyYXkgPSBBcnJheS5pc0FycmF5KGRhdGEpO1xuICAgIGlmIChpc09iamVjdChkYXRhKSB8fCBpc1BhcmVudE5vZGVBcnJheSkge1xuICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhW2tleV0pIHx8XG4gICAgICAgICAgICAgICAgKGlzT2JqZWN0KGRhdGFba2V5XSkgJiYgIW9iamVjdEhhc0Z1bmN0aW9uKGRhdGFba2V5XSkpKSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzVW5kZWZpbmVkKGZvcm1WYWx1ZXMpIHx8XG4gICAgICAgICAgICAgICAgICAgIGlzUHJpbWl0aXZlKGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldKSkge1xuICAgICAgICAgICAgICAgICAgICBkaXJ0eUZpZWxkc0Zyb21WYWx1ZXNba2V5XSA9IEFycmF5LmlzQXJyYXkoZGF0YVtrZXldKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBtYXJrRmllbGRzRGlydHkoZGF0YVtrZXldLCBbXSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDogeyAuLi5tYXJrRmllbGRzRGlydHkoZGF0YVtrZXldKSB9O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgZ2V0RGlydHlGaWVsZHNGcm9tRGVmYXVsdFZhbHVlcyhkYXRhW2tleV0sIGlzTnVsbE9yVW5kZWZpbmVkKGZvcm1WYWx1ZXMpID8ge30gOiBmb3JtVmFsdWVzW2tleV0sIGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBkaXJ0eUZpZWxkc0Zyb21WYWx1ZXNba2V5XSA9ICFkZWVwRXF1YWwoZGF0YVtrZXldLCBmb3JtVmFsdWVzW2tleV0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBkaXJ0eUZpZWxkc0Zyb21WYWx1ZXM7XG59XG52YXIgZ2V0RGlydHlGaWVsZHMgPSAoZGVmYXVsdFZhbHVlcywgZm9ybVZhbHVlcykgPT4gZ2V0RGlydHlGaWVsZHNGcm9tRGVmYXVsdFZhbHVlcyhkZWZhdWx0VmFsdWVzLCBmb3JtVmFsdWVzLCBtYXJrRmllbGRzRGlydHkoZm9ybVZhbHVlcykpO1xuXG5jb25zdCBkZWZhdWx0UmVzdWx0ID0ge1xuICAgIHZhbHVlOiBmYWxzZSxcbiAgICBpc1ZhbGlkOiBmYWxzZSxcbn07XG5jb25zdCB2YWxpZFJlc3VsdCA9IHsgdmFsdWU6IHRydWUsIGlzVmFsaWQ6IHRydWUgfTtcbnZhciBnZXRDaGVja2JveFZhbHVlID0gKG9wdGlvbnMpID0+IHtcbiAgICBpZiAoQXJyYXkuaXNBcnJheShvcHRpb25zKSkge1xuICAgICAgICBpZiAob3B0aW9ucy5sZW5ndGggPiAxKSB7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZXMgPSBvcHRpb25zXG4gICAgICAgICAgICAgICAgLmZpbHRlcigob3B0aW9uKSA9PiBvcHRpb24gJiYgb3B0aW9uLmNoZWNrZWQgJiYgIW9wdGlvbi5kaXNhYmxlZClcbiAgICAgICAgICAgICAgICAubWFwKChvcHRpb24pID0+IG9wdGlvbi52YWx1ZSk7XG4gICAgICAgICAgICByZXR1cm4geyB2YWx1ZTogdmFsdWVzLCBpc1ZhbGlkOiAhIXZhbHVlcy5sZW5ndGggfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb3B0aW9uc1swXS5jaGVja2VkICYmICFvcHRpb25zWzBdLmRpc2FibGVkXG4gICAgICAgICAgICA/IC8vIEB0cy1leHBlY3QtZXJyb3IgZXhwZWN0ZWQgdG8gd29yayBpbiB0aGUgYnJvd3NlclxuICAgICAgICAgICAgICAgIG9wdGlvbnNbMF0uYXR0cmlidXRlcyAmJiAhaXNVbmRlZmluZWQob3B0aW9uc1swXS5hdHRyaWJ1dGVzLnZhbHVlKVxuICAgICAgICAgICAgICAgICAgICA/IGlzVW5kZWZpbmVkKG9wdGlvbnNbMF0udmFsdWUpIHx8IG9wdGlvbnNbMF0udmFsdWUgPT09ICcnXG4gICAgICAgICAgICAgICAgICAgICAgICA/IHZhbGlkUmVzdWx0XG4gICAgICAgICAgICAgICAgICAgICAgICA6IHsgdmFsdWU6IG9wdGlvbnNbMF0udmFsdWUsIGlzVmFsaWQ6IHRydWUgfVxuICAgICAgICAgICAgICAgICAgICA6IHZhbGlkUmVzdWx0XG4gICAgICAgICAgICA6IGRlZmF1bHRSZXN1bHQ7XG4gICAgfVxuICAgIHJldHVybiBkZWZhdWx0UmVzdWx0O1xufTtcblxudmFyIGdldEZpZWxkVmFsdWVBcyA9ICh2YWx1ZSwgeyB2YWx1ZUFzTnVtYmVyLCB2YWx1ZUFzRGF0ZSwgc2V0VmFsdWVBcyB9KSA9PiBpc1VuZGVmaW5lZCh2YWx1ZSlcbiAgICA/IHZhbHVlXG4gICAgOiB2YWx1ZUFzTnVtYmVyXG4gICAgICAgID8gdmFsdWUgPT09ICcnXG4gICAgICAgICAgICA/IE5hTlxuICAgICAgICAgICAgOiB2YWx1ZVxuICAgICAgICAgICAgICAgID8gK3ZhbHVlXG4gICAgICAgICAgICAgICAgOiB2YWx1ZVxuICAgICAgICA6IHZhbHVlQXNEYXRlICYmIGlzU3RyaW5nKHZhbHVlKVxuICAgICAgICAgICAgPyBuZXcgRGF0ZSh2YWx1ZSlcbiAgICAgICAgICAgIDogc2V0VmFsdWVBc1xuICAgICAgICAgICAgICAgID8gc2V0VmFsdWVBcyh2YWx1ZSlcbiAgICAgICAgICAgICAgICA6IHZhbHVlO1xuXG5jb25zdCBkZWZhdWx0UmV0dXJuID0ge1xuICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgIHZhbHVlOiBudWxsLFxufTtcbnZhciBnZXRSYWRpb1ZhbHVlID0gKG9wdGlvbnMpID0+IEFycmF5LmlzQXJyYXkob3B0aW9ucylcbiAgICA/IG9wdGlvbnMucmVkdWNlKChwcmV2aW91cywgb3B0aW9uKSA9PiBvcHRpb24gJiYgb3B0aW9uLmNoZWNrZWQgJiYgIW9wdGlvbi5kaXNhYmxlZFxuICAgICAgICA/IHtcbiAgICAgICAgICAgIGlzVmFsaWQ6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogb3B0aW9uLnZhbHVlLFxuICAgICAgICB9XG4gICAgICAgIDogcHJldmlvdXMsIGRlZmF1bHRSZXR1cm4pXG4gICAgOiBkZWZhdWx0UmV0dXJuO1xuXG5mdW5jdGlvbiBnZXRGaWVsZFZhbHVlKF9mKSB7XG4gICAgY29uc3QgcmVmID0gX2YucmVmO1xuICAgIGlmIChpc0ZpbGVJbnB1dChyZWYpKSB7XG4gICAgICAgIHJldHVybiByZWYuZmlsZXM7XG4gICAgfVxuICAgIGlmIChpc1JhZGlvSW5wdXQocmVmKSkge1xuICAgICAgICByZXR1cm4gZ2V0UmFkaW9WYWx1ZShfZi5yZWZzKS52YWx1ZTtcbiAgICB9XG4gICAgaWYgKGlzTXVsdGlwbGVTZWxlY3QocmVmKSkge1xuICAgICAgICByZXR1cm4gWy4uLnJlZi5zZWxlY3RlZE9wdGlvbnNdLm1hcCgoeyB2YWx1ZSB9KSA9PiB2YWx1ZSk7XG4gICAgfVxuICAgIGlmIChpc0NoZWNrQm94SW5wdXQocmVmKSkge1xuICAgICAgICByZXR1cm4gZ2V0Q2hlY2tib3hWYWx1ZShfZi5yZWZzKS52YWx1ZTtcbiAgICB9XG4gICAgcmV0dXJuIGdldEZpZWxkVmFsdWVBcyhpc1VuZGVmaW5lZChyZWYudmFsdWUpID8gX2YucmVmLnZhbHVlIDogcmVmLnZhbHVlLCBfZik7XG59XG5cbnZhciBnZXRSZXNvbHZlck9wdGlvbnMgPSAoZmllbGRzTmFtZXMsIF9maWVsZHMsIGNyaXRlcmlhTW9kZSwgc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbikgPT4ge1xuICAgIGNvbnN0IGZpZWxkcyA9IHt9O1xuICAgIGZvciAoY29uc3QgbmFtZSBvZiBmaWVsZHNOYW1lcykge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgZmllbGQgJiYgc2V0KGZpZWxkcywgbmFtZSwgZmllbGQuX2YpO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICBjcml0ZXJpYU1vZGUsXG4gICAgICAgIG5hbWVzOiBbLi4uZmllbGRzTmFtZXNdLFxuICAgICAgICBmaWVsZHMsXG4gICAgICAgIHNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24sXG4gICAgfTtcbn07XG5cbnZhciBpc1JlZ2V4ID0gKHZhbHVlKSA9PiB2YWx1ZSBpbnN0YW5jZW9mIFJlZ0V4cDtcblxudmFyIGdldFJ1bGVWYWx1ZSA9IChydWxlKSA9PiBpc1VuZGVmaW5lZChydWxlKVxuICAgID8gcnVsZVxuICAgIDogaXNSZWdleChydWxlKVxuICAgICAgICA/IHJ1bGUuc291cmNlXG4gICAgICAgIDogaXNPYmplY3QocnVsZSlcbiAgICAgICAgICAgID8gaXNSZWdleChydWxlLnZhbHVlKVxuICAgICAgICAgICAgICAgID8gcnVsZS52YWx1ZS5zb3VyY2VcbiAgICAgICAgICAgICAgICA6IHJ1bGUudmFsdWVcbiAgICAgICAgICAgIDogcnVsZTtcblxudmFyIGdldFZhbGlkYXRpb25Nb2RlcyA9IChtb2RlKSA9PiAoe1xuICAgIGlzT25TdWJtaXQ6ICFtb2RlIHx8IG1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5vblN1Ym1pdCxcbiAgICBpc09uQmx1cjogbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uQmx1cixcbiAgICBpc09uQ2hhbmdlOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUub25DaGFuZ2UsXG4gICAgaXNPbkFsbDogbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLmFsbCxcbiAgICBpc09uVG91Y2g6IG1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5vblRvdWNoZWQsXG59KTtcblxuY29uc3QgQVNZTkNfRlVOQ1RJT04gPSAnQXN5bmNGdW5jdGlvbic7XG52YXIgaGFzUHJvbWlzZVZhbGlkYXRpb24gPSAoZmllbGRSZWZlcmVuY2UpID0+ICEhZmllbGRSZWZlcmVuY2UgJiZcbiAgICAhIWZpZWxkUmVmZXJlbmNlLnZhbGlkYXRlICYmXG4gICAgISEoKGlzRnVuY3Rpb24oZmllbGRSZWZlcmVuY2UudmFsaWRhdGUpICYmXG4gICAgICAgIGZpZWxkUmVmZXJlbmNlLnZhbGlkYXRlLmNvbnN0cnVjdG9yLm5hbWUgPT09IEFTWU5DX0ZVTkNUSU9OKSB8fFxuICAgICAgICAoaXNPYmplY3QoZmllbGRSZWZlcmVuY2UudmFsaWRhdGUpICYmXG4gICAgICAgICAgICBPYmplY3QudmFsdWVzKGZpZWxkUmVmZXJlbmNlLnZhbGlkYXRlKS5maW5kKCh2YWxpZGF0ZUZ1bmN0aW9uKSA9PiB2YWxpZGF0ZUZ1bmN0aW9uLmNvbnN0cnVjdG9yLm5hbWUgPT09IEFTWU5DX0ZVTkNUSU9OKSkpO1xuXG52YXIgaGFzVmFsaWRhdGlvbiA9IChvcHRpb25zKSA9PiBvcHRpb25zLm1vdW50ICYmXG4gICAgKG9wdGlvbnMucmVxdWlyZWQgfHxcbiAgICAgICAgb3B0aW9ucy5taW4gfHxcbiAgICAgICAgb3B0aW9ucy5tYXggfHxcbiAgICAgICAgb3B0aW9ucy5tYXhMZW5ndGggfHxcbiAgICAgICAgb3B0aW9ucy5taW5MZW5ndGggfHxcbiAgICAgICAgb3B0aW9ucy5wYXR0ZXJuIHx8XG4gICAgICAgIG9wdGlvbnMudmFsaWRhdGUpO1xuXG52YXIgaXNXYXRjaGVkID0gKG5hbWUsIF9uYW1lcywgaXNCbHVyRXZlbnQpID0+ICFpc0JsdXJFdmVudCAmJlxuICAgIChfbmFtZXMud2F0Y2hBbGwgfHxcbiAgICAgICAgX25hbWVzLndhdGNoLmhhcyhuYW1lKSB8fFxuICAgICAgICBbLi4uX25hbWVzLndhdGNoXS5zb21lKCh3YXRjaE5hbWUpID0+IG5hbWUuc3RhcnRzV2l0aCh3YXRjaE5hbWUpICYmXG4gICAgICAgICAgICAvXlxcLlxcdysvLnRlc3QobmFtZS5zbGljZSh3YXRjaE5hbWUubGVuZ3RoKSkpKTtcblxuY29uc3QgaXRlcmF0ZUZpZWxkc0J5QWN0aW9uID0gKGZpZWxkcywgYWN0aW9uLCBmaWVsZHNOYW1lcywgYWJvcnRFYXJseSkgPT4ge1xuICAgIGZvciAoY29uc3Qga2V5IG9mIGZpZWxkc05hbWVzIHx8IE9iamVjdC5rZXlzKGZpZWxkcykpIHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoZmllbGRzLCBrZXkpO1xuICAgICAgICBpZiAoZmllbGQpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgX2YsIC4uLmN1cnJlbnRGaWVsZCB9ID0gZmllbGQ7XG4gICAgICAgICAgICBpZiAoX2YpIHtcbiAgICAgICAgICAgICAgICBpZiAoX2YucmVmcyAmJiBfZi5yZWZzWzBdICYmIGFjdGlvbihfZi5yZWZzWzBdLCBrZXkpICYmICFhYm9ydEVhcmx5KSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChfZi5yZWYgJiYgYWN0aW9uKF9mLnJlZiwgX2YubmFtZSkgJiYgIWFib3J0RWFybHkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBpZiAoaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKGN1cnJlbnRGaWVsZCwgYWN0aW9uKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChpc09iamVjdChjdXJyZW50RmllbGQpKSB7XG4gICAgICAgICAgICAgICAgaWYgKGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihjdXJyZW50RmllbGQsIGFjdGlvbikpIHtcbiAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybjtcbn07XG5cbmZ1bmN0aW9uIHNjaGVtYUVycm9yTG9va3VwKGVycm9ycywgX2ZpZWxkcywgbmFtZSkge1xuICAgIGNvbnN0IGVycm9yID0gZ2V0KGVycm9ycywgbmFtZSk7XG4gICAgaWYgKGVycm9yIHx8IGlzS2V5KG5hbWUpKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBlcnJvcixcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgIH07XG4gICAgfVxuICAgIGNvbnN0IG5hbWVzID0gbmFtZS5zcGxpdCgnLicpO1xuICAgIHdoaWxlIChuYW1lcy5sZW5ndGgpIHtcbiAgICAgICAgY29uc3QgZmllbGROYW1lID0gbmFtZXMuam9pbignLicpO1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBmaWVsZE5hbWUpO1xuICAgICAgICBjb25zdCBmb3VuZEVycm9yID0gZ2V0KGVycm9ycywgZmllbGROYW1lKTtcbiAgICAgICAgaWYgKGZpZWxkICYmICFBcnJheS5pc0FycmF5KGZpZWxkKSAmJiBuYW1lICE9PSBmaWVsZE5hbWUpIHtcbiAgICAgICAgICAgIHJldHVybiB7IG5hbWUgfTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZm91bmRFcnJvciAmJiBmb3VuZEVycm9yLnR5cGUpIHtcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgbmFtZTogZmllbGROYW1lLFxuICAgICAgICAgICAgICAgIGVycm9yOiBmb3VuZEVycm9yLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICBuYW1lcy5wb3AoKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbmFtZSxcbiAgICB9O1xufVxuXG52YXIgc2hvdWxkUmVuZGVyRm9ybVN0YXRlID0gKGZvcm1TdGF0ZURhdGEsIF9wcm94eUZvcm1TdGF0ZSwgdXBkYXRlRm9ybVN0YXRlLCBpc1Jvb3QpID0+IHtcbiAgICB1cGRhdGVGb3JtU3RhdGUoZm9ybVN0YXRlRGF0YSk7XG4gICAgY29uc3QgeyBuYW1lLCAuLi5mb3JtU3RhdGUgfSA9IGZvcm1TdGF0ZURhdGE7XG4gICAgcmV0dXJuIChpc0VtcHR5T2JqZWN0KGZvcm1TdGF0ZSkgfHxcbiAgICAgICAgT2JqZWN0LmtleXMoZm9ybVN0YXRlKS5sZW5ndGggPj0gT2JqZWN0LmtleXMoX3Byb3h5Rm9ybVN0YXRlKS5sZW5ndGggfHxcbiAgICAgICAgT2JqZWN0LmtleXMoZm9ybVN0YXRlKS5maW5kKChrZXkpID0+IF9wcm94eUZvcm1TdGF0ZVtrZXldID09PVxuICAgICAgICAgICAgKCFpc1Jvb3QgfHwgVkFMSURBVElPTl9NT0RFLmFsbCkpKTtcbn07XG5cbnZhciBzaG91bGRTdWJzY3JpYmVCeU5hbWUgPSAobmFtZSwgc2lnbmFsTmFtZSwgZXhhY3QpID0+ICFuYW1lIHx8XG4gICAgIXNpZ25hbE5hbWUgfHxcbiAgICBuYW1lID09PSBzaWduYWxOYW1lIHx8XG4gICAgY29udmVydFRvQXJyYXlQYXlsb2FkKG5hbWUpLnNvbWUoKGN1cnJlbnROYW1lKSA9PiBjdXJyZW50TmFtZSAmJlxuICAgICAgICAoZXhhY3RcbiAgICAgICAgICAgID8gY3VycmVudE5hbWUgPT09IHNpZ25hbE5hbWVcbiAgICAgICAgICAgIDogY3VycmVudE5hbWUuc3RhcnRzV2l0aChzaWduYWxOYW1lKSB8fFxuICAgICAgICAgICAgICAgIHNpZ25hbE5hbWUuc3RhcnRzV2l0aChjdXJyZW50TmFtZSkpKTtcblxudmFyIHNraXBWYWxpZGF0aW9uID0gKGlzQmx1ckV2ZW50LCBpc1RvdWNoZWQsIGlzU3VibWl0dGVkLCByZVZhbGlkYXRlTW9kZSwgbW9kZSkgPT4ge1xuICAgIGlmIChtb2RlLmlzT25BbGwpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBlbHNlIGlmICghaXNTdWJtaXR0ZWQgJiYgbW9kZS5pc09uVG91Y2gpIHtcbiAgICAgICAgcmV0dXJuICEoaXNUb3VjaGVkIHx8IGlzQmx1ckV2ZW50KTtcbiAgICB9XG4gICAgZWxzZSBpZiAoaXNTdWJtaXR0ZWQgPyByZVZhbGlkYXRlTW9kZS5pc09uQmx1ciA6IG1vZGUuaXNPbkJsdXIpIHtcbiAgICAgICAgcmV0dXJuICFpc0JsdXJFdmVudDtcbiAgICB9XG4gICAgZWxzZSBpZiAoaXNTdWJtaXR0ZWQgPyByZVZhbGlkYXRlTW9kZS5pc09uQ2hhbmdlIDogbW9kZS5pc09uQ2hhbmdlKSB7XG4gICAgICAgIHJldHVybiBpc0JsdXJFdmVudDtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59O1xuXG52YXIgdW5zZXRFbXB0eUFycmF5ID0gKHJlZiwgbmFtZSkgPT4gIWNvbXBhY3QoZ2V0KHJlZiwgbmFtZSkpLmxlbmd0aCAmJiB1bnNldChyZWYsIG5hbWUpO1xuXG52YXIgdXBkYXRlRmllbGRBcnJheVJvb3RFcnJvciA9IChlcnJvcnMsIGVycm9yLCBuYW1lKSA9PiB7XG4gICAgY29uc3QgZmllbGRBcnJheUVycm9ycyA9IGNvbnZlcnRUb0FycmF5UGF5bG9hZChnZXQoZXJyb3JzLCBuYW1lKSk7XG4gICAgc2V0KGZpZWxkQXJyYXlFcnJvcnMsICdyb290JywgZXJyb3JbbmFtZV0pO1xuICAgIHNldChlcnJvcnMsIG5hbWUsIGZpZWxkQXJyYXlFcnJvcnMpO1xuICAgIHJldHVybiBlcnJvcnM7XG59O1xuXG52YXIgaXNNZXNzYWdlID0gKHZhbHVlKSA9PiBpc1N0cmluZyh2YWx1ZSk7XG5cbmZ1bmN0aW9uIGdldFZhbGlkYXRlRXJyb3IocmVzdWx0LCByZWYsIHR5cGUgPSAndmFsaWRhdGUnKSB7XG4gICAgaWYgKGlzTWVzc2FnZShyZXN1bHQpIHx8XG4gICAgICAgIChBcnJheS5pc0FycmF5KHJlc3VsdCkgJiYgcmVzdWx0LmV2ZXJ5KGlzTWVzc2FnZSkpIHx8XG4gICAgICAgIChpc0Jvb2xlYW4ocmVzdWx0KSAmJiAhcmVzdWx0KSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdHlwZSxcbiAgICAgICAgICAgIG1lc3NhZ2U6IGlzTWVzc2FnZShyZXN1bHQpID8gcmVzdWx0IDogJycsXG4gICAgICAgICAgICByZWYsXG4gICAgICAgIH07XG4gICAgfVxufVxuXG52YXIgZ2V0VmFsdWVBbmRNZXNzYWdlID0gKHZhbGlkYXRpb25EYXRhKSA9PiBpc09iamVjdCh2YWxpZGF0aW9uRGF0YSkgJiYgIWlzUmVnZXgodmFsaWRhdGlvbkRhdGEpXG4gICAgPyB2YWxpZGF0aW9uRGF0YVxuICAgIDoge1xuICAgICAgICB2YWx1ZTogdmFsaWRhdGlvbkRhdGEsXG4gICAgICAgIG1lc3NhZ2U6ICcnLFxuICAgIH07XG5cbnZhciB2YWxpZGF0ZUZpZWxkID0gYXN5bmMgKGZpZWxkLCBkaXNhYmxlZEZpZWxkTmFtZXMsIGZvcm1WYWx1ZXMsIHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSwgc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbiwgaXNGaWVsZEFycmF5KSA9PiB7XG4gICAgY29uc3QgeyByZWYsIHJlZnMsIHJlcXVpcmVkLCBtYXhMZW5ndGgsIG1pbkxlbmd0aCwgbWluLCBtYXgsIHBhdHRlcm4sIHZhbGlkYXRlLCBuYW1lLCB2YWx1ZUFzTnVtYmVyLCBtb3VudCwgfSA9IGZpZWxkLl9mO1xuICAgIGNvbnN0IGlucHV0VmFsdWUgPSBnZXQoZm9ybVZhbHVlcywgbmFtZSk7XG4gICAgaWYgKCFtb3VudCB8fCBkaXNhYmxlZEZpZWxkTmFtZXMuaGFzKG5hbWUpKSB7XG4gICAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gICAgY29uc3QgaW5wdXRSZWYgPSByZWZzID8gcmVmc1swXSA6IHJlZjtcbiAgICBjb25zdCBzZXRDdXN0b21WYWxpZGl0eSA9IChtZXNzYWdlKSA9PiB7XG4gICAgICAgIGlmIChzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uICYmIGlucHV0UmVmLnJlcG9ydFZhbGlkaXR5KSB7XG4gICAgICAgICAgICBpbnB1dFJlZi5zZXRDdXN0b21WYWxpZGl0eShpc0Jvb2xlYW4obWVzc2FnZSkgPyAnJyA6IG1lc3NhZ2UgfHwgJycpO1xuICAgICAgICAgICAgaW5wdXRSZWYucmVwb3J0VmFsaWRpdHkoKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgZXJyb3IgPSB7fTtcbiAgICBjb25zdCBpc1JhZGlvID0gaXNSYWRpb0lucHV0KHJlZik7XG4gICAgY29uc3QgaXNDaGVja0JveCA9IGlzQ2hlY2tCb3hJbnB1dChyZWYpO1xuICAgIGNvbnN0IGlzUmFkaW9PckNoZWNrYm94ID0gaXNSYWRpbyB8fCBpc0NoZWNrQm94O1xuICAgIGNvbnN0IGlzRW1wdHkgPSAoKHZhbHVlQXNOdW1iZXIgfHwgaXNGaWxlSW5wdXQocmVmKSkgJiZcbiAgICAgICAgaXNVbmRlZmluZWQocmVmLnZhbHVlKSAmJlxuICAgICAgICBpc1VuZGVmaW5lZChpbnB1dFZhbHVlKSkgfHxcbiAgICAgICAgKGlzSFRNTEVsZW1lbnQocmVmKSAmJiByZWYudmFsdWUgPT09ICcnKSB8fFxuICAgICAgICBpbnB1dFZhbHVlID09PSAnJyB8fFxuICAgICAgICAoQXJyYXkuaXNBcnJheShpbnB1dFZhbHVlKSAmJiAhaW5wdXRWYWx1ZS5sZW5ndGgpO1xuICAgIGNvbnN0IGFwcGVuZEVycm9yc0N1cnJ5ID0gYXBwZW5kRXJyb3JzLmJpbmQobnVsbCwgbmFtZSwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBlcnJvcik7XG4gICAgY29uc3QgZ2V0TWluTWF4TWVzc2FnZSA9IChleGNlZWRNYXgsIG1heExlbmd0aE1lc3NhZ2UsIG1pbkxlbmd0aE1lc3NhZ2UsIG1heFR5cGUgPSBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLm1heExlbmd0aCwgbWluVHlwZSA9IElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWluTGVuZ3RoKSA9PiB7XG4gICAgICAgIGNvbnN0IG1lc3NhZ2UgPSBleGNlZWRNYXggPyBtYXhMZW5ndGhNZXNzYWdlIDogbWluTGVuZ3RoTWVzc2FnZTtcbiAgICAgICAgZXJyb3JbbmFtZV0gPSB7XG4gICAgICAgICAgICB0eXBlOiBleGNlZWRNYXggPyBtYXhUeXBlIDogbWluVHlwZSxcbiAgICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgICAgICByZWYsXG4gICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShleGNlZWRNYXggPyBtYXhUeXBlIDogbWluVHlwZSwgbWVzc2FnZSksXG4gICAgICAgIH07XG4gICAgfTtcbiAgICBpZiAoaXNGaWVsZEFycmF5XG4gICAgICAgID8gIUFycmF5LmlzQXJyYXkoaW5wdXRWYWx1ZSkgfHwgIWlucHV0VmFsdWUubGVuZ3RoXG4gICAgICAgIDogcmVxdWlyZWQgJiZcbiAgICAgICAgICAgICgoIWlzUmFkaW9PckNoZWNrYm94ICYmIChpc0VtcHR5IHx8IGlzTnVsbE9yVW5kZWZpbmVkKGlucHV0VmFsdWUpKSkgfHxcbiAgICAgICAgICAgICAgICAoaXNCb29sZWFuKGlucHV0VmFsdWUpICYmICFpbnB1dFZhbHVlKSB8fFxuICAgICAgICAgICAgICAgIChpc0NoZWNrQm94ICYmICFnZXRDaGVja2JveFZhbHVlKHJlZnMpLmlzVmFsaWQpIHx8XG4gICAgICAgICAgICAgICAgKGlzUmFkaW8gJiYgIWdldFJhZGlvVmFsdWUocmVmcykuaXNWYWxpZCkpKSB7XG4gICAgICAgIGNvbnN0IHsgdmFsdWUsIG1lc3NhZ2UgfSA9IGlzTWVzc2FnZShyZXF1aXJlZClcbiAgICAgICAgICAgID8geyB2YWx1ZTogISFyZXF1aXJlZCwgbWVzc2FnZTogcmVxdWlyZWQgfVxuICAgICAgICAgICAgOiBnZXRWYWx1ZUFuZE1lc3NhZ2UocmVxdWlyZWQpO1xuICAgICAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgIHR5cGU6IElOUFVUX1ZBTElEQVRJT05fUlVMRVMucmVxdWlyZWQsXG4gICAgICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgICAgICByZWY6IGlucHV0UmVmLFxuICAgICAgICAgICAgICAgIC4uLmFwcGVuZEVycm9yc0N1cnJ5KElOUFVUX1ZBTElEQVRJT05fUlVMRVMucmVxdWlyZWQsIG1lc3NhZ2UpLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkobWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmICghaXNFbXB0eSAmJiAoIWlzTnVsbE9yVW5kZWZpbmVkKG1pbikgfHwgIWlzTnVsbE9yVW5kZWZpbmVkKG1heCkpKSB7XG4gICAgICAgIGxldCBleGNlZWRNYXg7XG4gICAgICAgIGxldCBleGNlZWRNaW47XG4gICAgICAgIGNvbnN0IG1heE91dHB1dCA9IGdldFZhbHVlQW5kTWVzc2FnZShtYXgpO1xuICAgICAgICBjb25zdCBtaW5PdXRwdXQgPSBnZXRWYWx1ZUFuZE1lc3NhZ2UobWluKTtcbiAgICAgICAgaWYgKCFpc051bGxPclVuZGVmaW5lZChpbnB1dFZhbHVlKSAmJiAhaXNOYU4oaW5wdXRWYWx1ZSkpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlTnVtYmVyID0gcmVmLnZhbHVlQXNOdW1iZXIgfHxcbiAgICAgICAgICAgICAgICAoaW5wdXRWYWx1ZSA/ICtpbnB1dFZhbHVlIDogaW5wdXRWYWx1ZSk7XG4gICAgICAgICAgICBpZiAoIWlzTnVsbE9yVW5kZWZpbmVkKG1heE91dHB1dC52YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBleGNlZWRNYXggPSB2YWx1ZU51bWJlciA+IG1heE91dHB1dC52YWx1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghaXNOdWxsT3JVbmRlZmluZWQobWluT3V0cHV0LnZhbHVlKSkge1xuICAgICAgICAgICAgICAgIGV4Y2VlZE1pbiA9IHZhbHVlTnVtYmVyIDwgbWluT3V0cHV0LnZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgdmFsdWVEYXRlID0gcmVmLnZhbHVlQXNEYXRlIHx8IG5ldyBEYXRlKGlucHV0VmFsdWUpO1xuICAgICAgICAgICAgY29uc3QgY29udmVydFRpbWVUb0RhdGUgPSAodGltZSkgPT4gbmV3IERhdGUobmV3IERhdGUoKS50b0RhdGVTdHJpbmcoKSArICcgJyArIHRpbWUpO1xuICAgICAgICAgICAgY29uc3QgaXNUaW1lID0gcmVmLnR5cGUgPT0gJ3RpbWUnO1xuICAgICAgICAgICAgY29uc3QgaXNXZWVrID0gcmVmLnR5cGUgPT0gJ3dlZWsnO1xuICAgICAgICAgICAgaWYgKGlzU3RyaW5nKG1heE91dHB1dC52YWx1ZSkgJiYgaW5wdXRWYWx1ZSkge1xuICAgICAgICAgICAgICAgIGV4Y2VlZE1heCA9IGlzVGltZVxuICAgICAgICAgICAgICAgICAgICA/IGNvbnZlcnRUaW1lVG9EYXRlKGlucHV0VmFsdWUpID4gY29udmVydFRpbWVUb0RhdGUobWF4T3V0cHV0LnZhbHVlKVxuICAgICAgICAgICAgICAgICAgICA6IGlzV2Vla1xuICAgICAgICAgICAgICAgICAgICAgICAgPyBpbnB1dFZhbHVlID4gbWF4T3V0cHV0LnZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHZhbHVlRGF0ZSA+IG5ldyBEYXRlKG1heE91dHB1dC52YWx1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNTdHJpbmcobWluT3V0cHV0LnZhbHVlKSAmJiBpbnB1dFZhbHVlKSB7XG4gICAgICAgICAgICAgICAgZXhjZWVkTWluID0gaXNUaW1lXG4gICAgICAgICAgICAgICAgICAgID8gY29udmVydFRpbWVUb0RhdGUoaW5wdXRWYWx1ZSkgPCBjb252ZXJ0VGltZVRvRGF0ZShtaW5PdXRwdXQudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgIDogaXNXZWVrXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGlucHV0VmFsdWUgPCBtaW5PdXRwdXQudmFsdWVcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdmFsdWVEYXRlIDwgbmV3IERhdGUobWluT3V0cHV0LnZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoZXhjZWVkTWF4IHx8IGV4Y2VlZE1pbikge1xuICAgICAgICAgICAgZ2V0TWluTWF4TWVzc2FnZSghIWV4Y2VlZE1heCwgbWF4T3V0cHV0Lm1lc3NhZ2UsIG1pbk91dHB1dC5tZXNzYWdlLCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLm1heCwgSU5QVVRfVkFMSURBVElPTl9SVUxFUy5taW4pO1xuICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICBzZXRDdXN0b21WYWxpZGl0eShlcnJvcltuYW1lXS5tZXNzYWdlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKChtYXhMZW5ndGggfHwgbWluTGVuZ3RoKSAmJlxuICAgICAgICAhaXNFbXB0eSAmJlxuICAgICAgICAoaXNTdHJpbmcoaW5wdXRWYWx1ZSkgfHwgKGlzRmllbGRBcnJheSAmJiBBcnJheS5pc0FycmF5KGlucHV0VmFsdWUpKSkpIHtcbiAgICAgICAgY29uc3QgbWF4TGVuZ3RoT3V0cHV0ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1heExlbmd0aCk7XG4gICAgICAgIGNvbnN0IG1pbkxlbmd0aE91dHB1dCA9IGdldFZhbHVlQW5kTWVzc2FnZShtaW5MZW5ndGgpO1xuICAgICAgICBjb25zdCBleGNlZWRNYXggPSAhaXNOdWxsT3JVbmRlZmluZWQobWF4TGVuZ3RoT3V0cHV0LnZhbHVlKSAmJlxuICAgICAgICAgICAgaW5wdXRWYWx1ZS5sZW5ndGggPiArbWF4TGVuZ3RoT3V0cHV0LnZhbHVlO1xuICAgICAgICBjb25zdCBleGNlZWRNaW4gPSAhaXNOdWxsT3JVbmRlZmluZWQobWluTGVuZ3RoT3V0cHV0LnZhbHVlKSAmJlxuICAgICAgICAgICAgaW5wdXRWYWx1ZS5sZW5ndGggPCArbWluTGVuZ3RoT3V0cHV0LnZhbHVlO1xuICAgICAgICBpZiAoZXhjZWVkTWF4IHx8IGV4Y2VlZE1pbikge1xuICAgICAgICAgICAgZ2V0TWluTWF4TWVzc2FnZShleGNlZWRNYXgsIG1heExlbmd0aE91dHB1dC5tZXNzYWdlLCBtaW5MZW5ndGhPdXRwdXQubWVzc2FnZSk7XG4gICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5KGVycm9yW25hbWVdLm1lc3NhZ2UpO1xuICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAocGF0dGVybiAmJiAhaXNFbXB0eSAmJiBpc1N0cmluZyhpbnB1dFZhbHVlKSkge1xuICAgICAgICBjb25zdCB7IHZhbHVlOiBwYXR0ZXJuVmFsdWUsIG1lc3NhZ2UgfSA9IGdldFZhbHVlQW5kTWVzc2FnZShwYXR0ZXJuKTtcbiAgICAgICAgaWYgKGlzUmVnZXgocGF0dGVyblZhbHVlKSAmJiAhaW5wdXRWYWx1ZS5tYXRjaChwYXR0ZXJuVmFsdWUpKSB7XG4gICAgICAgICAgICBlcnJvcltuYW1lXSA9IHtcbiAgICAgICAgICAgICAgICB0eXBlOiBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnBhdHRlcm4sXG4gICAgICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgICAgICByZWYsXG4gICAgICAgICAgICAgICAgLi4uYXBwZW5kRXJyb3JzQ3VycnkoSU5QVVRfVkFMSURBVElPTl9SVUxFUy5wYXR0ZXJuLCBtZXNzYWdlKSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5KG1lc3NhZ2UpO1xuICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAodmFsaWRhdGUpIHtcbiAgICAgICAgaWYgKGlzRnVuY3Rpb24odmFsaWRhdGUpKSB7XG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB2YWxpZGF0ZShpbnB1dFZhbHVlLCBmb3JtVmFsdWVzKTtcbiAgICAgICAgICAgIGNvbnN0IHZhbGlkYXRlRXJyb3IgPSBnZXRWYWxpZGF0ZUVycm9yKHJlc3VsdCwgaW5wdXRSZWYpO1xuICAgICAgICAgICAgaWYgKHZhbGlkYXRlRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBlcnJvcltuYW1lXSA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4udmFsaWRhdGVFcnJvcixcbiAgICAgICAgICAgICAgICAgICAgLi4uYXBwZW5kRXJyb3JzQ3VycnkoSU5QVVRfVkFMSURBVElPTl9SVUxFUy52YWxpZGF0ZSwgdmFsaWRhdGVFcnJvci5tZXNzYWdlKSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5KHZhbGlkYXRlRXJyb3IubWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoaXNPYmplY3QodmFsaWRhdGUpKSB7XG4gICAgICAgICAgICBsZXQgdmFsaWRhdGlvblJlc3VsdCA9IHt9O1xuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gdmFsaWRhdGUpIHtcbiAgICAgICAgICAgICAgICBpZiAoIWlzRW1wdHlPYmplY3QodmFsaWRhdGlvblJlc3VsdCkgJiYgIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgdmFsaWRhdGVFcnJvciA9IGdldFZhbGlkYXRlRXJyb3IoYXdhaXQgdmFsaWRhdGVba2V5XShpbnB1dFZhbHVlLCBmb3JtVmFsdWVzKSwgaW5wdXRSZWYsIGtleSk7XG4gICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlRXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsaWRhdGlvblJlc3VsdCA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLnZhbGlkYXRlRXJyb3IsXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShrZXksIHZhbGlkYXRlRXJyb3IubWVzc2FnZSksXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5KHZhbGlkYXRlRXJyb3IubWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yW25hbWVdID0gdmFsaWRhdGlvblJlc3VsdDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghaXNFbXB0eU9iamVjdCh2YWxpZGF0aW9uUmVzdWx0KSkge1xuICAgICAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgICAgICByZWY6IGlucHV0UmVmLFxuICAgICAgICAgICAgICAgICAgICAuLi52YWxpZGF0aW9uUmVzdWx0LFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBzZXRDdXN0b21WYWxpZGl0eSh0cnVlKTtcbiAgICByZXR1cm4gZXJyb3I7XG59O1xuXG5jb25zdCBkZWZhdWx0T3B0aW9ucyA9IHtcbiAgICBtb2RlOiBWQUxJREFUSU9OX01PREUub25TdWJtaXQsXG4gICAgcmVWYWxpZGF0ZU1vZGU6IFZBTElEQVRJT05fTU9ERS5vbkNoYW5nZSxcbiAgICBzaG91bGRGb2N1c0Vycm9yOiB0cnVlLFxufTtcbmZ1bmN0aW9uIGNyZWF0ZUZvcm1Db250cm9sKHByb3BzID0ge30pIHtcbiAgICBsZXQgX29wdGlvbnMgPSB7XG4gICAgICAgIC4uLmRlZmF1bHRPcHRpb25zLFxuICAgICAgICAuLi5wcm9wcyxcbiAgICB9O1xuICAgIGxldCBfZm9ybVN0YXRlID0ge1xuICAgICAgICBzdWJtaXRDb3VudDogMCxcbiAgICAgICAgaXNEaXJ0eTogZmFsc2UsXG4gICAgICAgIGlzUmVhZHk6IGZhbHNlLFxuICAgICAgICBpc0xvYWRpbmc6IGlzRnVuY3Rpb24oX29wdGlvbnMuZGVmYXVsdFZhbHVlcyksXG4gICAgICAgIGlzVmFsaWRhdGluZzogZmFsc2UsXG4gICAgICAgIGlzU3VibWl0dGVkOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXRTdWNjZXNzZnVsOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIHRvdWNoZWRGaWVsZHM6IHt9LFxuICAgICAgICBkaXJ0eUZpZWxkczoge30sXG4gICAgICAgIHZhbGlkYXRpbmdGaWVsZHM6IHt9LFxuICAgICAgICBlcnJvcnM6IF9vcHRpb25zLmVycm9ycyB8fCB7fSxcbiAgICAgICAgZGlzYWJsZWQ6IF9vcHRpb25zLmRpc2FibGVkIHx8IGZhbHNlLFxuICAgIH07XG4gICAgY29uc3QgX2ZpZWxkcyA9IHt9O1xuICAgIGxldCBfZGVmYXVsdFZhbHVlcyA9IGlzT2JqZWN0KF9vcHRpb25zLmRlZmF1bHRWYWx1ZXMpIHx8IGlzT2JqZWN0KF9vcHRpb25zLnZhbHVlcylcbiAgICAgICAgPyBjbG9uZU9iamVjdChfb3B0aW9ucy5kZWZhdWx0VmFsdWVzIHx8IF9vcHRpb25zLnZhbHVlcykgfHwge31cbiAgICAgICAgOiB7fTtcbiAgICBsZXQgX2Zvcm1WYWx1ZXMgPSBfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyXG4gICAgICAgID8ge31cbiAgICAgICAgOiBjbG9uZU9iamVjdChfZGVmYXVsdFZhbHVlcyk7XG4gICAgbGV0IF9zdGF0ZSA9IHtcbiAgICAgICAgYWN0aW9uOiBmYWxzZSxcbiAgICAgICAgbW91bnQ6IGZhbHNlLFxuICAgICAgICB3YXRjaDogZmFsc2UsXG4gICAgfTtcbiAgICBsZXQgX25hbWVzID0ge1xuICAgICAgICBtb3VudDogbmV3IFNldCgpLFxuICAgICAgICBkaXNhYmxlZDogbmV3IFNldCgpLFxuICAgICAgICB1bk1vdW50OiBuZXcgU2V0KCksXG4gICAgICAgIGFycmF5OiBuZXcgU2V0KCksXG4gICAgICAgIHdhdGNoOiBuZXcgU2V0KCksXG4gICAgfTtcbiAgICBsZXQgZGVsYXlFcnJvckNhbGxiYWNrO1xuICAgIGxldCB0aW1lciA9IDA7XG4gICAgY29uc3QgX3Byb3h5Rm9ybVN0YXRlID0ge1xuICAgICAgICBpc0RpcnR5OiBmYWxzZSxcbiAgICAgICAgZGlydHlGaWVsZHM6IGZhbHNlLFxuICAgICAgICB2YWxpZGF0aW5nRmllbGRzOiBmYWxzZSxcbiAgICAgICAgdG91Y2hlZEZpZWxkczogZmFsc2UsXG4gICAgICAgIGlzVmFsaWRhdGluZzogZmFsc2UsXG4gICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICBlcnJvcnM6IGZhbHNlLFxuICAgIH07XG4gICAgbGV0IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZSA9IHtcbiAgICAgICAgLi4uX3Byb3h5Rm9ybVN0YXRlLFxuICAgIH07XG4gICAgY29uc3QgX3N1YmplY3RzID0ge1xuICAgICAgICBhcnJheTogY3JlYXRlU3ViamVjdCgpLFxuICAgICAgICBzdGF0ZTogY3JlYXRlU3ViamVjdCgpLFxuICAgIH07XG4gICAgY29uc3Qgc2hvdWxkRGlzcGxheUFsbEFzc29jaWF0ZWRFcnJvcnMgPSBfb3B0aW9ucy5jcml0ZXJpYU1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5hbGw7XG4gICAgY29uc3QgZGVib3VuY2UgPSAoY2FsbGJhY2spID0+ICh3YWl0KSA9PiB7XG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgICAgIHRpbWVyID0gc2V0VGltZW91dChjYWxsYmFjaywgd2FpdCk7XG4gICAgfTtcbiAgICBjb25zdCBfc2V0VmFsaWQgPSBhc3luYyAoc2hvdWxkVXBkYXRlVmFsaWQpID0+IHtcbiAgICAgICAgaWYgKCFfb3B0aW9ucy5kaXNhYmxlZCAmJlxuICAgICAgICAgICAgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVWYWxpZCkpIHtcbiAgICAgICAgICAgIGNvbnN0IGlzVmFsaWQgPSBfb3B0aW9ucy5yZXNvbHZlclxuICAgICAgICAgICAgICAgID8gaXNFbXB0eU9iamVjdCgoYXdhaXQgX3J1blNjaGVtYSgpKS5lcnJvcnMpXG4gICAgICAgICAgICAgICAgOiBhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oX2ZpZWxkcywgdHJ1ZSk7XG4gICAgICAgICAgICBpZiAoaXNWYWxpZCAhPT0gX2Zvcm1TdGF0ZS5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfdXBkYXRlSXNWYWxpZGF0aW5nID0gKG5hbWVzLCBpc1ZhbGlkYXRpbmcpID0+IHtcbiAgICAgICAgaWYgKCFfb3B0aW9ucy5kaXNhYmxlZCAmJlxuICAgICAgICAgICAgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkYXRpbmcgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkYXRpbmcgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcykpIHtcbiAgICAgICAgICAgIChuYW1lcyB8fCBBcnJheS5mcm9tKF9uYW1lcy5tb3VudCkpLmZvckVhY2goKG5hbWUpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAobmFtZSkge1xuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkYXRpbmdcbiAgICAgICAgICAgICAgICAgICAgICAgID8gc2V0KF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcywgbmFtZSwgaXNWYWxpZGF0aW5nKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB1bnNldChfZm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIHZhbGlkYXRpbmdGaWVsZHM6IF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcyxcbiAgICAgICAgICAgICAgICBpc1ZhbGlkYXRpbmc6ICFpc0VtcHR5T2JqZWN0KF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcyksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgX3NldEZpZWxkQXJyYXkgPSAobmFtZSwgdmFsdWVzID0gW10sIG1ldGhvZCwgYXJncywgc2hvdWxkU2V0VmFsdWVzID0gdHJ1ZSwgc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgPSB0cnVlKSA9PiB7XG4gICAgICAgIGlmIChhcmdzICYmIG1ldGhvZCAmJiAhX29wdGlvbnMuZGlzYWJsZWQpIHtcbiAgICAgICAgICAgIF9zdGF0ZS5hY3Rpb24gPSB0cnVlO1xuICAgICAgICAgICAgaWYgKHNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlICYmIEFycmF5LmlzQXJyYXkoZ2V0KF9maWVsZHMsIG5hbWUpKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWVzID0gbWV0aG9kKGdldChfZmllbGRzLCBuYW1lKSwgYXJncy5hcmdBLCBhcmdzLmFyZ0IpO1xuICAgICAgICAgICAgICAgIHNob3VsZFNldFZhbHVlcyAmJiBzZXQoX2ZpZWxkcywgbmFtZSwgZmllbGRWYWx1ZXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlICYmXG4gICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9ycyA9IG1ldGhvZChnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpLCBhcmdzLmFyZ0EsIGFyZ3MuYXJnQik7XG4gICAgICAgICAgICAgICAgc2hvdWxkU2V0VmFsdWVzICYmIHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3JzKTtcbiAgICAgICAgICAgICAgICB1bnNldEVtcHR5QXJyYXkoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKChfcHJveHlGb3JtU3RhdGUudG91Y2hlZEZpZWxkcyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzKSAmJlxuICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlICYmXG4gICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShnZXQoX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lKSkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB0b3VjaGVkRmllbGRzID0gbWV0aG9kKGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpLCBhcmdzLmFyZ0EsIGFyZ3MuYXJnQik7XG4gICAgICAgICAgICAgICAgc2hvdWxkU2V0VmFsdWVzICYmIHNldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUsIHRvdWNoZWRGaWVsZHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKF9wcm94eUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcyB8fCBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuZGlydHlGaWVsZHMpIHtcbiAgICAgICAgICAgICAgICBfZm9ybVN0YXRlLmRpcnR5RmllbGRzID0gZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIF9mb3JtVmFsdWVzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgIGlzRGlydHk6IF9nZXREaXJ0eShuYW1lLCB2YWx1ZXMpLFxuICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzOiBfZm9ybVN0YXRlLmRpcnR5RmllbGRzLFxuICAgICAgICAgICAgICAgIGVycm9yczogX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgICAgICAgICAgaXNWYWxpZDogX2Zvcm1TdGF0ZS5pc1ZhbGlkLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIHZhbHVlcyk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZUVycm9ycyA9IChuYW1lLCBlcnJvcikgPT4ge1xuICAgICAgICBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIGVycm9yKTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBfc2V0RXJyb3JzID0gKGVycm9ycykgPT4ge1xuICAgICAgICBfZm9ybVN0YXRlLmVycm9ycyA9IGVycm9ycztcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZVZhbGlkQW5kVmFsdWUgPSAobmFtZSwgc2hvdWxkU2tpcFNldFZhbHVlQXMsIHZhbHVlLCByZWYpID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgZGVmYXVsdFZhbHVlID0gZ2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBpc1VuZGVmaW5lZCh2YWx1ZSkgPyBnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpIDogdmFsdWUpO1xuICAgICAgICAgICAgaXNVbmRlZmluZWQoZGVmYXVsdFZhbHVlKSB8fFxuICAgICAgICAgICAgICAgIChyZWYgJiYgcmVmLmRlZmF1bHRDaGVja2VkKSB8fFxuICAgICAgICAgICAgICAgIHNob3VsZFNraXBTZXRWYWx1ZUFzXG4gICAgICAgICAgICAgICAgPyBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIHNob3VsZFNraXBTZXRWYWx1ZUFzID8gZGVmYXVsdFZhbHVlIDogZ2V0RmllbGRWYWx1ZShmaWVsZC5fZikpXG4gICAgICAgICAgICAgICAgOiBzZXRGaWVsZFZhbHVlKG5hbWUsIGRlZmF1bHRWYWx1ZSk7XG4gICAgICAgICAgICBfc3RhdGUubW91bnQgJiYgX3NldFZhbGlkKCk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZVRvdWNoQW5kRGlydHkgPSAobmFtZSwgZmllbGRWYWx1ZSwgaXNCbHVyRXZlbnQsIHNob3VsZERpcnR5LCBzaG91bGRSZW5kZXIpID0+IHtcbiAgICAgICAgbGV0IHNob3VsZFVwZGF0ZUZpZWxkID0gZmFsc2U7XG4gICAgICAgIGxldCBpc1ByZXZpb3VzRGlydHkgPSBmYWxzZTtcbiAgICAgICAgY29uc3Qgb3V0cHV0ID0ge1xuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKCFfb3B0aW9ucy5kaXNhYmxlZCkge1xuICAgICAgICAgICAgaWYgKCFpc0JsdXJFdmVudCB8fCBzaG91bGREaXJ0eSkge1xuICAgICAgICAgICAgICAgIGlmIChfcHJveHlGb3JtU3RhdGUuaXNEaXJ0eSB8fCBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNEaXJ0eSkge1xuICAgICAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRGlydHkgPSBfZm9ybVN0YXRlLmlzRGlydHk7XG4gICAgICAgICAgICAgICAgICAgIF9mb3JtU3RhdGUuaXNEaXJ0eSA9IG91dHB1dC5pc0RpcnR5ID0gX2dldERpcnR5KCk7XG4gICAgICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkID0gaXNQcmV2aW91c0RpcnR5ICE9PSBvdXRwdXQuaXNEaXJ0eTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50RmllbGRQcmlzdGluZSA9IGRlZXBFcXVhbChnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpLCBmaWVsZFZhbHVlKTtcbiAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRGlydHkgPSAhIWdldChfZm9ybVN0YXRlLmRpcnR5RmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpc0N1cnJlbnRGaWVsZFByaXN0aW5lXG4gICAgICAgICAgICAgICAgICAgID8gdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSlcbiAgICAgICAgICAgICAgICAgICAgOiBzZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgb3V0cHV0LmRpcnR5RmllbGRzID0gX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcztcbiAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCA9XG4gICAgICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAoKF9wcm94eUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcyB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcykgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRGlydHkgIT09ICFpc0N1cnJlbnRGaWVsZFByaXN0aW5lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc0JsdXJFdmVudCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzUHJldmlvdXNGaWVsZFRvdWNoZWQgPSBnZXQoX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoIWlzUHJldmlvdXNGaWVsZFRvdWNoZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSwgaXNCbHVyRXZlbnQpO1xuICAgICAgICAgICAgICAgICAgICBvdXRwdXQudG91Y2hlZEZpZWxkcyA9IF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcztcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgPVxuICAgICAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoKF9wcm94eUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzKSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1ByZXZpb3VzRmllbGRUb3VjaGVkICE9PSBpc0JsdXJFdmVudCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgJiYgc2hvdWxkUmVuZGVyICYmIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KG91dHB1dCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNob3VsZFVwZGF0ZUZpZWxkID8gb3V0cHV0IDoge307XG4gICAgfTtcbiAgICBjb25zdCBzaG91bGRSZW5kZXJCeUVycm9yID0gKG5hbWUsIGlzVmFsaWQsIGVycm9yLCBmaWVsZFN0YXRlKSA9PiB7XG4gICAgICAgIGNvbnN0IHByZXZpb3VzRmllbGRFcnJvciA9IGdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgIGNvbnN0IHNob3VsZFVwZGF0ZVZhbGlkID0gKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkKSAmJlxuICAgICAgICAgICAgaXNCb29sZWFuKGlzVmFsaWQpICYmXG4gICAgICAgICAgICBfZm9ybVN0YXRlLmlzVmFsaWQgIT09IGlzVmFsaWQ7XG4gICAgICAgIGlmIChfb3B0aW9ucy5kZWxheUVycm9yICYmIGVycm9yKSB7XG4gICAgICAgICAgICBkZWxheUVycm9yQ2FsbGJhY2sgPSBkZWJvdW5jZSgoKSA9PiB1cGRhdGVFcnJvcnMobmFtZSwgZXJyb3IpKTtcbiAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayhfb3B0aW9ucy5kZWxheUVycm9yKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lcik7XG4gICAgICAgICAgICBkZWxheUVycm9yQ2FsbGJhY2sgPSBudWxsO1xuICAgICAgICAgICAgZXJyb3JcbiAgICAgICAgICAgICAgICA/IHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3IpXG4gICAgICAgICAgICAgICAgOiB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKChlcnJvciA/ICFkZWVwRXF1YWwocHJldmlvdXNGaWVsZEVycm9yLCBlcnJvcikgOiBwcmV2aW91c0ZpZWxkRXJyb3IpIHx8XG4gICAgICAgICAgICAhaXNFbXB0eU9iamVjdChmaWVsZFN0YXRlKSB8fFxuICAgICAgICAgICAgc2hvdWxkVXBkYXRlVmFsaWQpIHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRGb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAgICAgLi4uZmllbGRTdGF0ZSxcbiAgICAgICAgICAgICAgICAuLi4oc2hvdWxkVXBkYXRlVmFsaWQgJiYgaXNCb29sZWFuKGlzVmFsaWQpID8geyBpc1ZhbGlkIH0gOiB7fSksXG4gICAgICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIF9mb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAgICAgLi4uX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgICAgICAuLi51cGRhdGVkRm9ybVN0YXRlLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHVwZGF0ZWRGb3JtU3RhdGUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfcnVuU2NoZW1hID0gYXN5bmMgKG5hbWUpID0+IHtcbiAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhuYW1lLCB0cnVlKTtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgX29wdGlvbnMucmVzb2x2ZXIoX2Zvcm1WYWx1ZXMsIF9vcHRpb25zLmNvbnRleHQsIGdldFJlc29sdmVyT3B0aW9ucyhuYW1lIHx8IF9uYW1lcy5tb3VudCwgX2ZpZWxkcywgX29wdGlvbnMuY3JpdGVyaWFNb2RlLCBfb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uKSk7XG4gICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcobmFtZSk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbiAgICBjb25zdCBleGVjdXRlU2NoZW1hQW5kVXBkYXRlU3RhdGUgPSBhc3luYyAobmFtZXMpID0+IHtcbiAgICAgICAgY29uc3QgeyBlcnJvcnMgfSA9IGF3YWl0IF9ydW5TY2hlbWEobmFtZXMpO1xuICAgICAgICBpZiAobmFtZXMpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBuYW1lcykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZ2V0KGVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgZXJyb3JcbiAgICAgICAgICAgICAgICAgICAgPyBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIGVycm9yKVxuICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIF9mb3JtU3RhdGUuZXJyb3JzID0gZXJyb3JzO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBlcnJvcnM7XG4gICAgfTtcbiAgICBjb25zdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24gPSBhc3luYyAoZmllbGRzLCBzaG91bGRPbmx5Q2hlY2tWYWxpZCwgY29udGV4dCA9IHtcbiAgICAgICAgdmFsaWQ6IHRydWUsXG4gICAgfSkgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IG5hbWUgaW4gZmllbGRzKSB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZCA9IGZpZWxkc1tuYW1lXTtcbiAgICAgICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgX2YsIC4uLmZpZWxkVmFsdWUgfSA9IGZpZWxkO1xuICAgICAgICAgICAgICAgIGlmIChfZikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBpc0ZpZWxkQXJyYXlSb290ID0gX25hbWVzLmFycmF5LmhhcyhfZi5uYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNQcm9taXNlRnVuY3Rpb24gPSBmaWVsZC5fZiAmJiBoYXNQcm9taXNlVmFsaWRhdGlvbihmaWVsZC5fZik7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc1Byb21pc2VGdW5jdGlvbiAmJiBfcHJveHlGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0sIHRydWUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkRXJyb3IgPSBhd2FpdCB2YWxpZGF0ZUZpZWxkKGZpZWxkLCBfbmFtZXMuZGlzYWJsZWQsIF9mb3JtVmFsdWVzLCBzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycywgX29wdGlvbnMuc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbiAmJiAhc2hvdWxkT25seUNoZWNrVmFsaWQsIGlzRmllbGRBcnJheVJvb3QpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoaXNQcm9taXNlRnVuY3Rpb24gJiYgX3Byb3h5Rm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcoW25hbWVdKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoZmllbGRFcnJvcltfZi5uYW1lXSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC52YWxpZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHNob3VsZE9ubHlDaGVja1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgIXNob3VsZE9ubHlDaGVja1ZhbGlkICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAoZ2V0KGZpZWxkRXJyb3IsIF9mLm5hbWUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBpc0ZpZWxkQXJyYXlSb290XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdXBkYXRlRmllbGRBcnJheVJvb3RFcnJvcihfZm9ybVN0YXRlLmVycm9ycywgZmllbGRFcnJvciwgX2YubmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIF9mLm5hbWUsIGZpZWxkRXJyb3JbX2YubmFtZV0pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgX2YubmFtZSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAhaXNFbXB0eU9iamVjdChmaWVsZFZhbHVlKSAmJlxuICAgICAgICAgICAgICAgICAgICAoYXdhaXQgZXhlY3V0ZUJ1aWx0SW5WYWxpZGF0aW9uKGZpZWxkVmFsdWUsIHNob3VsZE9ubHlDaGVja1ZhbGlkLCBjb250ZXh0KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvbnRleHQudmFsaWQ7XG4gICAgfTtcbiAgICBjb25zdCBfcmVtb3ZlVW5tb3VudGVkID0gKCkgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IG5hbWUgb2YgX25hbWVzLnVuTW91bnQpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgZmllbGQgJiZcbiAgICAgICAgICAgICAgICAoZmllbGQuX2YucmVmc1xuICAgICAgICAgICAgICAgICAgICA/IGZpZWxkLl9mLnJlZnMuZXZlcnkoKHJlZikgPT4gIWxpdmUocmVmKSlcbiAgICAgICAgICAgICAgICAgICAgOiAhbGl2ZShmaWVsZC5fZi5yZWYpKSAmJlxuICAgICAgICAgICAgICAgIHVucmVnaXN0ZXIobmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgX25hbWVzLnVuTW91bnQgPSBuZXcgU2V0KCk7XG4gICAgfTtcbiAgICBjb25zdCBfZ2V0RGlydHkgPSAobmFtZSwgZGF0YSkgPT4gIV9vcHRpb25zLmRpc2FibGVkICYmXG4gICAgICAgIChuYW1lICYmIGRhdGEgJiYgc2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBkYXRhKSxcbiAgICAgICAgICAgICFkZWVwRXF1YWwoZ2V0VmFsdWVzKCksIF9kZWZhdWx0VmFsdWVzKSk7XG4gICAgY29uc3QgX2dldFdhdGNoID0gKG5hbWVzLCBkZWZhdWx0VmFsdWUsIGlzR2xvYmFsKSA9PiBnZW5lcmF0ZVdhdGNoT3V0cHV0KG5hbWVzLCBfbmFtZXMsIHtcbiAgICAgICAgLi4uKF9zdGF0ZS5tb3VudFxuICAgICAgICAgICAgPyBfZm9ybVZhbHVlc1xuICAgICAgICAgICAgOiBpc1VuZGVmaW5lZChkZWZhdWx0VmFsdWUpXG4gICAgICAgICAgICAgICAgPyBfZGVmYXVsdFZhbHVlc1xuICAgICAgICAgICAgICAgIDogaXNTdHJpbmcobmFtZXMpXG4gICAgICAgICAgICAgICAgICAgID8geyBbbmFtZXNdOiBkZWZhdWx0VmFsdWUgfVxuICAgICAgICAgICAgICAgICAgICA6IGRlZmF1bHRWYWx1ZSksXG4gICAgfSwgaXNHbG9iYWwsIGRlZmF1bHRWYWx1ZSk7XG4gICAgY29uc3QgX2dldEZpZWxkQXJyYXkgPSAobmFtZSkgPT4gY29tcGFjdChnZXQoX3N0YXRlLm1vdW50ID8gX2Zvcm1WYWx1ZXMgOiBfZGVmYXVsdFZhbHVlcywgbmFtZSwgX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciA/IGdldChfZGVmYXVsdFZhbHVlcywgbmFtZSwgW10pIDogW10pKTtcbiAgICBjb25zdCBzZXRGaWVsZFZhbHVlID0gKG5hbWUsIHZhbHVlLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGxldCBmaWVsZFZhbHVlID0gdmFsdWU7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgZmllbGRSZWZlcmVuY2UgPSBmaWVsZC5fZjtcbiAgICAgICAgICAgIGlmIChmaWVsZFJlZmVyZW5jZSkge1xuICAgICAgICAgICAgICAgICFmaWVsZFJlZmVyZW5jZS5kaXNhYmxlZCAmJlxuICAgICAgICAgICAgICAgICAgICBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGdldEZpZWxkVmFsdWVBcyh2YWx1ZSwgZmllbGRSZWZlcmVuY2UpKTtcbiAgICAgICAgICAgICAgICBmaWVsZFZhbHVlID1cbiAgICAgICAgICAgICAgICAgICAgaXNIVE1MRWxlbWVudChmaWVsZFJlZmVyZW5jZS5yZWYpICYmIGlzTnVsbE9yVW5kZWZpbmVkKHZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgOiB2YWx1ZTtcbiAgICAgICAgICAgICAgICBpZiAoaXNNdWx0aXBsZVNlbGVjdChmaWVsZFJlZmVyZW5jZS5yZWYpKSB7XG4gICAgICAgICAgICAgICAgICAgIFsuLi5maWVsZFJlZmVyZW5jZS5yZWYub3B0aW9uc10uZm9yRWFjaCgob3B0aW9uUmVmKSA9PiAob3B0aW9uUmVmLnNlbGVjdGVkID0gZmllbGRWYWx1ZS5pbmNsdWRlcyhvcHRpb25SZWYudmFsdWUpKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGZpZWxkUmVmZXJlbmNlLnJlZnMpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzQ2hlY2tCb3hJbnB1dChmaWVsZFJlZmVyZW5jZS5yZWYpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZmVyZW5jZS5yZWZzLmZvckVhY2goKGNoZWNrYm94UmVmKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFjaGVja2JveFJlZi5kZWZhdWx0Q2hlY2tlZCB8fCAhY2hlY2tib3hSZWYuZGlzYWJsZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZmllbGRWYWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrYm94UmVmLmNoZWNrZWQgPSAhIWZpZWxkVmFsdWUuZmluZCgoZGF0YSkgPT4gZGF0YSA9PT0gY2hlY2tib3hSZWYudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tib3hSZWYuY2hlY2tlZCA9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRWYWx1ZSA9PT0gY2hlY2tib3hSZWYudmFsdWUgfHwgISFmaWVsZFZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZmVyZW5jZS5yZWZzLmZvckVhY2goKHJhZGlvUmVmKSA9PiAocmFkaW9SZWYuY2hlY2tlZCA9IHJhZGlvUmVmLnZhbHVlID09PSBmaWVsZFZhbHVlKSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoaXNGaWxlSW5wdXQoZmllbGRSZWZlcmVuY2UucmVmKSkge1xuICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZmVyZW5jZS5yZWYudmFsdWUgPSAnJztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZi52YWx1ZSA9IGZpZWxkVmFsdWU7XG4gICAgICAgICAgICAgICAgICAgIGlmICghZmllbGRSZWZlcmVuY2UucmVmLnR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgKG9wdGlvbnMuc2hvdWxkRGlydHkgfHwgb3B0aW9ucy5zaG91bGRUb3VjaCkgJiZcbiAgICAgICAgICAgIHVwZGF0ZVRvdWNoQW5kRGlydHkobmFtZSwgZmllbGRWYWx1ZSwgb3B0aW9ucy5zaG91bGRUb3VjaCwgb3B0aW9ucy5zaG91bGREaXJ0eSwgdHJ1ZSk7XG4gICAgICAgIG9wdGlvbnMuc2hvdWxkVmFsaWRhdGUgJiYgdHJpZ2dlcihuYW1lKTtcbiAgICB9O1xuICAgIGNvbnN0IHNldFZhbHVlcyA9IChuYW1lLCB2YWx1ZSwgb3B0aW9ucykgPT4ge1xuICAgICAgICBmb3IgKGNvbnN0IGZpZWxkS2V5IGluIHZhbHVlKSB7XG4gICAgICAgICAgICBpZiAoIXZhbHVlLmhhc093blByb3BlcnR5KGZpZWxkS2V5KSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWUgPSB2YWx1ZVtmaWVsZEtleV07XG4gICAgICAgICAgICBjb25zdCBmaWVsZE5hbWUgPSBgJHtuYW1lfS4ke2ZpZWxkS2V5fWA7XG4gICAgICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBmaWVsZE5hbWUpO1xuICAgICAgICAgICAgKF9uYW1lcy5hcnJheS5oYXMobmFtZSkgfHxcbiAgICAgICAgICAgICAgICBpc09iamVjdChmaWVsZFZhbHVlKSB8fFxuICAgICAgICAgICAgICAgIChmaWVsZCAmJiAhZmllbGQuX2YpKSAmJlxuICAgICAgICAgICAgICAgICFpc0RhdGVPYmplY3QoZmllbGRWYWx1ZSlcbiAgICAgICAgICAgICAgICA/IHNldFZhbHVlcyhmaWVsZE5hbWUsIGZpZWxkVmFsdWUsIG9wdGlvbnMpXG4gICAgICAgICAgICAgICAgOiBzZXRGaWVsZFZhbHVlKGZpZWxkTmFtZSwgZmllbGRWYWx1ZSwgb3B0aW9ucyk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHNldFZhbHVlID0gKG5hbWUsIHZhbHVlLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGNvbnN0IGlzRmllbGRBcnJheSA9IF9uYW1lcy5hcnJheS5oYXMobmFtZSk7XG4gICAgICAgIGNvbnN0IGNsb25lVmFsdWUgPSBjbG9uZU9iamVjdCh2YWx1ZSk7XG4gICAgICAgIHNldChfZm9ybVZhbHVlcywgbmFtZSwgY2xvbmVWYWx1ZSk7XG4gICAgICAgIGlmIChpc0ZpZWxkQXJyYXkpIHtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5hcnJheS5uZXh0KHtcbiAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoKF9wcm94eUZvcm1TdGF0ZS5pc0RpcnR5IHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5Rm9ybVN0YXRlLmRpcnR5RmllbGRzIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzRGlydHkgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuZGlydHlGaWVsZHMpICYmXG4gICAgICAgICAgICAgICAgb3B0aW9ucy5zaG91bGREaXJ0eSkge1xuICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgZGlydHlGaWVsZHM6IGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICAgICAgICAgIGlzRGlydHk6IF9nZXREaXJ0eShuYW1lLCBjbG9uZVZhbHVlKSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGZpZWxkICYmICFmaWVsZC5fZiAmJiAhaXNOdWxsT3JVbmRlZmluZWQoY2xvbmVWYWx1ZSlcbiAgICAgICAgICAgICAgICA/IHNldFZhbHVlcyhuYW1lLCBjbG9uZVZhbHVlLCBvcHRpb25zKVxuICAgICAgICAgICAgICAgIDogc2V0RmllbGRWYWx1ZShuYW1lLCBjbG9uZVZhbHVlLCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgICAgICBpc1dhdGNoZWQobmFtZSwgX25hbWVzKSAmJiBfc3ViamVjdHMuc3RhdGUubmV4dCh7IC4uLl9mb3JtU3RhdGUgfSk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIG5hbWU6IF9zdGF0ZS5tb3VudCA/IG5hbWUgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICB2YWx1ZXM6IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBvbkNoYW5nZSA9IGFzeW5jIChldmVudCkgPT4ge1xuICAgICAgICBfc3RhdGUubW91bnQgPSB0cnVlO1xuICAgICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQ7XG4gICAgICAgIGxldCBuYW1lID0gdGFyZ2V0Lm5hbWU7XG4gICAgICAgIGxldCBpc0ZpZWxkVmFsdWVVcGRhdGVkID0gdHJ1ZTtcbiAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgIGNvbnN0IF91cGRhdGVJc0ZpZWxkVmFsdWVVcGRhdGVkID0gKGZpZWxkVmFsdWUpID0+IHtcbiAgICAgICAgICAgIGlzRmllbGRWYWx1ZVVwZGF0ZWQgPVxuICAgICAgICAgICAgICAgIE51bWJlci5pc05hTihmaWVsZFZhbHVlKSB8fFxuICAgICAgICAgICAgICAgICAgICAoaXNEYXRlT2JqZWN0KGZpZWxkVmFsdWUpICYmIGlzTmFOKGZpZWxkVmFsdWUuZ2V0VGltZSgpKSkgfHxcbiAgICAgICAgICAgICAgICAgICAgZGVlcEVxdWFsKGZpZWxkVmFsdWUsIGdldChfZm9ybVZhbHVlcywgbmFtZSwgZmllbGRWYWx1ZSkpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCB2YWxpZGF0aW9uTW9kZUJlZm9yZVN1Ym1pdCA9IGdldFZhbGlkYXRpb25Nb2Rlcyhfb3B0aW9ucy5tb2RlKTtcbiAgICAgICAgY29uc3QgdmFsaWRhdGlvbk1vZGVBZnRlclN1Ym1pdCA9IGdldFZhbGlkYXRpb25Nb2Rlcyhfb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgbGV0IGVycm9yO1xuICAgICAgICAgICAgbGV0IGlzVmFsaWQ7XG4gICAgICAgICAgICBjb25zdCBmaWVsZFZhbHVlID0gdGFyZ2V0LnR5cGVcbiAgICAgICAgICAgICAgICA/IGdldEZpZWxkVmFsdWUoZmllbGQuX2YpXG4gICAgICAgICAgICAgICAgOiBnZXRFdmVudFZhbHVlKGV2ZW50KTtcbiAgICAgICAgICAgIGNvbnN0IGlzQmx1ckV2ZW50ID0gZXZlbnQudHlwZSA9PT0gRVZFTlRTLkJMVVIgfHwgZXZlbnQudHlwZSA9PT0gRVZFTlRTLkZPQ1VTX09VVDtcbiAgICAgICAgICAgIGNvbnN0IHNob3VsZFNraXBWYWxpZGF0aW9uID0gKCFoYXNWYWxpZGF0aW9uKGZpZWxkLl9mKSAmJlxuICAgICAgICAgICAgICAgICFfb3B0aW9ucy5yZXNvbHZlciAmJlxuICAgICAgICAgICAgICAgICFnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpICYmXG4gICAgICAgICAgICAgICAgIWZpZWxkLl9mLmRlcHMpIHx8XG4gICAgICAgICAgICAgICAgc2tpcFZhbGlkYXRpb24oaXNCbHVyRXZlbnQsIGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpLCBfZm9ybVN0YXRlLmlzU3VibWl0dGVkLCB2YWxpZGF0aW9uTW9kZUFmdGVyU3VibWl0LCB2YWxpZGF0aW9uTW9kZUJlZm9yZVN1Ym1pdCk7XG4gICAgICAgICAgICBjb25zdCB3YXRjaGVkID0gaXNXYXRjaGVkKG5hbWUsIF9uYW1lcywgaXNCbHVyRXZlbnQpO1xuICAgICAgICAgICAgc2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBmaWVsZFZhbHVlKTtcbiAgICAgICAgICAgIGlmIChpc0JsdXJFdmVudCkge1xuICAgICAgICAgICAgICAgIGZpZWxkLl9mLm9uQmx1ciAmJiBmaWVsZC5fZi5vbkJsdXIoZXZlbnQpO1xuICAgICAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayAmJiBkZWxheUVycm9yQ2FsbGJhY2soMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChmaWVsZC5fZi5vbkNoYW5nZSkge1xuICAgICAgICAgICAgICAgIGZpZWxkLl9mLm9uQ2hhbmdlKGV2ZW50KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGZpZWxkU3RhdGUgPSB1cGRhdGVUb3VjaEFuZERpcnR5KG5hbWUsIGZpZWxkVmFsdWUsIGlzQmx1ckV2ZW50KTtcbiAgICAgICAgICAgIGNvbnN0IHNob3VsZFJlbmRlciA9ICFpc0VtcHR5T2JqZWN0KGZpZWxkU3RhdGUpIHx8IHdhdGNoZWQ7XG4gICAgICAgICAgICAhaXNCbHVyRXZlbnQgJiZcbiAgICAgICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgIHR5cGU6IGV2ZW50LnR5cGUsXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHNob3VsZFNraXBWYWxpZGF0aW9uKSB7XG4gICAgICAgICAgICAgICAgaWYgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChfb3B0aW9ucy5tb2RlID09PSAnb25CbHVyJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgX3NldFZhbGlkKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoIWlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfc2V0VmFsaWQoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gKHNob3VsZFJlbmRlciAmJlxuICAgICAgICAgICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7IG5hbWUsIC4uLih3YXRjaGVkID8ge30gOiBmaWVsZFN0YXRlKSB9KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAhaXNCbHVyRXZlbnQgJiYgd2F0Y2hlZCAmJiBfc3ViamVjdHMuc3RhdGUubmV4dCh7IC4uLl9mb3JtU3RhdGUgfSk7XG4gICAgICAgICAgICBpZiAoX29wdGlvbnMucmVzb2x2ZXIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGVycm9ycyB9ID0gYXdhaXQgX3J1blNjaGVtYShbbmFtZV0pO1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc0ZpZWxkVmFsdWVVcGRhdGVkKGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgICAgIGlmIChpc0ZpZWxkVmFsdWVVcGRhdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHByZXZpb3VzRXJyb3JMb29rdXBSZXN1bHQgPSBzY2hlbWFFcnJvckxvb2t1cChfZm9ybVN0YXRlLmVycm9ycywgX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yTG9va3VwUmVzdWx0ID0gc2NoZW1hRXJyb3JMb29rdXAoZXJyb3JzLCBfZmllbGRzLCBwcmV2aW91c0Vycm9yTG9va3VwUmVzdWx0Lm5hbWUgfHwgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yID0gZXJyb3JMb29rdXBSZXN1bHQuZXJyb3I7XG4gICAgICAgICAgICAgICAgICAgIG5hbWUgPSBlcnJvckxvb2t1cFJlc3VsdC5uYW1lO1xuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkID0gaXNFbXB0eU9iamVjdChlcnJvcnMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcoW25hbWVdLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBlcnJvciA9IChhd2FpdCB2YWxpZGF0ZUZpZWxkKGZpZWxkLCBfbmFtZXMuZGlzYWJsZWQsIF9mb3JtVmFsdWVzLCBzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycywgX29wdGlvbnMuc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbikpW25hbWVdO1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc1ZhbGlkYXRpbmcoW25hbWVdKTtcbiAgICAgICAgICAgICAgICBfdXBkYXRlSXNGaWVsZFZhbHVlVXBkYXRlZChmaWVsZFZhbHVlKTtcbiAgICAgICAgICAgICAgICBpZiAoaXNGaWVsZFZhbHVlVXBkYXRlZCkge1xuICAgICAgICAgICAgICAgICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChfcHJveHlGb3JtU3RhdGUuaXNWYWxpZCB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgPSBhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oX2ZpZWxkcywgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNGaWVsZFZhbHVlVXBkYXRlZCkge1xuICAgICAgICAgICAgICAgIGZpZWxkLl9mLmRlcHMgJiZcbiAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcihmaWVsZC5fZi5kZXBzKTtcbiAgICAgICAgICAgICAgICBzaG91bGRSZW5kZXJCeUVycm9yKG5hbWUsIGlzVmFsaWQsIGVycm9yLCBmaWVsZFN0YXRlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgX2ZvY3VzSW5wdXQgPSAocmVmLCBrZXkpID0+IHtcbiAgICAgICAgaWYgKGdldChfZm9ybVN0YXRlLmVycm9ycywga2V5KSAmJiByZWYuZm9jdXMpIHtcbiAgICAgICAgICAgIHJlZi5mb2N1cygpO1xuICAgICAgICAgICAgcmV0dXJuIDE7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgIH07XG4gICAgY29uc3QgdHJpZ2dlciA9IGFzeW5jIChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgbGV0IGlzVmFsaWQ7XG4gICAgICAgIGxldCB2YWxpZGF0aW9uUmVzdWx0O1xuICAgICAgICBjb25zdCBmaWVsZE5hbWVzID0gY29udmVydFRvQXJyYXlQYXlsb2FkKG5hbWUpO1xuICAgICAgICBpZiAoX29wdGlvbnMucmVzb2x2ZXIpIHtcbiAgICAgICAgICAgIGNvbnN0IGVycm9ycyA9IGF3YWl0IGV4ZWN1dGVTY2hlbWFBbmRVcGRhdGVTdGF0ZShpc1VuZGVmaW5lZChuYW1lKSA/IG5hbWUgOiBmaWVsZE5hbWVzKTtcbiAgICAgICAgICAgIGlzVmFsaWQgPSBpc0VtcHR5T2JqZWN0KGVycm9ycyk7XG4gICAgICAgICAgICB2YWxpZGF0aW9uUmVzdWx0ID0gbmFtZVxuICAgICAgICAgICAgICAgID8gIWZpZWxkTmFtZXMuc29tZSgobmFtZSkgPT4gZ2V0KGVycm9ycywgbmFtZSkpXG4gICAgICAgICAgICAgICAgOiBpc1ZhbGlkO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKG5hbWUpIHtcbiAgICAgICAgICAgIHZhbGlkYXRpb25SZXN1bHQgPSAoYXdhaXQgUHJvbWlzZS5hbGwoZmllbGROYW1lcy5tYXAoYXN5bmMgKGZpZWxkTmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihmaWVsZCAmJiBmaWVsZC5fZiA/IHsgW2ZpZWxkTmFtZV06IGZpZWxkIH0gOiBmaWVsZCk7XG4gICAgICAgICAgICB9KSkpLmV2ZXJ5KEJvb2xlYW4pO1xuICAgICAgICAgICAgISghdmFsaWRhdGlvblJlc3VsdCAmJiAhX2Zvcm1TdGF0ZS5pc1ZhbGlkKSAmJiBfc2V0VmFsaWQoKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHZhbGlkYXRpb25SZXN1bHQgPSBpc1ZhbGlkID0gYXdhaXQgZXhlY3V0ZUJ1aWx0SW5WYWxpZGF0aW9uKF9maWVsZHMpO1xuICAgICAgICB9XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIC4uLighaXNTdHJpbmcobmFtZSkgfHxcbiAgICAgICAgICAgICAgICAoKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc1ZhbGlkKSAmJlxuICAgICAgICAgICAgICAgICAgICBpc1ZhbGlkICE9PSBfZm9ybVN0YXRlLmlzVmFsaWQpXG4gICAgICAgICAgICAgICAgPyB7fVxuICAgICAgICAgICAgICAgIDogeyBuYW1lIH0pLFxuICAgICAgICAgICAgLi4uKF9vcHRpb25zLnJlc29sdmVyIHx8ICFuYW1lID8geyBpc1ZhbGlkIH0gOiB7fSksXG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICB9KTtcbiAgICAgICAgb3B0aW9ucy5zaG91bGRGb2N1cyAmJlxuICAgICAgICAgICAgIXZhbGlkYXRpb25SZXN1bHQgJiZcbiAgICAgICAgICAgIGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihfZmllbGRzLCBfZm9jdXNJbnB1dCwgbmFtZSA/IGZpZWxkTmFtZXMgOiBfbmFtZXMubW91bnQpO1xuICAgICAgICByZXR1cm4gdmFsaWRhdGlvblJlc3VsdDtcbiAgICB9O1xuICAgIGNvbnN0IGdldFZhbHVlcyA9IChmaWVsZE5hbWVzKSA9PiB7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IHtcbiAgICAgICAgICAgIC4uLihfc3RhdGUubW91bnQgPyBfZm9ybVZhbHVlcyA6IF9kZWZhdWx0VmFsdWVzKSxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIGlzVW5kZWZpbmVkKGZpZWxkTmFtZXMpXG4gICAgICAgICAgICA/IHZhbHVlc1xuICAgICAgICAgICAgOiBpc1N0cmluZyhmaWVsZE5hbWVzKVxuICAgICAgICAgICAgICAgID8gZ2V0KHZhbHVlcywgZmllbGROYW1lcylcbiAgICAgICAgICAgICAgICA6IGZpZWxkTmFtZXMubWFwKChuYW1lKSA9PiBnZXQodmFsdWVzLCBuYW1lKSk7XG4gICAgfTtcbiAgICBjb25zdCBnZXRGaWVsZFN0YXRlID0gKG5hbWUsIGZvcm1TdGF0ZSkgPT4gKHtcbiAgICAgICAgaW52YWxpZDogISFnZXQoKGZvcm1TdGF0ZSB8fCBfZm9ybVN0YXRlKS5lcnJvcnMsIG5hbWUpLFxuICAgICAgICBpc0RpcnR5OiAhIWdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLmRpcnR5RmllbGRzLCBuYW1lKSxcbiAgICAgICAgZXJyb3I6IGdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLmVycm9ycywgbmFtZSksXG4gICAgICAgIGlzVmFsaWRhdGluZzogISFnZXQoX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLCBuYW1lKSxcbiAgICAgICAgaXNUb3VjaGVkOiAhIWdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLnRvdWNoZWRGaWVsZHMsIG5hbWUpLFxuICAgIH0pO1xuICAgIGNvbnN0IGNsZWFyRXJyb3JzID0gKG5hbWUpID0+IHtcbiAgICAgICAgbmFtZSAmJlxuICAgICAgICAgICAgY29udmVydFRvQXJyYXlQYXlsb2FkKG5hbWUpLmZvckVhY2goKGlucHV0TmFtZSkgPT4gdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIGlucHV0TmFtZSkpO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBlcnJvcnM6IG5hbWUgPyBfZm9ybVN0YXRlLmVycm9ycyA6IHt9LFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHNldEVycm9yID0gKG5hbWUsIGVycm9yLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGNvbnN0IHJlZiA9IChnZXQoX2ZpZWxkcywgbmFtZSwgeyBfZjoge30gfSkuX2YgfHwge30pLnJlZjtcbiAgICAgICAgY29uc3QgY3VycmVudEVycm9yID0gZ2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKSB8fCB7fTtcbiAgICAgICAgLy8gRG9uJ3Qgb3ZlcnJpZGUgZXhpc3RpbmcgZXJyb3IgbWVzc2FnZXMgZWxzZXdoZXJlIGluIHRoZSBvYmplY3QgdHJlZS5cbiAgICAgICAgY29uc3QgeyByZWY6IGN1cnJlbnRSZWYsIG1lc3NhZ2UsIHR5cGUsIC4uLnJlc3RPZkVycm9yVHJlZSB9ID0gY3VycmVudEVycm9yO1xuICAgICAgICBzZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIHtcbiAgICAgICAgICAgIC4uLnJlc3RPZkVycm9yVHJlZSxcbiAgICAgICAgICAgIC4uLmVycm9yLFxuICAgICAgICAgICAgcmVmLFxuICAgICAgICB9KTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIGVycm9yczogX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgICAgIG9wdGlvbnMgJiYgb3B0aW9ucy5zaG91bGRGb2N1cyAmJiByZWYgJiYgcmVmLmZvY3VzICYmIHJlZi5mb2N1cygpO1xuICAgIH07XG4gICAgY29uc3Qgd2F0Y2ggPSAobmFtZSwgZGVmYXVsdFZhbHVlKSA9PiBpc0Z1bmN0aW9uKG5hbWUpXG4gICAgICAgID8gX3N1YmplY3RzLnN0YXRlLnN1YnNjcmliZSh7XG4gICAgICAgICAgICBuZXh0OiAocGF5bG9hZCkgPT4gbmFtZShfZ2V0V2F0Y2godW5kZWZpbmVkLCBkZWZhdWx0VmFsdWUpLCBwYXlsb2FkKSxcbiAgICAgICAgfSlcbiAgICAgICAgOiBfZ2V0V2F0Y2gobmFtZSwgZGVmYXVsdFZhbHVlLCB0cnVlKTtcbiAgICBjb25zdCBfc3Vic2NyaWJlID0gKHByb3BzKSA9PiBfc3ViamVjdHMuc3RhdGUuc3Vic2NyaWJlKHtcbiAgICAgICAgbmV4dDogKGZvcm1TdGF0ZSkgPT4ge1xuICAgICAgICAgICAgaWYgKHNob3VsZFN1YnNjcmliZUJ5TmFtZShwcm9wcy5uYW1lLCBmb3JtU3RhdGUubmFtZSwgcHJvcHMuZXhhY3QpICYmXG4gICAgICAgICAgICAgICAgc2hvdWxkUmVuZGVyRm9ybVN0YXRlKGZvcm1TdGF0ZSwgcHJvcHMuZm9ybVN0YXRlIHx8IF9wcm94eUZvcm1TdGF0ZSwgX3NldEZvcm1TdGF0ZSwgcHJvcHMucmVSZW5kZXJSb290KSkge1xuICAgICAgICAgICAgICAgIHByb3BzLmNhbGxiYWNrKHtcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiB7IC4uLl9mb3JtVmFsdWVzIH0sXG4gICAgICAgICAgICAgICAgICAgIC4uLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICAgICAgICAgIC4uLmZvcm1TdGF0ZSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9KS51bnN1YnNjcmliZTtcbiAgICBjb25zdCBzdWJzY3JpYmUgPSAocHJvcHMpID0+IHtcbiAgICAgICAgX3N0YXRlLm1vdW50ID0gdHJ1ZTtcbiAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlID0ge1xuICAgICAgICAgICAgLi4uX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLFxuICAgICAgICAgICAgLi4ucHJvcHMuZm9ybVN0YXRlLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gX3N1YnNjcmliZSh7XG4gICAgICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgICAgIGZvcm1TdGF0ZTogX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHVucmVnaXN0ZXIgPSAobmFtZSwgb3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgZmllbGROYW1lIG9mIG5hbWUgPyBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSkgOiBfbmFtZXMubW91bnQpIHtcbiAgICAgICAgICAgIF9uYW1lcy5tb3VudC5kZWxldGUoZmllbGROYW1lKTtcbiAgICAgICAgICAgIF9uYW1lcy5hcnJheS5kZWxldGUoZmllbGROYW1lKTtcbiAgICAgICAgICAgIGlmICghb3B0aW9ucy5rZWVwVmFsdWUpIHtcbiAgICAgICAgICAgICAgICB1bnNldChfZmllbGRzLCBmaWVsZE5hbWUpO1xuICAgICAgICAgICAgICAgIHVuc2V0KF9mb3JtVmFsdWVzLCBmaWVsZE5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgIW9wdGlvbnMua2VlcEVycm9yICYmIHVuc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBmaWVsZE5hbWUpO1xuICAgICAgICAgICAgIW9wdGlvbnMua2VlcERpcnR5ICYmIHVuc2V0KF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwVG91Y2hlZCAmJiB1bnNldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwSXNWYWxpZGF0aW5nICYmXG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLCBmaWVsZE5hbWUpO1xuICAgICAgICAgICAgIV9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXIgJiZcbiAgICAgICAgICAgICAgICAhb3B0aW9ucy5rZWVwRGVmYXVsdFZhbHVlICYmXG4gICAgICAgICAgICAgICAgdW5zZXQoX2RlZmF1bHRWYWx1ZXMsIGZpZWxkTmFtZSk7XG4gICAgICAgIH1cbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgdmFsdWVzOiBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyksXG4gICAgICAgIH0pO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAuLi5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgLi4uKCFvcHRpb25zLmtlZXBEaXJ0eSA/IHt9IDogeyBpc0RpcnR5OiBfZ2V0RGlydHkoKSB9KSxcbiAgICAgICAgfSk7XG4gICAgICAgICFvcHRpb25zLmtlZXBJc1ZhbGlkICYmIF9zZXRWYWxpZCgpO1xuICAgIH07XG4gICAgY29uc3QgX3NldERpc2FibGVkRmllbGQgPSAoeyBkaXNhYmxlZCwgbmFtZSwgfSkgPT4ge1xuICAgICAgICBpZiAoKGlzQm9vbGVhbihkaXNhYmxlZCkgJiYgX3N0YXRlLm1vdW50KSB8fFxuICAgICAgICAgICAgISFkaXNhYmxlZCB8fFxuICAgICAgICAgICAgX25hbWVzLmRpc2FibGVkLmhhcyhuYW1lKSkge1xuICAgICAgICAgICAgZGlzYWJsZWQgPyBfbmFtZXMuZGlzYWJsZWQuYWRkKG5hbWUpIDogX25hbWVzLmRpc2FibGVkLmRlbGV0ZShuYW1lKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgcmVnaXN0ZXIgPSAobmFtZSwgb3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgICAgIGxldCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgY29uc3QgZGlzYWJsZWRJc0RlZmluZWQgPSBpc0Jvb2xlYW4ob3B0aW9ucy5kaXNhYmxlZCkgfHwgaXNCb29sZWFuKF9vcHRpb25zLmRpc2FibGVkKTtcbiAgICAgICAgc2V0KF9maWVsZHMsIG5hbWUsIHtcbiAgICAgICAgICAgIC4uLihmaWVsZCB8fCB7fSksXG4gICAgICAgICAgICBfZjoge1xuICAgICAgICAgICAgICAgIC4uLihmaWVsZCAmJiBmaWVsZC5fZiA/IGZpZWxkLl9mIDogeyByZWY6IHsgbmFtZSB9IH0pLFxuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgbW91bnQ6IHRydWUsXG4gICAgICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgICAgICBfbmFtZXMubW91bnQuYWRkKG5hbWUpO1xuICAgICAgICBpZiAoZmllbGQpIHtcbiAgICAgICAgICAgIF9zZXREaXNhYmxlZEZpZWxkKHtcbiAgICAgICAgICAgICAgICBkaXNhYmxlZDogaXNCb29sZWFuKG9wdGlvbnMuZGlzYWJsZWQpXG4gICAgICAgICAgICAgICAgICAgID8gb3B0aW9ucy5kaXNhYmxlZFxuICAgICAgICAgICAgICAgICAgICA6IF9vcHRpb25zLmRpc2FibGVkLFxuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHVwZGF0ZVZhbGlkQW5kVmFsdWUobmFtZSwgdHJ1ZSwgb3B0aW9ucy52YWx1ZSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLihkaXNhYmxlZElzRGVmaW5lZFxuICAgICAgICAgICAgICAgID8geyBkaXNhYmxlZDogb3B0aW9ucy5kaXNhYmxlZCB8fCBfb3B0aW9ucy5kaXNhYmxlZCB9XG4gICAgICAgICAgICAgICAgOiB7fSksXG4gICAgICAgICAgICAuLi4oX29wdGlvbnMucHJvZ3Jlc3NpdmVcbiAgICAgICAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWQ6ICEhb3B0aW9ucy5yZXF1aXJlZCxcbiAgICAgICAgICAgICAgICAgICAgbWluOiBnZXRSdWxlVmFsdWUob3B0aW9ucy5taW4pLFxuICAgICAgICAgICAgICAgICAgICBtYXg6IGdldFJ1bGVWYWx1ZShvcHRpb25zLm1heCksXG4gICAgICAgICAgICAgICAgICAgIG1pbkxlbmd0aDogZ2V0UnVsZVZhbHVlKG9wdGlvbnMubWluTGVuZ3RoKSxcbiAgICAgICAgICAgICAgICAgICAgbWF4TGVuZ3RoOiBnZXRSdWxlVmFsdWUob3B0aW9ucy5tYXhMZW5ndGgpLFxuICAgICAgICAgICAgICAgICAgICBwYXR0ZXJuOiBnZXRSdWxlVmFsdWUob3B0aW9ucy5wYXR0ZXJuKSxcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgOiB7fSksXG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgb25DaGFuZ2UsXG4gICAgICAgICAgICBvbkJsdXI6IG9uQ2hhbmdlLFxuICAgICAgICAgICAgcmVmOiAocmVmKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHJlZikge1xuICAgICAgICAgICAgICAgICAgICByZWdpc3RlcihuYW1lLCBvcHRpb25zKTtcbiAgICAgICAgICAgICAgICAgICAgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkUmVmID0gaXNVbmRlZmluZWQocmVmLnZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyByZWYucXVlcnlTZWxlY3RvckFsbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gcmVmLnF1ZXJ5U2VsZWN0b3JBbGwoJ2lucHV0LHNlbGVjdCx0ZXh0YXJlYScpWzBdIHx8IHJlZlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogcmVmXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHJlZjtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmFkaW9PckNoZWNrYm94ID0gaXNSYWRpb09yQ2hlY2tib3goZmllbGRSZWYpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZWZzID0gZmllbGQuX2YucmVmcyB8fCBbXTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJhZGlvT3JDaGVja2JveFxuICAgICAgICAgICAgICAgICAgICAgICAgPyByZWZzLmZpbmQoKG9wdGlvbikgPT4gb3B0aW9uID09PSBmaWVsZFJlZilcbiAgICAgICAgICAgICAgICAgICAgICAgIDogZmllbGRSZWYgPT09IGZpZWxkLl9mLnJlZikge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHNldChfZmllbGRzLCBuYW1lLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfZjoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmZpZWxkLl9mLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLihyYWRpb09yQ2hlY2tib3hcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWZzOiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucmVmcy5maWx0ZXIobGl2ZSksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmllbGRSZWYsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uKEFycmF5LmlzQXJyYXkoZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lKSkgPyBbe31dIDogW10pLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZjogeyB0eXBlOiBmaWVsZFJlZi50eXBlLCBuYW1lIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB7IHJlZjogZmllbGRSZWYgfSksXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgdXBkYXRlVmFsaWRBbmRWYWx1ZShuYW1lLCBmYWxzZSwgdW5kZWZpbmVkLCBmaWVsZFJlZik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lLCB7fSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZC5fZikge1xuICAgICAgICAgICAgICAgICAgICAgICAgZmllbGQuX2YubW91bnQgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAoX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciB8fCBvcHRpb25zLnNob3VsZFVucmVnaXN0ZXIpICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAhKGlzTmFtZUluRmllbGRBcnJheShfbmFtZXMuYXJyYXksIG5hbWUpICYmIF9zdGF0ZS5hY3Rpb24pICYmXG4gICAgICAgICAgICAgICAgICAgICAgICBfbmFtZXMudW5Nb3VudC5hZGQobmFtZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9O1xuICAgIGNvbnN0IF9mb2N1c0Vycm9yID0gKCkgPT4gX29wdGlvbnMuc2hvdWxkRm9jdXNFcnJvciAmJlxuICAgICAgICBpdGVyYXRlRmllbGRzQnlBY3Rpb24oX2ZpZWxkcywgX2ZvY3VzSW5wdXQsIF9uYW1lcy5tb3VudCk7XG4gICAgY29uc3QgX2Rpc2FibGVGb3JtID0gKGRpc2FibGVkKSA9PiB7XG4gICAgICAgIGlmIChpc0Jvb2xlYW4oZGlzYWJsZWQpKSB7XG4gICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7IGRpc2FibGVkIH0pO1xuICAgICAgICAgICAgaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKF9maWVsZHMsIChyZWYsIG5hbWUpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50RmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRGaWVsZCkge1xuICAgICAgICAgICAgICAgICAgICByZWYuZGlzYWJsZWQgPSBjdXJyZW50RmllbGQuX2YuZGlzYWJsZWQgfHwgZGlzYWJsZWQ7XG4gICAgICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGN1cnJlbnRGaWVsZC5fZi5yZWZzKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudEZpZWxkLl9mLnJlZnMuZm9yRWFjaCgoaW5wdXRSZWYpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dFJlZi5kaXNhYmxlZCA9IGN1cnJlbnRGaWVsZC5fZi5kaXNhYmxlZCB8fCBkaXNhYmxlZDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgMCwgZmFsc2UpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBoYW5kbGVTdWJtaXQgPSAob25WYWxpZCwgb25JbnZhbGlkKSA9PiBhc3luYyAoZSkgPT4ge1xuICAgICAgICBsZXQgb25WYWxpZEVycm9yID0gdW5kZWZpbmVkO1xuICAgICAgICBpZiAoZSkge1xuICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCAmJiBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICBlLnBlcnNpc3QgJiZcbiAgICAgICAgICAgICAgICBlLnBlcnNpc3QoKTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgZmllbGRWYWx1ZXMgPSBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIGlzU3VibWl0dGluZzogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChfb3B0aW9ucy5yZXNvbHZlcikge1xuICAgICAgICAgICAgY29uc3QgeyBlcnJvcnMsIHZhbHVlcyB9ID0gYXdhaXQgX3J1blNjaGVtYSgpO1xuICAgICAgICAgICAgX2Zvcm1TdGF0ZS5lcnJvcnMgPSBlcnJvcnM7XG4gICAgICAgICAgICBmaWVsZFZhbHVlcyA9IHZhbHVlcztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihfZmllbGRzKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoX25hbWVzLmRpc2FibGVkLnNpemUpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMuZGlzYWJsZWQpIHtcbiAgICAgICAgICAgICAgICBzZXQoZmllbGRWYWx1ZXMsIG5hbWUsIHVuZGVmaW5lZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsICdyb290Jyk7XG4gICAgICAgIGlmIChpc0VtcHR5T2JqZWN0KF9mb3JtU3RhdGUuZXJyb3JzKSkge1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIGVycm9yczoge30sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgb25WYWxpZChmaWVsZFZhbHVlcywgZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBvblZhbGlkRXJyb3IgPSBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlmIChvbkludmFsaWQpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBvbkludmFsaWQoeyAuLi5fZm9ybVN0YXRlLmVycm9ycyB9LCBlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF9mb2N1c0Vycm9yKCk7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KF9mb2N1c0Vycm9yKTtcbiAgICAgICAgfVxuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBpc1N1Ym1pdHRlZDogdHJ1ZSxcbiAgICAgICAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGlzRW1wdHlPYmplY3QoX2Zvcm1TdGF0ZS5lcnJvcnMpICYmICFvblZhbGlkRXJyb3IsXG4gICAgICAgICAgICBzdWJtaXRDb3VudDogX2Zvcm1TdGF0ZS5zdWJtaXRDb3VudCArIDEsXG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKG9uVmFsaWRFcnJvcikge1xuICAgICAgICAgICAgdGhyb3cgb25WYWxpZEVycm9yO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCByZXNldEZpZWxkID0gKG5hbWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBpZiAoZ2V0KF9maWVsZHMsIG5hbWUpKSB7XG4gICAgICAgICAgICBpZiAoaXNVbmRlZmluZWQob3B0aW9ucy5kZWZhdWx0VmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgc2V0VmFsdWUobmFtZSwgY2xvbmVPYmplY3QoZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lKSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgc2V0VmFsdWUobmFtZSwgb3B0aW9ucy5kZWZhdWx0VmFsdWUpO1xuICAgICAgICAgICAgICAgIHNldChfZGVmYXVsdFZhbHVlcywgbmFtZSwgY2xvbmVPYmplY3Qob3B0aW9ucy5kZWZhdWx0VmFsdWUpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghb3B0aW9ucy5rZWVwVG91Y2hlZCkge1xuICAgICAgICAgICAgICAgIHVuc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIW9wdGlvbnMua2VlcERpcnR5KSB7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgX2Zvcm1TdGF0ZS5pc0RpcnR5ID0gb3B0aW9ucy5kZWZhdWx0VmFsdWVcbiAgICAgICAgICAgICAgICAgICAgPyBfZ2V0RGlydHkobmFtZSwgY2xvbmVPYmplY3QoZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lKSkpXG4gICAgICAgICAgICAgICAgICAgIDogX2dldERpcnR5KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIW9wdGlvbnMua2VlcEVycm9yKSB7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkICYmIF9zZXRWYWxpZCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoeyAuLi5fZm9ybVN0YXRlIH0pO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfcmVzZXQgPSAoZm9ybVZhbHVlcywga2VlcFN0YXRlT3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRWYWx1ZXMgPSBmb3JtVmFsdWVzID8gY2xvbmVPYmplY3QoZm9ybVZhbHVlcykgOiBfZGVmYXVsdFZhbHVlcztcbiAgICAgICAgY29uc3QgY2xvbmVVcGRhdGVkVmFsdWVzID0gY2xvbmVPYmplY3QodXBkYXRlZFZhbHVlcyk7XG4gICAgICAgIGNvbnN0IGlzRW1wdHlSZXNldFZhbHVlcyA9IGlzRW1wdHlPYmplY3QoZm9ybVZhbHVlcyk7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IGlzRW1wdHlSZXNldFZhbHVlcyA/IF9kZWZhdWx0VmFsdWVzIDogY2xvbmVVcGRhdGVkVmFsdWVzO1xuICAgICAgICBpZiAoIWtlZXBTdGF0ZU9wdGlvbnMua2VlcERlZmF1bHRWYWx1ZXMpIHtcbiAgICAgICAgICAgIF9kZWZhdWx0VmFsdWVzID0gdXBkYXRlZFZhbHVlcztcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWtlZXBTdGF0ZU9wdGlvbnMua2VlcFZhbHVlcykge1xuICAgICAgICAgICAgaWYgKGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5VmFsdWVzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRzVG9DaGVjayA9IG5ldyBTZXQoW1xuICAgICAgICAgICAgICAgICAgICAuLi5fbmFtZXMubW91bnQsXG4gICAgICAgICAgICAgICAgICAgIC4uLk9iamVjdC5rZXlzKGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBfZm9ybVZhbHVlcykpLFxuICAgICAgICAgICAgICAgIF0pO1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZmllbGROYW1lIG9mIEFycmF5LmZyb20oZmllbGRzVG9DaGVjaykpIHtcbiAgICAgICAgICAgICAgICAgICAgZ2V0KF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsIGZpZWxkTmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gc2V0KHZhbHVlcywgZmllbGROYW1lLCBnZXQoX2Zvcm1WYWx1ZXMsIGZpZWxkTmFtZSkpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHNldFZhbHVlKGZpZWxkTmFtZSwgZ2V0KHZhbHVlcywgZmllbGROYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzV2ViICYmIGlzVW5kZWZpbmVkKGZvcm1WYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMubW91bnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRSZWZlcmVuY2UgPSBBcnJheS5pc0FycmF5KGZpZWxkLl9mLnJlZnMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gZmllbGQuX2YucmVmc1swXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGZpZWxkLl9mLnJlZjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaXNIVE1MRWxlbWVudChmaWVsZFJlZmVyZW5jZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZm9ybSA9IGZpZWxkUmVmZXJlbmNlLmNsb3Nlc3QoJ2Zvcm0nKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZvcm0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm0ucmVzZXQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZmllbGROYW1lIG9mIF9uYW1lcy5tb3VudCkge1xuICAgICAgICAgICAgICAgICAgICBzZXRWYWx1ZShmaWVsZE5hbWUsIGdldCh2YWx1ZXMsIGZpZWxkTmFtZSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF9mb3JtVmFsdWVzID0gY2xvbmVPYmplY3QodmFsdWVzKTtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5hcnJheS5uZXh0KHtcbiAgICAgICAgICAgICAgICB2YWx1ZXM6IHsgLi4udmFsdWVzIH0sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICB2YWx1ZXM6IHsgLi4udmFsdWVzIH0sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBfbmFtZXMgPSB7XG4gICAgICAgICAgICBtb3VudDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGlydHlWYWx1ZXMgPyBfbmFtZXMubW91bnQgOiBuZXcgU2V0KCksXG4gICAgICAgICAgICB1bk1vdW50OiBuZXcgU2V0KCksXG4gICAgICAgICAgICBhcnJheTogbmV3IFNldCgpLFxuICAgICAgICAgICAgZGlzYWJsZWQ6IG5ldyBTZXQoKSxcbiAgICAgICAgICAgIHdhdGNoOiBuZXcgU2V0KCksXG4gICAgICAgICAgICB3YXRjaEFsbDogZmFsc2UsXG4gICAgICAgICAgICBmb2N1czogJycsXG4gICAgICAgIH07XG4gICAgICAgIF9zdGF0ZS5tb3VudCA9XG4gICAgICAgICAgICAhX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICAhIWtlZXBTdGF0ZU9wdGlvbnMua2VlcElzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICAhIWtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5VmFsdWVzO1xuICAgICAgICBfc3RhdGUud2F0Y2ggPSAhIV9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXI7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIHN1Ym1pdENvdW50OiBrZWVwU3RhdGVPcHRpb25zLmtlZXBTdWJtaXRDb3VudFxuICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5zdWJtaXRDb3VudFxuICAgICAgICAgICAgICAgIDogMCxcbiAgICAgICAgICAgIGlzRGlydHk6IGlzRW1wdHlSZXNldFZhbHVlc1xuICAgICAgICAgICAgICAgID8gZmFsc2VcbiAgICAgICAgICAgICAgICA6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5XG4gICAgICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5pc0RpcnR5XG4gICAgICAgICAgICAgICAgICAgIDogISEoa2VlcFN0YXRlT3B0aW9ucy5rZWVwRGVmYXVsdFZhbHVlcyAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgIWRlZXBFcXVhbChmb3JtVmFsdWVzLCBfZGVmYXVsdFZhbHVlcykpLFxuICAgICAgICAgICAgaXNTdWJtaXR0ZWQ6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcElzU3VibWl0dGVkXG4gICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLmlzU3VibWl0dGVkXG4gICAgICAgICAgICAgICAgOiBmYWxzZSxcbiAgICAgICAgICAgIGRpcnR5RmllbGRzOiBpc0VtcHR5UmVzZXRWYWx1ZXNcbiAgICAgICAgICAgICAgICA/IHt9XG4gICAgICAgICAgICAgICAgOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBEaXJ0eVZhbHVlc1xuICAgICAgICAgICAgICAgICAgICA/IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERlZmF1bHRWYWx1ZXMgJiYgX2Zvcm1WYWx1ZXNcbiAgICAgICAgICAgICAgICAgICAgICAgID8gZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIF9mb3JtVmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBfZm9ybVN0YXRlLmRpcnR5RmllbGRzXG4gICAgICAgICAgICAgICAgICAgIDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGVmYXVsdFZhbHVlcyAmJiBmb3JtVmFsdWVzXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGdldERpcnR5RmllbGRzKF9kZWZhdWx0VmFsdWVzLCBmb3JtVmFsdWVzKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBEaXJ0eVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDoge30sXG4gICAgICAgICAgICB0b3VjaGVkRmllbGRzOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBUb3VjaGVkXG4gICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHNcbiAgICAgICAgICAgICAgICA6IHt9LFxuICAgICAgICAgICAgZXJyb3JzOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBFcnJvcnMgPyBfZm9ybVN0YXRlLmVycm9ycyA6IHt9LFxuICAgICAgICAgICAgaXNTdWJtaXRTdWNjZXNzZnVsOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBJc1N1Ym1pdFN1Y2Nlc3NmdWxcbiAgICAgICAgICAgICAgICA/IF9mb3JtU3RhdGUuaXNTdWJtaXRTdWNjZXNzZnVsXG4gICAgICAgICAgICAgICAgOiBmYWxzZSxcbiAgICAgICAgICAgIGlzU3VibWl0dGluZzogZmFsc2UsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgcmVzZXQgPSAoZm9ybVZhbHVlcywga2VlcFN0YXRlT3B0aW9ucykgPT4gX3Jlc2V0KGlzRnVuY3Rpb24oZm9ybVZhbHVlcylcbiAgICAgICAgPyBmb3JtVmFsdWVzKF9mb3JtVmFsdWVzKVxuICAgICAgICA6IGZvcm1WYWx1ZXMsIGtlZXBTdGF0ZU9wdGlvbnMpO1xuICAgIGNvbnN0IHNldEZvY3VzID0gKG5hbWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgY29uc3QgZmllbGRSZWZlcmVuY2UgPSBmaWVsZCAmJiBmaWVsZC5fZjtcbiAgICAgICAgaWYgKGZpZWxkUmVmZXJlbmNlKSB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZFJlZiA9IGZpZWxkUmVmZXJlbmNlLnJlZnNcbiAgICAgICAgICAgICAgICA/IGZpZWxkUmVmZXJlbmNlLnJlZnNbMF1cbiAgICAgICAgICAgICAgICA6IGZpZWxkUmVmZXJlbmNlLnJlZjtcbiAgICAgICAgICAgIGlmIChmaWVsZFJlZi5mb2N1cykge1xuICAgICAgICAgICAgICAgIGZpZWxkUmVmLmZvY3VzKCk7XG4gICAgICAgICAgICAgICAgb3B0aW9ucy5zaG91bGRTZWxlY3QgJiZcbiAgICAgICAgICAgICAgICAgICAgaXNGdW5jdGlvbihmaWVsZFJlZi5zZWxlY3QpICYmXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmLnNlbGVjdCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfc2V0Rm9ybVN0YXRlID0gKHVwZGF0ZWRGb3JtU3RhdGUpID0+IHtcbiAgICAgICAgX2Zvcm1TdGF0ZSA9IHtcbiAgICAgICAgICAgIC4uLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi51cGRhdGVkRm9ybVN0YXRlLFxuICAgICAgICB9O1xuICAgIH07XG4gICAgY29uc3QgX3Jlc2V0RGVmYXVsdFZhbHVlcyA9ICgpID0+IGlzRnVuY3Rpb24oX29wdGlvbnMuZGVmYXVsdFZhbHVlcykgJiZcbiAgICAgICAgX29wdGlvbnMuZGVmYXVsdFZhbHVlcygpLnRoZW4oKHZhbHVlcykgPT4ge1xuICAgICAgICAgICAgcmVzZXQodmFsdWVzLCBfb3B0aW9ucy5yZXNldE9wdGlvbnMpO1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgY29uc3QgbWV0aG9kcyA9IHtcbiAgICAgICAgY29udHJvbDoge1xuICAgICAgICAgICAgcmVnaXN0ZXIsXG4gICAgICAgICAgICB1bnJlZ2lzdGVyLFxuICAgICAgICAgICAgZ2V0RmllbGRTdGF0ZSxcbiAgICAgICAgICAgIGhhbmRsZVN1Ym1pdCxcbiAgICAgICAgICAgIHNldEVycm9yLFxuICAgICAgICAgICAgX3N1YnNjcmliZSxcbiAgICAgICAgICAgIF9ydW5TY2hlbWEsXG4gICAgICAgICAgICBfZ2V0V2F0Y2gsXG4gICAgICAgICAgICBfZ2V0RGlydHksXG4gICAgICAgICAgICBfc2V0VmFsaWQsXG4gICAgICAgICAgICBfc2V0RmllbGRBcnJheSxcbiAgICAgICAgICAgIF9zZXREaXNhYmxlZEZpZWxkLFxuICAgICAgICAgICAgX3NldEVycm9ycyxcbiAgICAgICAgICAgIF9nZXRGaWVsZEFycmF5LFxuICAgICAgICAgICAgX3Jlc2V0LFxuICAgICAgICAgICAgX3Jlc2V0RGVmYXVsdFZhbHVlcyxcbiAgICAgICAgICAgIF9yZW1vdmVVbm1vdW50ZWQsXG4gICAgICAgICAgICBfZGlzYWJsZUZvcm0sXG4gICAgICAgICAgICBfc3ViamVjdHMsXG4gICAgICAgICAgICBfcHJveHlGb3JtU3RhdGUsXG4gICAgICAgICAgICBnZXQgX2ZpZWxkcygpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX2ZpZWxkcztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQgX2Zvcm1WYWx1ZXMoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9mb3JtVmFsdWVzO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfc3RhdGUoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9zdGF0ZTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZXQgX3N0YXRlKHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgX3N0YXRlID0gdmFsdWU7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9kZWZhdWx0VmFsdWVzKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfZGVmYXVsdFZhbHVlcztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQgX25hbWVzKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfbmFtZXM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc2V0IF9uYW1lcyh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIF9uYW1lcyA9IHZhbHVlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfZm9ybVN0YXRlKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfZm9ybVN0YXRlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfb3B0aW9ucygpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX29wdGlvbnM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc2V0IF9vcHRpb25zKHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgX29wdGlvbnMgPSB7XG4gICAgICAgICAgICAgICAgICAgIC4uLl9vcHRpb25zLFxuICAgICAgICAgICAgICAgICAgICAuLi52YWx1ZSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgc3Vic2NyaWJlLFxuICAgICAgICB0cmlnZ2VyLFxuICAgICAgICByZWdpc3RlcixcbiAgICAgICAgaGFuZGxlU3VibWl0LFxuICAgICAgICB3YXRjaCxcbiAgICAgICAgc2V0VmFsdWUsXG4gICAgICAgIGdldFZhbHVlcyxcbiAgICAgICAgcmVzZXQsXG4gICAgICAgIHJlc2V0RmllbGQsXG4gICAgICAgIGNsZWFyRXJyb3JzLFxuICAgICAgICB1bnJlZ2lzdGVyLFxuICAgICAgICBzZXRFcnJvcixcbiAgICAgICAgc2V0Rm9jdXMsXG4gICAgICAgIGdldEZpZWxkU3RhdGUsXG4gICAgfTtcbiAgICByZXR1cm4ge1xuICAgICAgICAuLi5tZXRob2RzLFxuICAgICAgICBmb3JtQ29udHJvbDogbWV0aG9kcyxcbiAgICB9O1xufVxuXG52YXIgZ2VuZXJhdGVJZCA9ICgpID0+IHtcbiAgICBjb25zdCBkID0gdHlwZW9mIHBlcmZvcm1hbmNlID09PSAndW5kZWZpbmVkJyA/IERhdGUubm93KCkgOiBwZXJmb3JtYW5jZS5ub3coKSAqIDEwMDA7XG4gICAgcmV0dXJuICd4eHh4eHh4eC14eHh4LTR4eHgteXh4eC14eHh4eHh4eHh4eHgnLnJlcGxhY2UoL1t4eV0vZywgKGMpID0+IHtcbiAgICAgICAgY29uc3QgciA9IChNYXRoLnJhbmRvbSgpICogMTYgKyBkKSAlIDE2IHwgMDtcbiAgICAgICAgcmV0dXJuIChjID09ICd4JyA/IHIgOiAociAmIDB4MykgfCAweDgpLnRvU3RyaW5nKDE2KTtcbiAgICB9KTtcbn07XG5cbnZhciBnZXRGb2N1c0ZpZWxkTmFtZSA9IChuYW1lLCBpbmRleCwgb3B0aW9ucyA9IHt9KSA9PiBvcHRpb25zLnNob3VsZEZvY3VzIHx8IGlzVW5kZWZpbmVkKG9wdGlvbnMuc2hvdWxkRm9jdXMpXG4gICAgPyBvcHRpb25zLmZvY3VzTmFtZSB8fFxuICAgICAgICBgJHtuYW1lfS4ke2lzVW5kZWZpbmVkKG9wdGlvbnMuZm9jdXNJbmRleCkgPyBpbmRleCA6IG9wdGlvbnMuZm9jdXNJbmRleH0uYFxuICAgIDogJyc7XG5cbnZhciBhcHBlbmRBdCA9IChkYXRhLCB2YWx1ZSkgPT4gW1xuICAgIC4uLmRhdGEsXG4gICAgLi4uY29udmVydFRvQXJyYXlQYXlsb2FkKHZhbHVlKSxcbl07XG5cbnZhciBmaWxsRW1wdHlBcnJheSA9ICh2YWx1ZSkgPT4gQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZS5tYXAoKCkgPT4gdW5kZWZpbmVkKSA6IHVuZGVmaW5lZDtcblxuZnVuY3Rpb24gaW5zZXJ0KGRhdGEsIGluZGV4LCB2YWx1ZSkge1xuICAgIHJldHVybiBbXG4gICAgICAgIC4uLmRhdGEuc2xpY2UoMCwgaW5kZXgpLFxuICAgICAgICAuLi5jb252ZXJ0VG9BcnJheVBheWxvYWQodmFsdWUpLFxuICAgICAgICAuLi5kYXRhLnNsaWNlKGluZGV4KSxcbiAgICBdO1xufVxuXG52YXIgbW92ZUFycmF5QXQgPSAoZGF0YSwgZnJvbSwgdG8pID0+IHtcbiAgICBpZiAoIUFycmF5LmlzQXJyYXkoZGF0YSkpIHtcbiAgICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgICBpZiAoaXNVbmRlZmluZWQoZGF0YVt0b10pKSB7XG4gICAgICAgIGRhdGFbdG9dID0gdW5kZWZpbmVkO1xuICAgIH1cbiAgICBkYXRhLnNwbGljZSh0bywgMCwgZGF0YS5zcGxpY2UoZnJvbSwgMSlbMF0pO1xuICAgIHJldHVybiBkYXRhO1xufTtcblxudmFyIHByZXBlbmRBdCA9IChkYXRhLCB2YWx1ZSkgPT4gW1xuICAgIC4uLmNvbnZlcnRUb0FycmF5UGF5bG9hZCh2YWx1ZSksXG4gICAgLi4uY29udmVydFRvQXJyYXlQYXlsb2FkKGRhdGEpLFxuXTtcblxuZnVuY3Rpb24gcmVtb3ZlQXRJbmRleGVzKGRhdGEsIGluZGV4ZXMpIHtcbiAgICBsZXQgaSA9IDA7XG4gICAgY29uc3QgdGVtcCA9IFsuLi5kYXRhXTtcbiAgICBmb3IgKGNvbnN0IGluZGV4IG9mIGluZGV4ZXMpIHtcbiAgICAgICAgdGVtcC5zcGxpY2UoaW5kZXggLSBpLCAxKTtcbiAgICAgICAgaSsrO1xuICAgIH1cbiAgICByZXR1cm4gY29tcGFjdCh0ZW1wKS5sZW5ndGggPyB0ZW1wIDogW107XG59XG52YXIgcmVtb3ZlQXJyYXlBdCA9IChkYXRhLCBpbmRleCkgPT4gaXNVbmRlZmluZWQoaW5kZXgpXG4gICAgPyBbXVxuICAgIDogcmVtb3ZlQXRJbmRleGVzKGRhdGEsIGNvbnZlcnRUb0FycmF5UGF5bG9hZChpbmRleCkuc29ydCgoYSwgYikgPT4gYSAtIGIpKTtcblxudmFyIHN3YXBBcnJheUF0ID0gKGRhdGEsIGluZGV4QSwgaW5kZXhCKSA9PiB7XG4gICAgW2RhdGFbaW5kZXhBXSwgZGF0YVtpbmRleEJdXSA9IFtkYXRhW2luZGV4Ql0sIGRhdGFbaW5kZXhBXV07XG59O1xuXG52YXIgdXBkYXRlQXQgPSAoZmllbGRWYWx1ZXMsIGluZGV4LCB2YWx1ZSkgPT4ge1xuICAgIGZpZWxkVmFsdWVzW2luZGV4XSA9IHZhbHVlO1xuICAgIHJldHVybiBmaWVsZFZhbHVlcztcbn07XG5cbi8qKlxuICogQSBjdXN0b20gaG9vayB0aGF0IGV4cG9zZXMgY29udmVuaWVudCBtZXRob2RzIHRvIHBlcmZvcm0gb3BlcmF0aW9ucyB3aXRoIGEgbGlzdCBvZiBkeW5hbWljIGlucHV0cyB0aGF0IG5lZWQgdG8gYmUgYXBwZW5kZWQsIHVwZGF0ZWQsIHJlbW92ZWQgZXRjLiDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tdXNlZmllbGRhcnJheS1zc3Vnbikg4oCiIFtWaWRlb10oaHR0cHM6Ly95b3V0dS5iZS80TXJiZkdTRlkyQSlcbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZmllbGRhcnJheSkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXVzZWZpZWxkYXJyYXktc3N1Z24pXG4gKlxuICogQHBhcmFtIHByb3BzIC0gdXNlRmllbGRBcnJheSBwcm9wc1xuICpcbiAqIEByZXR1cm5zIG1ldGhvZHMgLSBmdW5jdGlvbnMgdG8gbWFuaXB1bGF0ZSB3aXRoIHRoZSBGaWVsZCBBcnJheXMgKGR5bmFtaWMgaW5wdXRzKSB7QGxpbmsgVXNlRmllbGRBcnJheVJldHVybn1cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBmdW5jdGlvbiBBcHAoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIsIGNvbnRyb2wsIGhhbmRsZVN1Ym1pdCwgcmVzZXQsIHRyaWdnZXIsIHNldEVycm9yIH0gPSB1c2VGb3JtKHtcbiAqICAgICBkZWZhdWx0VmFsdWVzOiB7XG4gKiAgICAgICB0ZXN0OiBbXVxuICogICAgIH1cbiAqICAgfSk7XG4gKiAgIGNvbnN0IHsgZmllbGRzLCBhcHBlbmQgfSA9IHVzZUZpZWxkQXJyYXkoe1xuICogICAgIGNvbnRyb2wsXG4gKiAgICAgbmFtZTogXCJ0ZXN0XCJcbiAqICAgfSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQoZGF0YSA9PiBjb25zb2xlLmxvZyhkYXRhKSl9PlxuICogICAgICAge2ZpZWxkcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gKiAgICAgICAgICA8aW5wdXQga2V5PXtpdGVtLmlkfSB7Li4ucmVnaXN0ZXIoYHRlc3QuJHtpbmRleH0uZmlyc3ROYW1lYCl9ICAvPlxuICogICAgICAgKSl9XG4gKiAgICAgICA8YnV0dG9uIHR5cGU9XCJidXR0b25cIiBvbkNsaWNrPXsoKSA9PiBhcHBlbmQoeyBmaXJzdE5hbWU6IFwiYmlsbFwiIH0pfT5cbiAqICAgICAgICAgYXBwZW5kXG4gKiAgICAgICA8L2J1dHRvbj5cbiAqICAgICAgIDxpbnB1dCB0eXBlPVwic3VibWl0XCIgLz5cbiAqICAgICA8L2Zvcm0+XG4gKiAgICk7XG4gKiB9XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gdXNlRmllbGRBcnJheShwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IHsgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgbmFtZSwga2V5TmFtZSA9ICdpZCcsIHNob3VsZFVucmVnaXN0ZXIsIHJ1bGVzLCB9ID0gcHJvcHM7XG4gICAgY29uc3QgW2ZpZWxkcywgc2V0RmllbGRzXSA9IFJlYWN0X19kZWZhdWx0LnVzZVN0YXRlKGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSkpO1xuICAgIGNvbnN0IGlkcyA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZihjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpLm1hcChnZW5lcmF0ZUlkKSk7XG4gICAgY29uc3QgX2ZpZWxkSWRzID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKGZpZWxkcyk7XG4gICAgY29uc3QgX25hbWUgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYobmFtZSk7XG4gICAgY29uc3QgX2FjdGlvbmVkID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKGZhbHNlKTtcbiAgICBfbmFtZS5jdXJyZW50ID0gbmFtZTtcbiAgICBfZmllbGRJZHMuY3VycmVudCA9IGZpZWxkcztcbiAgICBjb250cm9sLl9uYW1lcy5hcnJheS5hZGQobmFtZSk7XG4gICAgcnVsZXMgJiZcbiAgICAgICAgY29udHJvbC5yZWdpc3RlcihuYW1lLCBydWxlcyk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IGNvbnRyb2wuX3N1YmplY3RzLmFycmF5LnN1YnNjcmliZSh7XG4gICAgICAgIG5leHQ6ICh7IHZhbHVlcywgbmFtZTogZmllbGRBcnJheU5hbWUsIH0pID0+IHtcbiAgICAgICAgICAgIGlmIChmaWVsZEFycmF5TmFtZSA9PT0gX25hbWUuY3VycmVudCB8fCAhZmllbGRBcnJheU5hbWUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBmaWVsZFZhbHVlcyA9IGdldCh2YWx1ZXMsIF9uYW1lLmN1cnJlbnQpO1xuICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGZpZWxkVmFsdWVzKSkge1xuICAgICAgICAgICAgICAgICAgICBzZXRGaWVsZHMoZmllbGRWYWx1ZXMpO1xuICAgICAgICAgICAgICAgICAgICBpZHMuY3VycmVudCA9IGZpZWxkVmFsdWVzLm1hcChnZW5lcmF0ZUlkKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgfSkudW5zdWJzY3JpYmUsIFtjb250cm9sXSk7XG4gICAgY29uc3QgdXBkYXRlVmFsdWVzID0gUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKSA9PiB7XG4gICAgICAgIF9hY3Rpb25lZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgfSwgW2NvbnRyb2wsIG5hbWVdKTtcbiAgICBjb25zdCBhcHBlbmQgPSAodmFsdWUsIG9wdGlvbnMpID0+IHtcbiAgICAgICAgY29uc3QgYXBwZW5kVmFsdWUgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBhcHBlbmRBdChjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpLCBhcHBlbmRWYWx1ZSk7XG4gICAgICAgIGNvbnRyb2wuX25hbWVzLmZvY3VzID0gZ2V0Rm9jdXNGaWVsZE5hbWUobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMubGVuZ3RoIC0gMSwgb3B0aW9ucyk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gYXBwZW5kQXQoaWRzLmN1cnJlbnQsIGFwcGVuZFZhbHVlLm1hcChnZW5lcmF0ZUlkKSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIGFwcGVuZEF0LCB7XG4gICAgICAgICAgICBhcmdBOiBmaWxsRW1wdHlBcnJheSh2YWx1ZSksXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgcHJlcGVuZCA9ICh2YWx1ZSwgb3B0aW9ucykgPT4ge1xuICAgICAgICBjb25zdCBwcmVwZW5kVmFsdWUgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBwcmVwZW5kQXQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgcHJlcGVuZFZhbHVlKTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSBnZXRGb2N1c0ZpZWxkTmFtZShuYW1lLCAwLCBvcHRpb25zKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSBwcmVwZW5kQXQoaWRzLmN1cnJlbnQsIHByZXBlbmRWYWx1ZS5tYXAoZ2VuZXJhdGVJZCkpO1xuICAgICAgICB1cGRhdGVWYWx1ZXModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBzZXRGaWVsZHModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBwcmVwZW5kQXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGZpbGxFbXB0eUFycmF5KHZhbHVlKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCByZW1vdmUgPSAoaW5kZXgpID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSByZW1vdmVBcnJheUF0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGluZGV4KTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSByZW1vdmVBcnJheUF0KGlkcy5jdXJyZW50LCBpbmRleCk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgICFBcnJheS5pc0FycmF5KGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpKSAmJlxuICAgICAgICAgICAgc2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSwgdW5kZWZpbmVkKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgcmVtb3ZlQXJyYXlBdCwge1xuICAgICAgICAgICAgYXJnQTogaW5kZXgsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgaW5zZXJ0JDEgPSAoaW5kZXgsIHZhbHVlLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGNvbnN0IGluc2VydFZhbHVlID0gY29udmVydFRvQXJyYXlQYXlsb2FkKGNsb25lT2JqZWN0KHZhbHVlKSk7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gaW5zZXJ0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGluZGV4LCBpbnNlcnRWYWx1ZSk7XG4gICAgICAgIGNvbnRyb2wuX25hbWVzLmZvY3VzID0gZ2V0Rm9jdXNGaWVsZE5hbWUobmFtZSwgaW5kZXgsIG9wdGlvbnMpO1xuICAgICAgICBpZHMuY3VycmVudCA9IGluc2VydChpZHMuY3VycmVudCwgaW5kZXgsIGluc2VydFZhbHVlLm1hcChnZW5lcmF0ZUlkKSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIGluc2VydCwge1xuICAgICAgICAgICAgYXJnQTogaW5kZXgsXG4gICAgICAgICAgICBhcmdCOiBmaWxsRW1wdHlBcnJheSh2YWx1ZSksXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3Qgc3dhcCA9IChpbmRleEEsIGluZGV4QikgPT4ge1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSk7XG4gICAgICAgIHN3YXBBcnJheUF0KHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBpbmRleEEsIGluZGV4Qik7XG4gICAgICAgIHN3YXBBcnJheUF0KGlkcy5jdXJyZW50LCBpbmRleEEsIGluZGV4Qik7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIHN3YXBBcnJheUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleEEsXG4gICAgICAgICAgICBhcmdCOiBpbmRleEIsXG4gICAgICAgIH0sIGZhbHNlKTtcbiAgICB9O1xuICAgIGNvbnN0IG1vdmUgPSAoZnJvbSwgdG8pID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpO1xuICAgICAgICBtb3ZlQXJyYXlBdCh1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgZnJvbSwgdG8pO1xuICAgICAgICBtb3ZlQXJyYXlBdChpZHMuY3VycmVudCwgZnJvbSwgdG8pO1xuICAgICAgICB1cGRhdGVWYWx1ZXModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBzZXRGaWVsZHModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBtb3ZlQXJyYXlBdCwge1xuICAgICAgICAgICAgYXJnQTogZnJvbSxcbiAgICAgICAgICAgIGFyZ0I6IHRvLFxuICAgICAgICB9LCBmYWxzZSk7XG4gICAgfTtcbiAgICBjb25zdCB1cGRhdGUgPSAoaW5kZXgsIHZhbHVlKSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZVZhbHVlID0gY2xvbmVPYmplY3QodmFsdWUpO1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IHVwZGF0ZUF0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGluZGV4LCB1cGRhdGVWYWx1ZSk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gWy4uLnVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzXS5tYXAoKGl0ZW0sIGkpID0+ICFpdGVtIHx8IGkgPT09IGluZGV4ID8gZ2VuZXJhdGVJZCgpIDogaWRzLmN1cnJlbnRbaV0pO1xuICAgICAgICB1cGRhdGVWYWx1ZXModXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpO1xuICAgICAgICBzZXRGaWVsZHMoWy4uLnVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzXSk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIHVwZGF0ZUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleCxcbiAgICAgICAgICAgIGFyZ0I6IHVwZGF0ZVZhbHVlLFxuICAgICAgICB9LCB0cnVlLCBmYWxzZSk7XG4gICAgfTtcbiAgICBjb25zdCByZXBsYWNlID0gKHZhbHVlKSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gY29udmVydFRvQXJyYXlQYXlsb2FkKGNsb25lT2JqZWN0KHZhbHVlKSk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMubWFwKGdlbmVyYXRlSWQpO1xuICAgICAgICB1cGRhdGVWYWx1ZXMoWy4uLnVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzXSk7XG4gICAgICAgIHNldEZpZWxkcyhbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCBbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdLCAoZGF0YSkgPT4gZGF0YSwge30sIHRydWUsIGZhbHNlKTtcbiAgICB9O1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnRyb2wuX3N0YXRlLmFjdGlvbiA9IGZhbHNlO1xuICAgICAgICBpc1dhdGNoZWQobmFtZSwgY29udHJvbC5fbmFtZXMpICYmXG4gICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAuLi5jb250cm9sLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgaWYgKF9hY3Rpb25lZC5jdXJyZW50ICYmXG4gICAgICAgICAgICAoIWdldFZhbGlkYXRpb25Nb2Rlcyhjb250cm9sLl9vcHRpb25zLm1vZGUpLmlzT25TdWJtaXQgfHxcbiAgICAgICAgICAgICAgICBjb250cm9sLl9mb3JtU3RhdGUuaXNTdWJtaXR0ZWQpICYmXG4gICAgICAgICAgICAhZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMucmVWYWxpZGF0ZU1vZGUpLmlzT25TdWJtaXQpIHtcbiAgICAgICAgICAgIGlmIChjb250cm9sLl9vcHRpb25zLnJlc29sdmVyKSB7XG4gICAgICAgICAgICAgICAgY29udHJvbC5fcnVuU2NoZW1hKFtuYW1lXSkudGhlbigocmVzdWx0KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVycm9yID0gZ2V0KHJlc3VsdC5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ0Vycm9yID0gZ2V0KGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoZXhpc3RpbmdFcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgPyAoIWVycm9yICYmIGV4aXN0aW5nRXJyb3IudHlwZSkgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXJyb3IgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGV4aXN0aW5nRXJyb3IudHlwZSAhPT0gZXJyb3IudHlwZSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdFcnJvci5tZXNzYWdlICE9PSBlcnJvci5tZXNzYWdlKSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDogZXJyb3IgJiYgZXJyb3IudHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHNldChjb250cm9sLl9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lLCBlcnJvcilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JzOiBjb250cm9sLl9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgaWYgKGZpZWxkICYmXG4gICAgICAgICAgICAgICAgICAgIGZpZWxkLl9mICYmXG4gICAgICAgICAgICAgICAgICAgICEoZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMucmVWYWxpZGF0ZU1vZGUpLmlzT25TdWJtaXQgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIGdldFZhbGlkYXRpb25Nb2Rlcyhjb250cm9sLl9vcHRpb25zLm1vZGUpLmlzT25TdWJtaXQpKSB7XG4gICAgICAgICAgICAgICAgICAgIHZhbGlkYXRlRmllbGQoZmllbGQsIGNvbnRyb2wuX25hbWVzLmRpc2FibGVkLCBjb250cm9sLl9mb3JtVmFsdWVzLCBjb250cm9sLl9vcHRpb25zLmNyaXRlcmlhTW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLmFsbCwgY29udHJvbC5fb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uLCB0cnVlKS50aGVuKChlcnJvcikgPT4gIWlzRW1wdHlPYmplY3QoZXJyb3IpICYmXG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnM6IHVwZGF0ZUZpZWxkQXJyYXlSb290RXJyb3IoY29udHJvbC5fZm9ybVN0YXRlLmVycm9ycywgZXJyb3IsIG5hbWUpLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB2YWx1ZXM6IGNsb25lT2JqZWN0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMpLFxuICAgICAgICB9KTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgJiZcbiAgICAgICAgICAgIGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihjb250cm9sLl9maWVsZHMsIChyZWYsIGtleSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChjb250cm9sLl9uYW1lcy5mb2N1cyAmJlxuICAgICAgICAgICAgICAgICAgICBrZXkuc3RhcnRzV2l0aChjb250cm9sLl9uYW1lcy5mb2N1cykgJiZcbiAgICAgICAgICAgICAgICAgICAgcmVmLmZvY3VzKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZi5mb2N1cygpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIGNvbnRyb2wuX25hbWVzLmZvY3VzID0gJyc7XG4gICAgICAgIGNvbnRyb2wuX3NldFZhbGlkKCk7XG4gICAgICAgIF9hY3Rpb25lZC5jdXJyZW50ID0gZmFsc2U7XG4gICAgfSwgW2ZpZWxkcywgbmFtZSwgY29udHJvbF0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgICFnZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSkgJiYgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZU1vdW50ZWQgPSAobmFtZSwgdmFsdWUpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBmaWVsZCA9IGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIGlmIChmaWVsZCAmJiBmaWVsZC5fZikge1xuICAgICAgICAgICAgICAgICAgICBmaWVsZC5fZi5tb3VudCA9IHZhbHVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb250cm9sLl9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXIgfHwgc2hvdWxkVW5yZWdpc3RlclxuICAgICAgICAgICAgICAgID8gY29udHJvbC51bnJlZ2lzdGVyKG5hbWUpXG4gICAgICAgICAgICAgICAgOiB1cGRhdGVNb3VudGVkKG5hbWUsIGZhbHNlKTtcbiAgICAgICAgfTtcbiAgICB9LCBbbmFtZSwgY29udHJvbCwga2V5TmFtZSwgc2hvdWxkVW5yZWdpc3Rlcl0pO1xuICAgIHJldHVybiB7XG4gICAgICAgIHN3YXA6IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKHN3YXAsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgbW92ZTogUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2sobW92ZSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICBwcmVwZW5kOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhwcmVwZW5kLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIGFwcGVuZDogUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soYXBwZW5kLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIHJlbW92ZTogUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2socmVtb3ZlLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIGluc2VydDogUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soaW5zZXJ0JDEsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgdXBkYXRlOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayh1cGRhdGUsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgcmVwbGFjZTogUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2socmVwbGFjZSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICBmaWVsZHM6IFJlYWN0X19kZWZhdWx0LnVzZU1lbW8oKCkgPT4gZmllbGRzLm1hcCgoZmllbGQsIGluZGV4KSA9PiAoe1xuICAgICAgICAgICAgLi4uZmllbGQsXG4gICAgICAgICAgICBba2V5TmFtZV06IGlkcy5jdXJyZW50W2luZGV4XSB8fCBnZW5lcmF0ZUlkKCksXG4gICAgICAgIH0pKSwgW2ZpZWxkcywga2V5TmFtZV0pLFxuICAgIH07XG59XG5cbi8qKlxuICogQ3VzdG9tIGhvb2sgdG8gbWFuYWdlIHRoZSBlbnRpcmUgZm9ybS5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybSkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLWdldC1zdGFydGVkLXRzLTVrc21tKSDigKIgW1ZpZGVvXShodHRwczovL3d3dy55b3V0dWJlLmNvbS93YXRjaD92PVJrWHY0QVhYQ180KVxuICpcbiAqIEBwYXJhbSBwcm9wcyAtIGZvcm0gY29uZmlndXJhdGlvbiBhbmQgdmFsaWRhdGlvbiBwYXJhbWV0ZXJzLlxuICpcbiAqIEByZXR1cm5zIG1ldGhvZHMgLSBpbmRpdmlkdWFsIGZ1bmN0aW9ucyB0byBtYW5hZ2UgdGhlIGZvcm0gc3RhdGUuIHtAbGluayBVc2VGb3JtUmV0dXJufVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyByZWdpc3RlciwgaGFuZGxlU3VibWl0LCB3YXRjaCwgZm9ybVN0YXRlOiB7IGVycm9ycyB9IH0gPSB1c2VGb3JtKCk7XG4gKiAgIGNvbnN0IG9uU3VibWl0ID0gZGF0YSA9PiBjb25zb2xlLmxvZyhkYXRhKTtcbiAqXG4gKiAgIGNvbnNvbGUubG9nKHdhdGNoKFwiZXhhbXBsZVwiKSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgIDxpbnB1dCBkZWZhdWx0VmFsdWU9XCJ0ZXN0XCIgey4uLnJlZ2lzdGVyKFwiZXhhbXBsZVwiKX0gLz5cbiAqICAgICAgIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJleGFtcGxlUmVxdWlyZWRcIiwgeyByZXF1aXJlZDogdHJ1ZSB9KX0gLz5cbiAqICAgICAgIHtlcnJvcnMuZXhhbXBsZVJlcXVpcmVkICYmIDxzcGFuPlRoaXMgZmllbGQgaXMgcmVxdWlyZWQ8L3NwYW4+fVxuICogICAgICAgPGJ1dHRvbj5TdWJtaXQ8L2J1dHRvbj5cbiAqICAgICA8L2Zvcm0+XG4gKiAgICk7XG4gKiB9XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gdXNlRm9ybShwcm9wcyA9IHt9KSB7XG4gICAgY29uc3QgX2Zvcm1Db250cm9sID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKHVuZGVmaW5lZCk7XG4gICAgY29uc3QgX3ZhbHVlcyA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZih1bmRlZmluZWQpO1xuICAgIGNvbnN0IFtmb3JtU3RhdGUsIHVwZGF0ZUZvcm1TdGF0ZV0gPSBSZWFjdF9fZGVmYXVsdC51c2VTdGF0ZSh7XG4gICAgICAgIGlzRGlydHk6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkYXRpbmc6IGZhbHNlLFxuICAgICAgICBpc0xvYWRpbmc6IGlzRnVuY3Rpb24ocHJvcHMuZGVmYXVsdFZhbHVlcyksXG4gICAgICAgIGlzU3VibWl0dGVkOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXRTdWNjZXNzZnVsOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIHN1Ym1pdENvdW50OiAwLFxuICAgICAgICBkaXJ0eUZpZWxkczoge30sXG4gICAgICAgIHRvdWNoZWRGaWVsZHM6IHt9LFxuICAgICAgICB2YWxpZGF0aW5nRmllbGRzOiB7fSxcbiAgICAgICAgZXJyb3JzOiBwcm9wcy5lcnJvcnMgfHwge30sXG4gICAgICAgIGRpc2FibGVkOiBwcm9wcy5kaXNhYmxlZCB8fCBmYWxzZSxcbiAgICAgICAgaXNSZWFkeTogZmFsc2UsXG4gICAgICAgIGRlZmF1bHRWYWx1ZXM6IGlzRnVuY3Rpb24ocHJvcHMuZGVmYXVsdFZhbHVlcylcbiAgICAgICAgICAgID8gdW5kZWZpbmVkXG4gICAgICAgICAgICA6IHByb3BzLmRlZmF1bHRWYWx1ZXMsXG4gICAgfSk7XG4gICAgaWYgKCFfZm9ybUNvbnRyb2wuY3VycmVudCkge1xuICAgICAgICBfZm9ybUNvbnRyb2wuY3VycmVudCA9IHtcbiAgICAgICAgICAgIC4uLihwcm9wcy5mb3JtQ29udHJvbCA/IHByb3BzLmZvcm1Db250cm9sIDogY3JlYXRlRm9ybUNvbnRyb2wocHJvcHMpKSxcbiAgICAgICAgICAgIGZvcm1TdGF0ZSxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHByb3BzLmZvcm1Db250cm9sICYmXG4gICAgICAgICAgICBwcm9wcy5kZWZhdWx0VmFsdWVzICYmXG4gICAgICAgICAgICAhaXNGdW5jdGlvbihwcm9wcy5kZWZhdWx0VmFsdWVzKSkge1xuICAgICAgICAgICAgcHJvcHMuZm9ybUNvbnRyb2wucmVzZXQocHJvcHMuZGVmYXVsdFZhbHVlcywgcHJvcHMucmVzZXRPcHRpb25zKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjb25zdCBjb250cm9sID0gX2Zvcm1Db250cm9sLmN1cnJlbnQuY29udHJvbDtcbiAgICBjb250cm9sLl9vcHRpb25zID0gcHJvcHM7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnN0IHN1YiA9IGNvbnRyb2wuX3N1YnNjcmliZSh7XG4gICAgICAgICAgICBmb3JtU3RhdGU6IGNvbnRyb2wuX3Byb3h5Rm9ybVN0YXRlLFxuICAgICAgICAgICAgY2FsbGJhY2s6ICgpID0+IHVwZGF0ZUZvcm1TdGF0ZSh7IC4uLmNvbnRyb2wuX2Zvcm1TdGF0ZSB9KSxcbiAgICAgICAgICAgIHJlUmVuZGVyUm9vdDogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgICAgIHVwZGF0ZUZvcm1TdGF0ZSgoZGF0YSkgPT4gKHtcbiAgICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgICBpc1JlYWR5OiB0cnVlLFxuICAgICAgICB9KSk7XG4gICAgICAgIGNvbnRyb2wuX2Zvcm1TdGF0ZS5pc1JlYWR5ID0gdHJ1ZTtcbiAgICAgICAgcmV0dXJuIHN1YjtcbiAgICB9LCBbY29udHJvbF0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiBjb250cm9sLl9kaXNhYmxlRm9ybShwcm9wcy5kaXNhYmxlZCksIFtjb250cm9sLCBwcm9wcy5kaXNhYmxlZF0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChwcm9wcy5tb2RlKSB7XG4gICAgICAgICAgICBjb250cm9sLl9vcHRpb25zLm1vZGUgPSBwcm9wcy5tb2RlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm9wcy5yZVZhbGlkYXRlTW9kZSkge1xuICAgICAgICAgICAgY29udHJvbC5fb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSA9IHByb3BzLnJlVmFsaWRhdGVNb2RlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm9wcy5lcnJvcnMgJiYgIWlzRW1wdHlPYmplY3QocHJvcHMuZXJyb3JzKSkge1xuICAgICAgICAgICAgY29udHJvbC5fc2V0RXJyb3JzKHByb3BzLmVycm9ycyk7XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbCwgcHJvcHMuZXJyb3JzLCBwcm9wcy5tb2RlLCBwcm9wcy5yZVZhbGlkYXRlTW9kZV0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIHByb3BzLnNob3VsZFVucmVnaXN0ZXIgJiZcbiAgICAgICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIHZhbHVlczogY29udHJvbC5fZ2V0V2F0Y2goKSxcbiAgICAgICAgICAgIH0pO1xuICAgIH0sIFtjb250cm9sLCBwcm9wcy5zaG91bGRVbnJlZ2lzdGVyXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKGNvbnRyb2wuX3Byb3h5Rm9ybVN0YXRlLmlzRGlydHkpIHtcbiAgICAgICAgICAgIGNvbnN0IGlzRGlydHkgPSBjb250cm9sLl9nZXREaXJ0eSgpO1xuICAgICAgICAgICAgaWYgKGlzRGlydHkgIT09IGZvcm1TdGF0ZS5pc0RpcnR5KSB7XG4gICAgICAgICAgICAgICAgY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgIGlzRGlydHksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbCwgZm9ybVN0YXRlLmlzRGlydHldKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAocHJvcHMudmFsdWVzICYmICFkZWVwRXF1YWwocHJvcHMudmFsdWVzLCBfdmFsdWVzLmN1cnJlbnQpKSB7XG4gICAgICAgICAgICBjb250cm9sLl9yZXNldChwcm9wcy52YWx1ZXMsIGNvbnRyb2wuX29wdGlvbnMucmVzZXRPcHRpb25zKTtcbiAgICAgICAgICAgIF92YWx1ZXMuY3VycmVudCA9IHByb3BzLnZhbHVlcztcbiAgICAgICAgICAgIHVwZGF0ZUZvcm1TdGF0ZSgoc3RhdGUpID0+ICh7IC4uLnN0YXRlIH0pKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX3Jlc2V0RGVmYXVsdFZhbHVlcygpO1xuICAgICAgICB9XG4gICAgfSwgW2NvbnRyb2wsIHByb3BzLnZhbHVlc10pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmICghY29udHJvbC5fc3RhdGUubW91bnQpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX3NldFZhbGlkKCk7XG4gICAgICAgICAgICBjb250cm9sLl9zdGF0ZS5tb3VudCA9IHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNvbnRyb2wuX3N0YXRlLndhdGNoKSB7XG4gICAgICAgICAgICBjb250cm9sLl9zdGF0ZS53YXRjaCA9IGZhbHNlO1xuICAgICAgICAgICAgY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7IC4uLmNvbnRyb2wuX2Zvcm1TdGF0ZSB9KTtcbiAgICAgICAgfVxuICAgICAgICBjb250cm9sLl9yZW1vdmVVbm1vdW50ZWQoKTtcbiAgICB9KTtcbiAgICBfZm9ybUNvbnRyb2wuY3VycmVudC5mb3JtU3RhdGUgPSBnZXRQcm94eUZvcm1TdGF0ZShmb3JtU3RhdGUsIGNvbnRyb2wpO1xuICAgIHJldHVybiBfZm9ybUNvbnRyb2wuY3VycmVudDtcbn1cblxuZXhwb3J0IHsgQ29udHJvbGxlciwgRm9ybSwgRm9ybVByb3ZpZGVyLCBhcHBlbmRFcnJvcnMsIGNyZWF0ZUZvcm1Db250cm9sLCBnZXQsIHNldCwgdXNlQ29udHJvbGxlciwgdXNlRmllbGRBcnJheSwgdXNlRm9ybSwgdXNlRm9ybUNvbnRleHQsIHVzZUZvcm1TdGF0ZSwgdXNlV2F0Y2ggfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmVzbS5tanMubWFwXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJSZWFjdF9fZGVmYXVsdCIsImlzQ2hlY2tCb3hJbnB1dCIsImVsZW1lbnQiLCJ0eXBlIiwiaXNEYXRlT2JqZWN0IiwidmFsdWUiLCJEYXRlIiwiaXNOdWxsT3JVbmRlZmluZWQiLCJpc09iamVjdFR5cGUiLCJpc09iamVjdCIsIkFycmF5IiwiaXNBcnJheSIsImdldEV2ZW50VmFsdWUiLCJldmVudCIsInRhcmdldCIsImNoZWNrZWQiLCJnZXROb2RlUGFyZW50TmFtZSIsIm5hbWUiLCJzdWJzdHJpbmciLCJzZWFyY2giLCJpc05hbWVJbkZpZWxkQXJyYXkiLCJuYW1lcyIsImhhcyIsImlzUGxhaW5PYmplY3QiLCJ0ZW1wT2JqZWN0IiwicHJvdG90eXBlQ29weSIsImNvbnN0cnVjdG9yIiwicHJvdG90eXBlIiwiaGFzT3duUHJvcGVydHkiLCJpc1dlYiIsIndpbmRvdyIsIkhUTUxFbGVtZW50IiwiZG9jdW1lbnQiLCJjbG9uZU9iamVjdCIsImRhdGEiLCJjb3B5IiwiaXNGaWxlTGlzdEluc3RhbmNlIiwiRmlsZUxpc3QiLCJTZXQiLCJCbG9iIiwia2V5IiwiY29tcGFjdCIsImZpbHRlciIsIkJvb2xlYW4iLCJpc1VuZGVmaW5lZCIsInZhbCIsInVuZGVmaW5lZCIsImdldCIsIm9iamVjdCIsInBhdGgiLCJkZWZhdWx0VmFsdWUiLCJyZXN1bHQiLCJzcGxpdCIsInJlZHVjZSIsImlzQm9vbGVhbiIsImlzS2V5IiwidGVzdCIsInN0cmluZ1RvUGF0aCIsImlucHV0IiwicmVwbGFjZSIsInNldCIsImluZGV4IiwidGVtcFBhdGgiLCJsZW5ndGgiLCJsYXN0SW5kZXgiLCJuZXdWYWx1ZSIsIm9ialZhbHVlIiwiaXNOYU4iLCJFVkVOVFMiLCJCTFVSIiwiRk9DVVNfT1VUIiwiQ0hBTkdFIiwiVkFMSURBVElPTl9NT0RFIiwib25CbHVyIiwib25DaGFuZ2UiLCJvblN1Ym1pdCIsIm9uVG91Y2hlZCIsImFsbCIsIklOUFVUX1ZBTElEQVRJT05fUlVMRVMiLCJtYXgiLCJtaW4iLCJtYXhMZW5ndGgiLCJtaW5MZW5ndGgiLCJwYXR0ZXJuIiwicmVxdWlyZWQiLCJ2YWxpZGF0ZSIsIkhvb2tGb3JtQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VGb3JtQ29udGV4dCIsInVzZUNvbnRleHQiLCJGb3JtUHJvdmlkZXIiLCJwcm9wcyIsImNoaWxkcmVuIiwiY3JlYXRlRWxlbWVudCIsIlByb3ZpZGVyIiwiZ2V0UHJveHlGb3JtU3RhdGUiLCJmb3JtU3RhdGUiLCJjb250cm9sIiwibG9jYWxQcm94eUZvcm1TdGF0ZSIsImlzUm9vdCIsImRlZmF1bHRWYWx1ZXMiLCJfZGVmYXVsdFZhbHVlcyIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiX2tleSIsIl9wcm94eUZvcm1TdGF0ZSIsInVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QiLCJ1c2VMYXlvdXRFZmZlY3QiLCJ1c2VFZmZlY3QiLCJ1c2VGb3JtU3RhdGUiLCJtZXRob2RzIiwiZGlzYWJsZWQiLCJleGFjdCIsInVwZGF0ZUZvcm1TdGF0ZSIsInVzZVN0YXRlIiwiX2Zvcm1TdGF0ZSIsIl9sb2NhbFByb3h5Rm9ybVN0YXRlIiwidXNlUmVmIiwiaXNEaXJ0eSIsImlzTG9hZGluZyIsImRpcnR5RmllbGRzIiwidG91Y2hlZEZpZWxkcyIsInZhbGlkYXRpbmdGaWVsZHMiLCJpc1ZhbGlkYXRpbmciLCJpc1ZhbGlkIiwiZXJyb3JzIiwiX3N1YnNjcmliZSIsImN1cnJlbnQiLCJjYWxsYmFjayIsIl9zZXRWYWxpZCIsInVzZU1lbW8iLCJpc1N0cmluZyIsImdlbmVyYXRlV2F0Y2hPdXRwdXQiLCJfbmFtZXMiLCJmb3JtVmFsdWVzIiwiaXNHbG9iYWwiLCJ3YXRjaCIsImFkZCIsIm1hcCIsImZpZWxkTmFtZSIsIndhdGNoQWxsIiwidXNlV2F0Y2giLCJfZGVmYXVsdFZhbHVlIiwidXBkYXRlVmFsdWUiLCJfZ2V0V2F0Y2giLCJ2YWx1ZXMiLCJfZm9ybVZhbHVlcyIsIl9yZW1vdmVVbm1vdW50ZWQiLCJ1c2VDb250cm9sbGVyIiwic2hvdWxkVW5yZWdpc3RlciIsImlzQXJyYXlGaWVsZCIsImFycmF5IiwiX3Byb3BzIiwiX3JlZ2lzdGVyUHJvcHMiLCJyZWdpc3RlciIsInJ1bGVzIiwiZmllbGRTdGF0ZSIsImRlZmluZVByb3BlcnRpZXMiLCJpbnZhbGlkIiwiZW51bWVyYWJsZSIsImlzVG91Y2hlZCIsImVycm9yIiwidXNlQ2FsbGJhY2siLCJyZWYiLCJlbG0iLCJmaWVsZCIsIl9maWVsZHMiLCJfZiIsImZvY3VzIiwic2VsZWN0Iiwic2V0Q3VzdG9tVmFsaWRpdHkiLCJtZXNzYWdlIiwicmVwb3J0VmFsaWRpdHkiLCJfc2hvdWxkVW5yZWdpc3RlckZpZWxkIiwiX29wdGlvbnMiLCJ1cGRhdGVNb3VudGVkIiwibW91bnQiLCJfc3RhdGUiLCJhY3Rpb24iLCJ1bnJlZ2lzdGVyIiwiX3NldERpc2FibGVkRmllbGQiLCJDb250cm9sbGVyIiwicmVuZGVyIiwiZmxhdHRlbiIsIm9iaiIsIm91dHB1dCIsImtleXMiLCJuZXN0ZWQiLCJuZXN0ZWRLZXkiLCJQT1NUX1JFUVVFU1QiLCJGb3JtIiwibW91bnRlZCIsInNldE1vdW50ZWQiLCJtZXRob2QiLCJoZWFkZXJzIiwiZW5jVHlwZSIsIm9uRXJyb3IiLCJvblN1Y2Nlc3MiLCJ2YWxpZGF0ZVN0YXR1cyIsInJlc3QiLCJzdWJtaXQiLCJoYXNFcnJvciIsImhhbmRsZVN1Ym1pdCIsImZvcm1EYXRhIiwiRm9ybURhdGEiLCJmb3JtRGF0YUpzb24iLCJKU09OIiwic3RyaW5naWZ5IiwiX2EiLCJmbGF0dGVuRm9ybVZhbHVlcyIsImFwcGVuZCIsInNob3VsZFN0cmluZ2lmeVN1Ym1pc3Npb25EYXRhIiwic29tZSIsImluY2x1ZGVzIiwicmVzcG9uc2UiLCJmZXRjaCIsIlN0cmluZyIsImJvZHkiLCJzdGF0dXMiLCJfc3ViamVjdHMiLCJzdGF0ZSIsIm5leHQiLCJpc1N1Ym1pdFN1Y2Nlc3NmdWwiLCJzZXRFcnJvciIsIkZyYWdtZW50Iiwibm9WYWxpZGF0ZSIsImFwcGVuZEVycm9ycyIsInZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSIsInR5cGVzIiwiY29udmVydFRvQXJyYXlQYXlsb2FkIiwiY3JlYXRlU3ViamVjdCIsIl9vYnNlcnZlcnMiLCJvYnNlcnZlciIsInN1YnNjcmliZSIsInB1c2giLCJ1bnN1YnNjcmliZSIsIm8iLCJvYnNlcnZlcnMiLCJpc1ByaW1pdGl2ZSIsImRlZXBFcXVhbCIsIm9iamVjdDEiLCJvYmplY3QyIiwiZ2V0VGltZSIsImtleXMxIiwia2V5czIiLCJ2YWwxIiwidmFsMiIsImlzRW1wdHlPYmplY3QiLCJpc0ZpbGVJbnB1dCIsImlzRnVuY3Rpb24iLCJpc0hUTUxFbGVtZW50Iiwib3duZXIiLCJvd25lckRvY3VtZW50IiwiZGVmYXVsdFZpZXciLCJpc011bHRpcGxlU2VsZWN0IiwiaXNSYWRpb0lucHV0IiwiaXNSYWRpb09yQ2hlY2tib3giLCJsaXZlIiwiaXNDb25uZWN0ZWQiLCJiYXNlR2V0IiwidXBkYXRlUGF0aCIsInNsaWNlIiwiaXNFbXB0eUFycmF5IiwidW5zZXQiLCJwYXRocyIsImNoaWxkT2JqZWN0Iiwib2JqZWN0SGFzRnVuY3Rpb24iLCJtYXJrRmllbGRzRGlydHkiLCJmaWVsZHMiLCJpc1BhcmVudE5vZGVBcnJheSIsImdldERpcnR5RmllbGRzRnJvbURlZmF1bHRWYWx1ZXMiLCJkaXJ0eUZpZWxkc0Zyb21WYWx1ZXMiLCJnZXREaXJ0eUZpZWxkcyIsImRlZmF1bHRSZXN1bHQiLCJ2YWxpZFJlc3VsdCIsImdldENoZWNrYm94VmFsdWUiLCJvcHRpb25zIiwib3B0aW9uIiwiYXR0cmlidXRlcyIsImdldEZpZWxkVmFsdWVBcyIsInZhbHVlQXNOdW1iZXIiLCJ2YWx1ZUFzRGF0ZSIsInNldFZhbHVlQXMiLCJOYU4iLCJkZWZhdWx0UmV0dXJuIiwiZ2V0UmFkaW9WYWx1ZSIsInByZXZpb3VzIiwiZ2V0RmllbGRWYWx1ZSIsImZpbGVzIiwicmVmcyIsInNlbGVjdGVkT3B0aW9ucyIsImdldFJlc29sdmVyT3B0aW9ucyIsImZpZWxkc05hbWVzIiwiY3JpdGVyaWFNb2RlIiwic2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbiIsImlzUmVnZXgiLCJSZWdFeHAiLCJnZXRSdWxlVmFsdWUiLCJydWxlIiwic291cmNlIiwiZ2V0VmFsaWRhdGlvbk1vZGVzIiwibW9kZSIsImlzT25TdWJtaXQiLCJpc09uQmx1ciIsImlzT25DaGFuZ2UiLCJpc09uQWxsIiwiaXNPblRvdWNoIiwiQVNZTkNfRlVOQ1RJT04iLCJoYXNQcm9taXNlVmFsaWRhdGlvbiIsImZpZWxkUmVmZXJlbmNlIiwiZmluZCIsInZhbGlkYXRlRnVuY3Rpb24iLCJoYXNWYWxpZGF0aW9uIiwiaXNXYXRjaGVkIiwiaXNCbHVyRXZlbnQiLCJ3YXRjaE5hbWUiLCJzdGFydHNXaXRoIiwiaXRlcmF0ZUZpZWxkc0J5QWN0aW9uIiwiYWJvcnRFYXJseSIsImN1cnJlbnRGaWVsZCIsInNjaGVtYUVycm9yTG9va3VwIiwiam9pbiIsImZvdW5kRXJyb3IiLCJwb3AiLCJzaG91bGRSZW5kZXJGb3JtU3RhdGUiLCJmb3JtU3RhdGVEYXRhIiwic2hvdWxkU3Vic2NyaWJlQnlOYW1lIiwic2lnbmFsTmFtZSIsImN1cnJlbnROYW1lIiwic2tpcFZhbGlkYXRpb24iLCJpc1N1Ym1pdHRlZCIsInJlVmFsaWRhdGVNb2RlIiwidW5zZXRFbXB0eUFycmF5IiwidXBkYXRlRmllbGRBcnJheVJvb3RFcnJvciIsImZpZWxkQXJyYXlFcnJvcnMiLCJpc01lc3NhZ2UiLCJnZXRWYWxpZGF0ZUVycm9yIiwiZXZlcnkiLCJnZXRWYWx1ZUFuZE1lc3NhZ2UiLCJ2YWxpZGF0aW9uRGF0YSIsInZhbGlkYXRlRmllbGQiLCJkaXNhYmxlZEZpZWxkTmFtZXMiLCJpc0ZpZWxkQXJyYXkiLCJpbnB1dFZhbHVlIiwiaW5wdXRSZWYiLCJpc1JhZGlvIiwiaXNDaGVja0JveCIsImlzRW1wdHkiLCJhcHBlbmRFcnJvcnNDdXJyeSIsImJpbmQiLCJnZXRNaW5NYXhNZXNzYWdlIiwiZXhjZWVkTWF4IiwibWF4TGVuZ3RoTWVzc2FnZSIsIm1pbkxlbmd0aE1lc3NhZ2UiLCJtYXhUeXBlIiwibWluVHlwZSIsImV4Y2VlZE1pbiIsIm1heE91dHB1dCIsIm1pbk91dHB1dCIsInZhbHVlTnVtYmVyIiwidmFsdWVEYXRlIiwiY29udmVydFRpbWVUb0RhdGUiLCJ0aW1lIiwidG9EYXRlU3RyaW5nIiwiaXNUaW1lIiwiaXNXZWVrIiwibWF4TGVuZ3RoT3V0cHV0IiwibWluTGVuZ3RoT3V0cHV0IiwicGF0dGVyblZhbHVlIiwibWF0Y2giLCJ2YWxpZGF0ZUVycm9yIiwidmFsaWRhdGlvblJlc3VsdCIsImRlZmF1bHRPcHRpb25zIiwic2hvdWxkRm9jdXNFcnJvciIsImNyZWF0ZUZvcm1Db250cm9sIiwic3VibWl0Q291bnQiLCJpc1JlYWR5IiwiaXNTdWJtaXR0aW5nIiwidW5Nb3VudCIsImRlbGF5RXJyb3JDYWxsYmFjayIsInRpbWVyIiwiX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlIiwic2hvdWxkRGlzcGxheUFsbEFzc29jaWF0ZWRFcnJvcnMiLCJkZWJvdW5jZSIsIndhaXQiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0Iiwic2hvdWxkVXBkYXRlVmFsaWQiLCJyZXNvbHZlciIsIl9ydW5TY2hlbWEiLCJleGVjdXRlQnVpbHRJblZhbGlkYXRpb24iLCJfdXBkYXRlSXNWYWxpZGF0aW5nIiwiZnJvbSIsImZvckVhY2giLCJfc2V0RmllbGRBcnJheSIsImFyZ3MiLCJzaG91bGRTZXRWYWx1ZXMiLCJzaG91bGRVcGRhdGVGaWVsZHNBbmRTdGF0ZSIsImZpZWxkVmFsdWVzIiwiYXJnQSIsImFyZ0IiLCJfZ2V0RGlydHkiLCJ1cGRhdGVFcnJvcnMiLCJfc2V0RXJyb3JzIiwidXBkYXRlVmFsaWRBbmRWYWx1ZSIsInNob3VsZFNraXBTZXRWYWx1ZUFzIiwiZGVmYXVsdENoZWNrZWQiLCJzZXRGaWVsZFZhbHVlIiwidXBkYXRlVG91Y2hBbmREaXJ0eSIsImZpZWxkVmFsdWUiLCJzaG91bGREaXJ0eSIsInNob3VsZFJlbmRlciIsInNob3VsZFVwZGF0ZUZpZWxkIiwiaXNQcmV2aW91c0RpcnR5IiwiaXNDdXJyZW50RmllbGRQcmlzdGluZSIsImlzUHJldmlvdXNGaWVsZFRvdWNoZWQiLCJzaG91bGRSZW5kZXJCeUVycm9yIiwicHJldmlvdXNGaWVsZEVycm9yIiwiZGVsYXlFcnJvciIsInVwZGF0ZWRGb3JtU3RhdGUiLCJjb250ZXh0IiwiZXhlY3V0ZVNjaGVtYUFuZFVwZGF0ZVN0YXRlIiwic2hvdWxkT25seUNoZWNrVmFsaWQiLCJ2YWxpZCIsImlzRmllbGRBcnJheVJvb3QiLCJpc1Byb21pc2VGdW5jdGlvbiIsImZpZWxkRXJyb3IiLCJnZXRWYWx1ZXMiLCJfZ2V0RmllbGRBcnJheSIsIm9wdGlvblJlZiIsInNlbGVjdGVkIiwiY2hlY2tib3hSZWYiLCJyYWRpb1JlZiIsInNob3VsZFRvdWNoIiwic2hvdWxkVmFsaWRhdGUiLCJ0cmlnZ2VyIiwic2V0VmFsdWVzIiwiZmllbGRLZXkiLCJzZXRWYWx1ZSIsImNsb25lVmFsdWUiLCJpc0ZpZWxkVmFsdWVVcGRhdGVkIiwiX3VwZGF0ZUlzRmllbGRWYWx1ZVVwZGF0ZWQiLCJOdW1iZXIiLCJ2YWxpZGF0aW9uTW9kZUJlZm9yZVN1Ym1pdCIsInZhbGlkYXRpb25Nb2RlQWZ0ZXJTdWJtaXQiLCJzaG91bGRTa2lwVmFsaWRhdGlvbiIsImRlcHMiLCJ3YXRjaGVkIiwicHJldmlvdXNFcnJvckxvb2t1cFJlc3VsdCIsImVycm9yTG9va3VwUmVzdWx0IiwiX2ZvY3VzSW5wdXQiLCJmaWVsZE5hbWVzIiwiUHJvbWlzZSIsInNob3VsZEZvY3VzIiwiZ2V0RmllbGRTdGF0ZSIsImNsZWFyRXJyb3JzIiwiaW5wdXROYW1lIiwiY3VycmVudEVycm9yIiwiY3VycmVudFJlZiIsInJlc3RPZkVycm9yVHJlZSIsInBheWxvYWQiLCJfc2V0Rm9ybVN0YXRlIiwicmVSZW5kZXJSb290IiwiZGVsZXRlIiwia2VlcFZhbHVlIiwia2VlcEVycm9yIiwia2VlcERpcnR5Iiwia2VlcFRvdWNoZWQiLCJrZWVwSXNWYWxpZGF0aW5nIiwia2VlcERlZmF1bHRWYWx1ZSIsImtlZXBJc1ZhbGlkIiwiZGlzYWJsZWRJc0RlZmluZWQiLCJwcm9ncmVzc2l2ZSIsImZpZWxkUmVmIiwicXVlcnlTZWxlY3RvckFsbCIsInJhZGlvT3JDaGVja2JveCIsIl9mb2N1c0Vycm9yIiwiX2Rpc2FibGVGb3JtIiwib25WYWxpZCIsIm9uSW52YWxpZCIsImUiLCJvblZhbGlkRXJyb3IiLCJwcmV2ZW50RGVmYXVsdCIsInBlcnNpc3QiLCJzaXplIiwicmVzZXRGaWVsZCIsIl9yZXNldCIsImtlZXBTdGF0ZU9wdGlvbnMiLCJ1cGRhdGVkVmFsdWVzIiwiY2xvbmVVcGRhdGVkVmFsdWVzIiwiaXNFbXB0eVJlc2V0VmFsdWVzIiwia2VlcERlZmF1bHRWYWx1ZXMiLCJrZWVwVmFsdWVzIiwia2VlcERpcnR5VmFsdWVzIiwiZmllbGRzVG9DaGVjayIsImZvcm0iLCJjbG9zZXN0IiwicmVzZXQiLCJrZWVwU3VibWl0Q291bnQiLCJrZWVwSXNTdWJtaXR0ZWQiLCJrZWVwRXJyb3JzIiwia2VlcElzU3VibWl0U3VjY2Vzc2Z1bCIsInNldEZvY3VzIiwic2hvdWxkU2VsZWN0IiwiX3Jlc2V0RGVmYXVsdFZhbHVlcyIsInRoZW4iLCJyZXNldE9wdGlvbnMiLCJmb3JtQ29udHJvbCIsImdlbmVyYXRlSWQiLCJkIiwicGVyZm9ybWFuY2UiLCJub3ciLCJjIiwiciIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsImdldEZvY3VzRmllbGROYW1lIiwiZm9jdXNOYW1lIiwiZm9jdXNJbmRleCIsImFwcGVuZEF0IiwiZmlsbEVtcHR5QXJyYXkiLCJpbnNlcnQiLCJtb3ZlQXJyYXlBdCIsInRvIiwic3BsaWNlIiwicHJlcGVuZEF0IiwicmVtb3ZlQXRJbmRleGVzIiwiaW5kZXhlcyIsImkiLCJ0ZW1wIiwicmVtb3ZlQXJyYXlBdCIsInNvcnQiLCJhIiwiYiIsInN3YXBBcnJheUF0IiwiaW5kZXhBIiwiaW5kZXhCIiwidXBkYXRlQXQiLCJ1c2VGaWVsZEFycmF5Iiwia2V5TmFtZSIsInNldEZpZWxkcyIsImlkcyIsIl9maWVsZElkcyIsIl9uYW1lIiwiX2FjdGlvbmVkIiwiZmllbGRBcnJheU5hbWUiLCJ1cGRhdGVWYWx1ZXMiLCJ1cGRhdGVkRmllbGRBcnJheVZhbHVlcyIsImFwcGVuZFZhbHVlIiwicHJlcGVuZCIsInByZXBlbmRWYWx1ZSIsInJlbW92ZSIsImluc2VydCQxIiwiaW5zZXJ0VmFsdWUiLCJzd2FwIiwibW92ZSIsInVwZGF0ZSIsIml0ZW0iLCJleGlzdGluZ0Vycm9yIiwidXNlRm9ybSIsIl9mb3JtQ29udHJvbCIsIl92YWx1ZXMiLCJzdWIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;