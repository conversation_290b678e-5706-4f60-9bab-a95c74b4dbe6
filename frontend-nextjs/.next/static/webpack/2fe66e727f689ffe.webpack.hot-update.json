{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "(app-pages-browser)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-arrow/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-menu/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-popper/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-presence/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-size/dist/index.mjs", "(app-pages-browser)/./node_modules/aria-hidden/dist/es2015/index.js", "(app-pages-browser)/./node_modules/get-nonce/dist/es2015/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/Combination.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/UI.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/hook.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/singleton.js", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/assignRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useRef.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/exports.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/medium.js", "(app-pages-browser)/./src/components/ui/dropdown-menu.tsx"]}