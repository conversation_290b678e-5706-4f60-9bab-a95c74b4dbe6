"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/market-overview.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/market-overview.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverview: function() { return /* binding */ MarketOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketOverview auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MarketOverview() {\n    _s();\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMarketData = async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.dashboardApi.getMarketData([\n                    \"BTCUSDT\",\n                    \"ETHUSDT\",\n                    \"BNBUSDT\",\n                    \"ADAUSDT\"\n                ]);\n                setMarketData(data || []);\n            } catch (error) {\n                console.error(\"获取市场数据失败:\", error);\n                // 使用模拟数据作为后备\n                setMarketData([\n                    {\n                        symbol: \"BTCUSDT\",\n                        price: 43250.50,\n                        change_24h: 1250.75,\n                        change_24h_pct: 2.98,\n                        volume_24h: 28450000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"ETHUSDT\",\n                        price: 2650.75,\n                        change_24h: -85.25,\n                        change_24h_pct: -3.11,\n                        volume_24h: 15230000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"BNBUSDT\",\n                        price: 315.80,\n                        change_24h: 12.45,\n                        change_24h_pct: 4.10,\n                        volume_24h: 1850000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"ADAUSDT\",\n                        price: 0.4825,\n                        change_24h: 0.0125,\n                        change_24h_pct: 2.66,\n                        volume_24h: 425000000,\n                        updated_at: new Date().toISOString()\n                    }\n                ]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchMarketData();\n        // 每30秒更新一次市场数据\n        const interval = setInterval(fetchMarketData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"市场概览\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"主要交易对价格动态\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 bg-muted rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-muted rounded w-16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted rounded w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            \"市场概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"主要交易对价格动态\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: marketData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"暂无市场数据\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : marketData.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex h-8 w-8 items-center justify-center rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-primary\",\n                                                    children: market.symbol.replace(\"USDT\", \"\").slice(0, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: market.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            \"Vol: \",\n                                                            (market.volume_24h / 1000000000).toFixed(2),\n                                                            \"B\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    \"$\",\n                                                    market.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    market.change_24h_pct >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-3 w-3 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getProfitColor)(market.change_24h_pct)),\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(market.change_24h_pct)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, market.symbol, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    marketData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-4 border-t\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"最后更新\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: new Date().toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketOverview, \"b1D0uo0O2eyao4YnmG4rzY2SbV8=\");\n_c = MarketOverview;\nvar _c;\n$RefreshReg$(_c, \"MarketOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/market-overview.tsx\n"));

/***/ })

});