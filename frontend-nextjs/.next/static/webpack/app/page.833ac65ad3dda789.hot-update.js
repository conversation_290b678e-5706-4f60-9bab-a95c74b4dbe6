"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/notification-banner */ \"(app-pages-browser)/./src/components/ui/notification-banner.tsx\");\n/* harmony import */ var _components_dashboard_feature_cards__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/feature-cards */ \"(app-pages-browser)/./src/components/dashboard/feature-cards.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: \"Alpha Tool\",\n        subtitle: \"专为「币安 Alpha 空投」而生\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__.NotificationBanner, {\n                            type: \"info\",\n                            message: \"SOPH 正在流发空投中，速速领取！\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__.NotificationBanner, {\n                            type: \"warning\",\n                            message: \"BSC 链暂受攻击交易暂停，建议优先阅 BSC 链上的 Alpha 代币！\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification_banner__WEBPACK_IMPORTED_MODULE_3__.NotificationBanner, {\n                            type: \"success\",\n                            message: \"通过 专属链接 注册币安，终身享受 20% 交易返佣！\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_feature_cards__WEBPACK_IMPORTED_MODULE_4__.FeatureCards, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/app/page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});