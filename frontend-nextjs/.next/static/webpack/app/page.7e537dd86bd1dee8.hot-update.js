"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/feature-cards.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/feature-cards.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeatureCards: function() { return /* binding */ FeatureCards; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,History,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,History,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,History,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,History,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,History,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ FeatureCards auto */ \n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"最佳实践\",\n        description: \"参与币安 Alpha 空投的最佳实践，包含视频教程与文字教程\",\n        href: \"/strategies\",\n        iconBg: \"bg-blue-100\",\n        iconColor: \"text-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"积分计算器\",\n        description: \"根据当前余额与目标积分，快速计算每日所需交易量。\",\n        href: \"/positions\",\n        iconBg: \"bg-indigo-100\",\n        iconColor: \"text-indigo-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"空投历史\",\n        description: \"查看历史空投 Alpha 空投数据，包含领取门槛、单号收益、历史曲线等。\",\n        href: \"/history\",\n        iconBg: \"bg-purple-100\",\n        iconColor: \"text-purple-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"规则详解\",\n        description: \"详细解读币安 Alpha 积分与空投规则，帮助你快速入门。\",\n        href: \"/rules\",\n        iconBg: \"bg-orange-100\",\n        iconColor: \"text-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_BookOpen_History_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"关于作者\",\n        description: \"Nico 投资有道，欢迎关注订阅。\",\n        href: \"/about\",\n        iconBg: \"bg-sky-100\",\n        iconColor: \"text-sky-500\"\n    }\n];\nfunction FeatureCards() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                href: feature.href,\n                className: \"flex flex-col p-6 bg-white rounded-xl border border-gray-100 hover:shadow-md transition-all\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-12 w-12 items-center justify-center rounded-full \".concat(feature.iconBg),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                    className: \"h-6 w-6 \".concat(feature.iconColor)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/feature-cards.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/feature-cards.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium\",\n                                children: feature.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/feature-cards.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/feature-cards.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 leading-relaxed\",\n                        children: feature.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/feature-cards.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/feature-cards.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/feature-cards.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_c = FeatureCards;\nvar _c;\n$RefreshReg$(_c, \"FeatureCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/feature-cards.tsx\n"));

/***/ })

});