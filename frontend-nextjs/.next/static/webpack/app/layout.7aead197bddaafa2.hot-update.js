/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/auth-provider.tsx */ \"(app-pages-browser)/./src/components/providers/auth-provider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(app-pages-browser)/./src/components/providers/theme-provider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Fauth-provider.tsx&modules=%2FUsers%2Fheningyu%2Fsofeware%2Flazy%2Ffrontend-nextjs%2Fsrc%2Fcomponents%2Fproviders%2Ftheme-provider.tsx&server=false!\n"));

/***/ })

});