"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/recent-trades.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/recent-trades.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentTrades: function() { return /* binding */ RecentTrades; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentTrades auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction RecentTrades() {\n    _s();\n    const [trades, setTrades] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchRecentTrades = async ()=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradeApi.getAll({\n                    page: 1,\n                    size: 10\n                });\n                setTrades(response.items || []);\n            } catch (error) {\n                console.error(\"获取交易记录失败:\", error);\n                // 使用模拟数据作为后备\n                setTrades([\n                    {\n                        id: 1,\n                        strategy_id: 1,\n                        symbol: \"BTCUSDT\",\n                        side: \"buy\",\n                        amount: 0.001,\n                        price: 43250.50,\n                        fee: 0.43,\n                        profit: 12.50,\n                        created_at: new Date().toISOString()\n                    },\n                    {\n                        id: 2,\n                        strategy_id: 2,\n                        symbol: \"ETHUSDT\",\n                        side: \"sell\",\n                        amount: 0.05,\n                        price: 2650.75,\n                        fee: 0.13,\n                        profit: -5.25,\n                        created_at: new Date(Date.now() - 3600000).toISOString()\n                    },\n                    {\n                        id: 3,\n                        strategy_id: 1,\n                        symbol: \"BTCUSDT\",\n                        side: \"sell\",\n                        amount: 0.001,\n                        price: 43380.25,\n                        fee: 0.43,\n                        profit: 25.75,\n                        created_at: new Date(Date.now() - 7200000).toISOString()\n                    }\n                ]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchRecentTrades();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"最近交易\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"最新的交易记录\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 bg-muted rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-muted rounded w-16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted rounded w-16 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted rounded w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        children: \"最近交易\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"最新的交易记录\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: trades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"暂无交易记录\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this) : trades.map((trade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-8 w-8 items-center justify-center rounded-full \".concat(trade.side === \"buy\" ? \"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400\" : \"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400\"),\n                                            children: trade.side === \"buy\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: trade.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: trade.side === \"buy\" ? \"买入\" : \"卖出\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        trade.amount,\n                                                        \" @ \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(trade.price)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        trade.profit !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getProfitColor)(trade.profit)),\n                                            children: [\n                                                trade.profit >= 0 ? \"+\" : \"\",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(trade.profit)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(trade.created_at)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, trade.id, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentTrades, \"nCh02x7Kz4ZS07yZSrvwyv3b46I=\");\n_c = RecentTrades;\nvar _c;\n$RefreshReg$(_c, \"RecentTrades\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/recent-trades.tsx\n"));

/***/ })

});