"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/recent-trades.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/recent-trades.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentTrades: function() { return /* binding */ RecentTrades; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownRight,ArrowUpRight,Clock!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-right.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentTrades auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction RecentTrades() {\n    _s();\n    const [trades, setTrades] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchRecentTrades = async ()=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradeApi.getAll({\n                    page: 1,\n                    size: 10\n                });\n                setTrades(response.items || []);\n            } catch (error) {\n                console.error(\"获取交易记录失败:\", error);\n                // 使用模拟数据作为后备\n                setTrades([\n                    {\n                        id: 1,\n                        strategy_id: 1,\n                        symbol: \"BTCUSDT\",\n                        side: \"buy\",\n                        amount: 0.001,\n                        price: 43250.50,\n                        fee: 0.43,\n                        profit: 12.50,\n                        created_at: new Date().toISOString()\n                    },\n                    {\n                        id: 2,\n                        strategy_id: 2,\n                        symbol: \"ETHUSDT\",\n                        side: \"sell\",\n                        amount: 0.05,\n                        price: 2650.75,\n                        fee: 0.13,\n                        profit: -5.25,\n                        created_at: new Date(Date.now() - 3600000).toISOString()\n                    },\n                    {\n                        id: 3,\n                        strategy_id: 1,\n                        symbol: \"BTCUSDT\",\n                        side: \"sell\",\n                        amount: 0.001,\n                        price: 43380.25,\n                        fee: 0.43,\n                        profit: 25.75,\n                        created_at: new Date(Date.now() - 7200000).toISOString()\n                    }\n                ]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchRecentTrades();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"最近交易\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"最新的交易记录\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(5)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 bg-muted rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-muted rounded w-16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted rounded w-16 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted rounded w-12\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        children: \"最近交易\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"最新的交易记录\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: !trades || trades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"暂无交易记录\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this) : trades.map((trade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex h-8 w-8 items-center justify-center rounded-full \".concat(trade.side === \"buy\" ? \"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400\" : \"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400\"),\n                                            children: trade.side === \"buy\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownRight_ArrowUpRight_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: trade.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: trade.side === \"buy\" ? \"买入\" : \"卖出\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        trade.amount,\n                                                        \" @ \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(trade.price)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        trade.profit !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getProfitColor)(trade.profit)),\n                                            children: [\n                                                trade.profit >= 0 ? \"+\" : \"\",\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(trade.profit)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatDate)(trade.created_at)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, trade.id, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/recent-trades.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentTrades, \"nCh02x7Kz4ZS07yZSrvwyv3b46I=\");\n_c = RecentTrades;\nvar _c;\n$RefreshReg$(_c, \"RecentTrades\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/recent-trades.tsx\n"));

/***/ })

});