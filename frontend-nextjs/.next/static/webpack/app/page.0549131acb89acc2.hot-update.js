"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/dashboard-stats.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/dashboard-stats.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardStats: function() { return /* binding */ DashboardStats; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DashboardStats auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardStats() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchStats = async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.dashboardApi.getStats();\n                setStats(data);\n            } catch (error) {\n                console.error(\"获取统计数据失败:\", error);\n                // 使用模拟数据作为后备\n                setStats({\n                    total_strategies: 8,\n                    active_strategies: 5,\n                    total_profit: 2450.75,\n                    total_trades: 156,\n                    win_rate: 73.2,\n                    best_strategy: {\n                        name: \"BTC网格策略\",\n                        profit: 1250.50\n                    },\n                    recent_trades: []\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchStats();\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n            children: [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            className: \"pb-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-muted rounded w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-muted rounded w-1/2 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-muted rounded w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, i, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this);\n    }\n    if (!stats) return null;\n    const profitColor = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getProfitColor)(stats.total_profit);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"总策略数\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: stats.total_strategies\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"活跃策略: \",\n                                    stats.active_strategies\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"活跃策略\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600\",\n                                children: stats.active_strategies\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"运行中的策略数量\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"总盈亏\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            stats.total_profit >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold \".concat(profitColor),\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(stats.total_profit)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"累计盈亏金额\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"总交易数\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: stats.total_trades\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"累计交易次数\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"胜率\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 text-muted-foreground\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold\",\n                                children: [\n                                    stats.win_rate.toFixed(1),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"盈利交易占比\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium\",\n                                children: \"最佳策略\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                variant: \"secondary\",\n                                className: \"text-xs\",\n                                children: \"TOP\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium truncate mb-1\",\n                                children: stats.best_strategy.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.getProfitColor)(stats.best_strategy.profit)),\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.formatCurrency)(stats.best_strategy.profit)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/dashboard-stats.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardStats, \"iGwa09lfId9dxY34GEVc7tv5csM=\");\n_c = DashboardStats;\nvar _c;\n$RefreshReg$(_c, \"DashboardStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/dashboard-stats.tsx\n"));

/***/ })

});