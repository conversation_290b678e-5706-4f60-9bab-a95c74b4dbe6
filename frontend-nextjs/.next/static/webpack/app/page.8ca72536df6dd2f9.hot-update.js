"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: function() { return /* binding */ cn; },\n/* harmony export */   debounce: function() { return /* binding */ debounce; },\n/* harmony export */   formatCurrency: function() { return /* binding */ formatCurrency; },\n/* harmony export */   formatDate: function() { return /* binding */ formatDate; },\n/* harmony export */   formatNumber: function() { return /* binding */ formatNumber; },\n/* harmony export */   formatPercentage: function() { return /* binding */ formatPercentage; },\n/* harmony export */   getProfitColor: function() { return /* binding */ getProfitColor; },\n/* harmony export */   getStatusColor: function() { return /* binding */ getStatusColor; },\n/* harmony export */   throttle: function() { return /* binding */ throttle; }\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount) {\n    let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"USDT\", decimals = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 2;\n    return \"\".concat(amount.toFixed(decimals), \" \").concat(currency);\n}\nfunction formatPercentage(value) {\n    let decimals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2;\n    const sign = value >= 0 ? \"+\" : \"\";\n    return \"\".concat(sign).concat(value.toFixed(decimals), \"%\");\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"zh-CN\", {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction formatNumber(num) {\n    let decimals = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2;\n    if (num >= 1e9) {\n        return (num / 1e9).toFixed(decimals) + \"B\";\n    }\n    if (num >= 1e6) {\n        return (num / 1e6).toFixed(decimals) + \"M\";\n    }\n    if (num >= 1e3) {\n        return (num / 1e3).toFixed(decimals) + \"K\";\n    }\n    return num.toFixed(decimals);\n}\nfunction getStatusColor(status) {\n    switch(status.toLowerCase()){\n        case \"running\":\n        case \"active\":\n            return \"text-success\";\n        case \"stopped\":\n        case \"inactive\":\n            return \"text-muted-foreground\";\n        case \"error\":\n        case \"failed\":\n            return \"text-destructive\";\n        case \"paused\":\n            return \"text-warning\";\n        default:\n            return \"text-muted-foreground\";\n    }\n}\nfunction getProfitColor(profit) {\n    if (profit > 0) return \"text-success\";\n    if (profit < 0) return \"text-destructive\";\n    return \"text-muted-foreground\";\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});