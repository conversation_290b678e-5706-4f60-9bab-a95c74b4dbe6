"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/strategies/page",{

/***/ "(app-pages-browser)/./src/components/strategies/strategies-content.tsx":
/*!**********************************************************!*\
  !*** ./src/components/strategies/strategies-content.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StrategiesContent: function() { return /* binding */ StrategiesContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Filter,MoreHorizontal,Play,Plus,Search,Square,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _strategy_creator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./strategy-creator */ \"(app-pages-browser)/./src/components/strategies/strategy-creator.tsx\");\n/* harmony import */ var _store_strategy__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/store/strategy */ \"(app-pages-browser)/./src/store/strategy.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ StrategiesContent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst strategyTypeMap = {\n    grid: {\n        label: \"网格策略\",\n        color: \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\"\n    },\n    martingale: {\n        label: \"马丁格尔\",\n        color: \"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200\"\n    },\n    dca: {\n        label: \"定投策略\",\n        color: \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\"\n    },\n    ma: {\n        label: \"均线策略\",\n        color: \"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200\"\n    }\n};\nconst statusMap = {\n    running: {\n        label: \"运行中\",\n        color: \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\"\n    },\n    stopped: {\n        label: \"已停止\",\n        color: \"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200\"\n    },\n    paused: {\n        label: \"已暂停\",\n        color: \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\"\n    },\n    error: {\n        label: \"错误\",\n        color: \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\"\n    }\n};\nfunction StrategiesContent() {\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { strategies, isLoading, fetchStrategies, deleteStrategy, startStrategy, stopStrategy, setFilters } = (0,_store_strategy__WEBPACK_IMPORTED_MODULE_8__.useStrategyStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStrategies().catch(_lib_api__WEBPACK_IMPORTED_MODULE_9__.showApiError);\n    }, [\n        fetchStrategies\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setFilters({\n            search: searchTerm\n        });\n    }, [\n        searchTerm,\n        setFilters\n    ]);\n    const handleDeleteStrategy = async (id)=>{\n        if (confirm(\"确定要删除这个策略吗？\")) {\n            try {\n                await deleteStrategy(id);\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_9__.showApiSuccess)(\"策略删除成功\");\n            } catch (error) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_9__.showApiError)(error);\n            }\n        }\n    };\n    const handleToggleStrategy = async (id, isRunning)=>{\n        try {\n            if (isRunning) {\n                await stopStrategy(id);\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_9__.showApiSuccess)(\"策略已停止\");\n            } else {\n                await startStrategy(id);\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_9__.showApiSuccess)(\"策略已启动\");\n            }\n        } catch (error) {\n            (0,_lib_api__WEBPACK_IMPORTED_MODULE_9__.showApiError)(error);\n        }\n    };\n    const filteredStrategies = (strategies || []).filter((strategy)=>strategy.name.toLowerCase().includes(searchTerm.toLowerCase()) || strategy.symbol.toLowerCase().includes(searchTerm.toLowerCase()));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4 justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex-1 max-w-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        placeholder: \"搜索策略名称或交易对...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pl-9\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            \"创建策略\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: filteredStrategies.map((strategy)=>{\n                    var _strategyTypeMap_strategy_strategy_type, _strategyTypeMap_strategy_strategy_type1, _statusMap_strategy_status, _statusMap_strategy_status1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"group hover:shadow-lg transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                className: \"pb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: strategy.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: ((_strategyTypeMap_strategy_strategy_type = strategyTypeMap[strategy.strategy_type]) === null || _strategyTypeMap_strategy_strategy_type === void 0 ? void 0 : _strategyTypeMap_strategy_strategy_type.color) || \"bg-gray-100 text-gray-800\",\n                                                                children: ((_strategyTypeMap_strategy_strategy_type1 = strategyTypeMap[strategy.strategy_type]) === null || _strategyTypeMap_strategy_strategy_type1 === void 0 ? void 0 : _strategyTypeMap_strategy_strategy_type1.label) || strategy.strategy_type\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: ((_statusMap_strategy_status = statusMap[strategy.status]) === null || _statusMap_strategy_status === void 0 ? void 0 : _statusMap_strategy_status.color) || \"bg-gray-100 text-gray-800\",\n                                                                children: ((_statusMap_strategy_status1 = statusMap[strategy.status]) === null || _statusMap_strategy_status1 === void 0 ? void 0 : _statusMap_strategy_status1.label) || strategy.status\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            className: \"opacity-0 hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuContent, {\n                                                        align: \"end\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                        lineNumber: 137,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"编辑策略\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                                onClick: ()=>handleToggleStrategy(strategy.id, strategy.status === \"running\"),\n                                                                children: strategy.status === \"running\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                            lineNumber: 143,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"停止策略\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                            lineNumber: 148,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"启动策略\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_6__.DropdownMenuItem, {\n                                                                className: \"text-destructive\",\n                                                                onClick: ()=>handleDeleteStrategy(strategy.id),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"删除策略\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: [\n                                            strategy.exchange,\n                                            \" • \",\n                                            strategy.symbol\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"总盈亏\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.getProfitColor)(strategy.total_profit - strategy.total_loss)),\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.formatCurrency)(strategy.total_profit - strategy.total_loss)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.getProfitColor)(strategy.total_profit - strategy.total_loss)),\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_10__.formatPercentage)((strategy.total_profit - strategy.total_loss) / strategy.base_amount * 100)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4 pt-4 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: strategy.total_orders\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"订单数\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: strategy.total_trades\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"成交数\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: [\n                                                            strategy.win_rate.toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"胜率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 pt-2\",\n                                        children: [\n                                            strategy.status === \"running\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"flex-1\",\n                                                onClick: ()=>handleToggleStrategy(strategy.id, true),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"停止\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                className: \"flex-1\",\n                                                onClick: ()=>handleToggleStrategy(strategy.id, false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"启动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"详情\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, strategy.id, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            filteredStrategies.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"p-12 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto h-12 w-12 rounded-full bg-muted flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-6 w-6 text-muted-foreground\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium mb-2\",\n                        children: \"暂无策略\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mb-4\",\n                        children: searchTerm ? \"没有找到符合条件的策略\" : \"创建您的第一个交易策略\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    !searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Filter_MoreHorizontal_Play_Plus_Search_Square_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this),\n                            \"创建策略\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this),\n            showCreateDialog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_strategy_creator__WEBPACK_IMPORTED_MODULE_7__.StrategyCreator, {\n                onClose: ()=>setShowCreateDialog(false),\n                onSuccess: ()=>{\n                    setShowCreateDialog(false);\n                    fetchStrategies();\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/strategies/strategies-content.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(StrategiesContent, \"rRbYYtzr3H/r2kkk4LFvhMqrImQ=\", false, function() {\n    return [\n        _store_strategy__WEBPACK_IMPORTED_MODULE_8__.useStrategyStore\n    ];\n});\n_c = StrategiesContent;\nvar _c;\n$RefreshReg$(_c, \"StrategiesContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/strategies/strategies-content.tsx\n"));

/***/ })

});