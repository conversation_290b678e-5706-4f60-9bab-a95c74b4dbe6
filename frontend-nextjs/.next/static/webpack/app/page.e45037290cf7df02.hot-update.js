"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/market-overview.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/market-overview.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverview: function() { return /* binding */ MarketOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketOverview auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MarketOverview() {\n    _s();\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchMarketData = async ()=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_3__.dashboardApi.getMarketData([\n                    \"BTCUSDT\",\n                    \"ETHUSDT\",\n                    \"BNBUSDT\",\n                    \"ADAUSDT\"\n                ]);\n                setMarketData(data || []);\n            } catch (error) {\n                console.error(\"获取市场数据失败:\", error);\n                // 使用模拟数据作为后备\n                setMarketData([\n                    {\n                        symbol: \"BTCUSDT\",\n                        price: 43250.50,\n                        change_24h: 1250.75,\n                        change_24h_pct: 2.98,\n                        volume_24h: 28450000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"ETHUSDT\",\n                        price: 2650.75,\n                        change_24h: -85.25,\n                        change_24h_pct: -3.11,\n                        volume_24h: 15230000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"BNBUSDT\",\n                        price: 315.80,\n                        change_24h: 12.45,\n                        change_24h_pct: 4.10,\n                        volume_24h: 1850000000,\n                        updated_at: new Date().toISOString()\n                    },\n                    {\n                        symbol: \"ADAUSDT\",\n                        price: 0.4825,\n                        change_24h: 0.0125,\n                        change_24h_pct: 2.66,\n                        volume_24h: 425000000,\n                        updated_at: new Date().toISOString()\n                    }\n                ]);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchMarketData();\n        // 每30秒更新一次市场数据\n        const interval = setInterval(fetchMarketData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"市场概览\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"主要交易对价格动态\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            ...Array(4)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between animate-pulse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 w-8 bg-muted rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-muted rounded w-16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted rounded w-20 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted rounded w-16\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            \"市场概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                        children: \"主要交易对价格动态\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: !marketData || marketData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"暂无市场数据\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : marketData.map((market)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex h-8 w-8 items-center justify-center rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-bold text-primary\",\n                                                    children: market.symbol.replace(\"USDT\", \"\").slice(0, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: market.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            \"Vol: \",\n                                                            (market.volume_24h / 1000000000).toFixed(2),\n                                                            \"B\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    \"$\",\n                                                    market.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    market.change_24h_pct >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-3 w-3 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getProfitColor)(market.change_24h_pct)),\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(market.change_24h_pct)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, market.symbol, true, {\n                                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    marketData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-4 border-t\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"最后更新\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: new Date().toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/sofeware/lazy/frontend-nextjs/src/components/dashboard/market-overview.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketOverview, \"b1D0uo0O2eyao4YnmG4rzY2SbV8=\");\n_c = MarketOverview;\nvar _c;\n$RefreshReg$(_c, \"MarketOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/market-overview.tsx\n"));

/***/ })

});