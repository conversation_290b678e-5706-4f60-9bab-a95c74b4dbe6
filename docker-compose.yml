version: '3.8'

services:
  # 后端API服务
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: trading-api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/trading.db
      - SECRET_KEY=${SECRET_KEY}
      - MASTER_PASSWORD=${MASTER_PASSWORD}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - trading-network

  # 前端Web服务
  web:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: trading-web
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - trading-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - trading-network
    command: redis-server --appendonly yes

  # 策略调度服务
  scheduler:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: trading-scheduler
    environment:
      - DATABASE_URL=sqlite:///./data/trading.db
      - SECRET_KEY=${SECRET_KEY}
      - MASTER_PASSWORD=${MASTER_PASSWORD}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - api
    restart: unless-stopped
    networks:
      - trading-network
    command: python -m app.scheduler

  # Nginx反向代理 (生产环境)
  nginx:
    image: nginx:alpine
    container_name: trading-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web
      - api
    restart: unless-stopped
    networks:
      - trading-network
    profiles:
      - production

volumes:
  redis_data:
  trading_data:

networks:
  trading-network:
    driver: bridge
