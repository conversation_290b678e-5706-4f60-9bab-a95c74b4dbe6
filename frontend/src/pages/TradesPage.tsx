import React, { useState, useEffect } from 'react'
import {
  FunnelIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  ArrowPathIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'
import { tradeApi, apiKeyApi } from '@/services/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import { cn } from '@/utils/cn'
import toast from 'react-hot-toast'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts'

interface Trade {
  id: number
  exchange: string
  exchange_trade_id: string | null
  symbol: string
  side: string
  amount: number
  price: number
  fee: number
  fee_currency: string | null
  pnl: number | null
  pnl_percentage: number | null
  executed_at: string
  api_key_id: number
  strategy_id: number | null
  notes: string | null
}

interface APIKey {
  id: number
  name: string
  exchange: string
}

interface TradeFilters {
  exchange: string
  symbol: string
  side: string
  market_type: string
  api_key_id: string
  date_from: string
  date_to: string
}

interface TradeStats {
  total_trades: number
  total_volume: number
  total_profit: number
  total_loss: number
  net_profit: number
  win_rate: number
  avg_profit: number
  avg_loss: number
  profit_factor: number
  max_drawdown: number
}

interface DailyStats {
  date: string
  trades: number
  volume: number
  profit: number
}

interface SymbolStats {
  symbol: string
  trades: number
  volume: number
  profit: number
  win_rate: number
}

const TradesPage: React.FC = () => {
  const [trades, setTrades] = useState<Trade[]>([])
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [stats, setStats] = useState<TradeStats | null>(null)
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([])
  const [symbolStats, setSymbolStats] = useState<SymbolStats[]>([])
  const [loading, setLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'trades' | 'stats'>('trades')
  const [filters, setFilters] = useState<TradeFilters>({
    exchange: '',
    symbol: '',
    side: '',
    market_type: '',
    api_key_id: '',
    date_from: '',
    date_to: ''
  })

  useEffect(() => {
    loadData()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [tradesData, apiKeysData, statsData, dailyData, symbolData] = await Promise.all([
        tradeApi.getTrades(filters),
        apiKeyApi.getApiKeys(),
        tradeApi.getTradeStats(30),
        tradeApi.getDailyStats(30),
        tradeApi.getSymbolStats(30)
      ])
      setTrades(tradesData)
      setApiKeys(apiKeysData)
      setStats(statsData)
      setDailyStats(dailyData)
      setSymbolStats(symbolData)
    } catch (error) {
      console.error('加载交易数据失败:', error)
      toast.error('加载交易数据失败')
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: keyof TradeFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      exchange: '',
      symbol: '',
      side: '',
      market_type: '',
      api_key_id: '',
      date_from: '',
      date_to: ''
    })
    setSearchTerm('')
  }

  const getSideColor = (side: string) => {
    return side === 'buy'
      ? 'text-green-600 dark:text-green-400'
      : 'text-red-600 dark:text-red-400'
  }

  const getPnlColor = (pnl: number | null) => {
    if (!pnl) return 'text-gray-500'
    return pnl > 0
      ? 'text-green-600 dark:text-green-400'
      : 'text-red-600 dark:text-red-400'
  }

  const filteredTrades = trades.filter(trade => {
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      return (
        trade.symbol.toLowerCase().includes(term) ||
        trade.exchange.toLowerCase().includes(term) ||
        (trade.exchange_trade_id && trade.exchange_trade_id.toLowerCase().includes(term))
      )
    }
    return true
  })

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            交易记录
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            查看您的交易历史和统计分析
          </p>
        </div>
        <button
          onClick={loadData}
          disabled={loading}
          className="btn btn-secondary"
        >
          <ArrowPathIcon className="w-4 h-4 mr-2" />
          刷新
        </button>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('trades')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'trades'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            )}
          >
            <CurrencyDollarIcon className="w-5 h-5 inline mr-2" />
            交易记录
          </button>
          <button
            onClick={() => setActiveTab('stats')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'stats'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            )}
          >
            <ChartBarIcon className="w-5 h-5 inline mr-2" />
            统计分析
          </button>
        </nav>
      </div>

      {/* 交易记录标签页 */}
      {activeTab === 'trades' && (
        <div className="space-y-6">
          {/* 搜索和筛选 */}
          <div className="card p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* 搜索框 */}
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索交易对、交易所或交易ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input pl-10"
                  />
                </div>
              </div>

              {/* 筛选按钮 */}
              <div className="flex gap-2">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={cn(
                    'btn',
                    showFilters ? 'btn-primary' : 'btn-secondary'
                  )}
                >
                  <FunnelIcon className="w-4 h-4 mr-2" />
                  筛选
                </button>
                {Object.values(filters).some(v => v) && (
                  <button
                    onClick={clearFilters}
                    className="btn btn-secondary"
                  >
                    <XMarkIcon className="w-4 h-4 mr-2" />
                    清除
                  </button>
                )}
              </div>
            </div>

            {/* 筛选面板 */}
            {showFilters && (
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      交易所
                    </label>
                    <select
                      value={filters.exchange}
                      onChange={(e) => handleFilterChange('exchange', e.target.value)}
                      className="input"
                    >
                      <option value="">全部交易所</option>
                      <option value="binance">Binance</option>
                      <option value="okx">OKX</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      交易方向
                    </label>
                    <select
                      value={filters.side}
                      onChange={(e) => handleFilterChange('side', e.target.value)}
                      className="input"
                    >
                      <option value="">全部方向</option>
                      <option value="buy">买入</option>
                      <option value="sell">卖出</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      市场类型
                    </label>
                    <select
                      value={filters.market_type}
                      onChange={(e) => handleFilterChange('market_type', e.target.value)}
                      className="input"
                    >
                      <option value="">全部类型</option>
                      <option value="spot">现货</option>
                      <option value="futures">合约</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      账户
                    </label>
                    <select
                      value={filters.api_key_id}
                      onChange={(e) => handleFilterChange('api_key_id', e.target.value)}
                      className="input"
                    >
                      <option value="">全部账户</option>
                      {apiKeys.map(apiKey => (
                        <option key={apiKey.id} value={apiKey.id.toString()}>
                          {apiKey.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      开始日期
                    </label>
                    <input
                      type="date"
                      value={filters.date_from}
                      onChange={(e) => handleFilterChange('date_from', e.target.value)}
                      className="input"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      结束日期
                    </label>
                    <input
                      type="date"
                      value={filters.date_to}
                      onChange={(e) => handleFilterChange('date_to', e.target.value)}
                      className="input"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 交易列表 */}
          <div className="card">
            {filteredTrades.length === 0 ? (
              <div className="text-center py-12">
                <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  暂无交易记录
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {searchTerm || Object.values(filters).some(v => v)
                    ? '没有找到符合条件的交易记录'
                    : '您还没有任何交易记录'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        交易对
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        方向
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        数量/价格
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        手续费
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        盈亏
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        时间
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredTrades.map((trade) => {
                      const apiKey = apiKeys.find(key => key.id === trade.api_key_id)

                      return (
                        <tr key={trade.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {trade.symbol}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {trade.exchange.toUpperCase()}
                              </div>
                              {apiKey && (
                                <div className="text-xs text-gray-400 dark:text-gray-500">
                                  {apiKey.name}
                                </div>
                              )}
                            </div>
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={cn('text-sm font-medium', getSideColor(trade.side))}>
                              {trade.side === 'buy' ? '买入' : '卖出'}
                            </span>
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm text-gray-900 dark:text-white">
                                {trade.amount.toFixed(8)}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                @ {trade.price.toFixed(2)}
                              </div>
                              <div className="text-xs text-gray-400 dark:text-gray-500">
                                ≈ ${(trade.amount * trade.price).toFixed(2)}
                              </div>
                            </div>
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {trade.fee.toFixed(8)}
                            </div>
                            {trade.fee_currency && (
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {trade.fee_currency}
                              </div>
                            )}
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap">
                            {trade.pnl !== null ? (
                              <div>
                                <div className={cn('text-sm font-medium', getPnlColor(trade.pnl))}>
                                  {trade.pnl > 0 ? '+' : ''}{trade.pnl.toFixed(2)} USDT
                                </div>
                                {trade.pnl_percentage !== null && (
                                  <div className={cn('text-sm', getPnlColor(trade.pnl))}>
                                    {trade.pnl_percentage > 0 ? '+' : ''}{trade.pnl_percentage.toFixed(2)}%
                                  </div>
                                )}
                              </div>
                            ) : (
                              <span className="text-sm text-gray-400 dark:text-gray-500">-</span>
                            )}
                          </td>

                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {new Date(trade.executed_at).toLocaleString()}
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 统计分析标签页 */}
      {activeTab === 'stats' && stats && (
        <div className="space-y-6">
          {/* 总体统计 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      总交易数
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {stats?.total_trades || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      总交易量
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      ${stats?.total_volume?.toFixed(2) || '0.00'}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ArrowUpIcon className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      净盈利
                    </dt>
                    <dd className={cn(
                      "text-lg font-medium",
                      (stats?.net_profit || 0) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                    )}>
                      {(stats?.net_profit || 0) >= 0 ? '+' : ''}{stats?.net_profit?.toFixed(2) || '0.00'} USDT
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      胜率
                    </dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-white">
                      {stats?.win_rate?.toFixed(1) || '0.0'}%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* 详细统计 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                盈亏分析
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">总盈利</span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    +{stats?.total_profit?.toFixed(2) || '0.00'} USDT
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">总亏损</span>
                  <span className="text-sm font-medium text-red-600 dark:text-red-400">
                    -{stats?.total_loss?.toFixed(2) || '0.00'} USDT
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">平均盈利</span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    +{stats?.avg_profit?.toFixed(2) || '0.00'} USDT
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">平均亏损</span>
                  <span className="text-sm font-medium text-red-600 dark:text-red-400">
                    -{stats?.avg_loss?.toFixed(2) || '0.00'} USDT
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">盈亏比</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {stats?.profit_factor?.toFixed(2) || '0.00'}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">最大回撤</span>
                  <span className="text-sm font-medium text-red-600 dark:text-red-400">
                    -{stats?.max_drawdown?.toFixed(2) || '0.00'}%
                  </span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                交易对分布
              </h3>
              {symbolStats.length > 0 ? (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={symbolStats}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ symbol, percent }) => `${symbol} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="trades"
                      >
                        {symbolStats.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  暂无数据
                </div>
              )}
            </div>
          </div>

          {/* 每日盈亏趋势 */}
          {dailyStats.length > 0 && (
            <div className="card p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                每日盈亏趋势 (近30天)
              </h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={dailyStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => new Date(value).toLocaleDateString()}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip
                      labelFormatter={(value) => new Date(value).toLocaleDateString()}
                      formatter={(value: number, name: string) => [
                        name === 'profit' ? `${value.toFixed(2)} USDT` : value,
                        name === 'profit' ? '盈亏' : name === 'trades' ? '交易数' : '交易量'
                      ]}
                    />
                    <Line
                      type="monotone"
                      dataKey="profit"
                      stroke="#8884d8"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          )}

          {/* 交易对统计表 */}
          {symbolStats.length > 0 && (
            <div className="card">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  交易对统计
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        交易对
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        交易数
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        交易量
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        盈亏
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        胜率
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {symbolStats.map((stat) => (
                      <tr key={stat.symbol} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          {stat.symbol}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {stat.trades}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          ${stat.volume.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <span className={cn(
                            stat.profit >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                          )}>
                            {stat.profit >= 0 ? '+' : ''}{stat.profit.toFixed(2)} USDT
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                          {stat.win_rate.toFixed(1)}%
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default TradesPage
