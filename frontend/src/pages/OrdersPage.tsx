import React, { useState, useEffect } from 'react'
import {
  FunnelIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { orderApi, api<PERSON>ey<PERSON>pi } from '@/services/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import { cn } from '@/utils/cn'
import toast from 'react-hot-toast'

interface Order {
  id: number
  exchange: string
  exchange_order_id: string | null
  symbol: string
  side: string
  order_type: string
  amount: number
  price: number | null
  filled_amount: number
  status: string
  market_type: string
  created_at: string
  updated_at: string
  executed_at: string | null
  cancelled_at: string | null
  api_key_id: number
  strategy_id: number | null
  notes: string | null
}

interface APIKey {
  id: number
  name: string
  exchange: string
}

interface OrderFilters {
  status: string
  exchange: string
  symbol: string
  side: string
  api_key_id: string
  date_from: string
  date_to: string
}

const OrdersPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([])
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [loading, setLoading] = useState(true)
  const [cancelling, setCancelling] = useState<{[key: number]: boolean}>({})
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<OrderFilters>({
    status: 'pending',
    exchange: '',
    symbol: '',
    side: '',
    api_key_id: '',
    date_from: '',
    date_to: ''
  })

  useEffect(() => {
    loadData()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [ordersData, apiKeysData] = await Promise.all([
        orderApi.getOrders(filters),
        apiKeyApi.getApiKeys()
      ])
      setOrders(ordersData)
      setApiKeys(apiKeysData)
    } catch (error) {
      console.error('加载订单数据失败:', error)
      toast.error('加载订单数据失败')
    } finally {
      setLoading(false)
    }
  }

  const handleCancelOrder = async (orderId: number) => {
    if (!confirm('确定要取消这个订单吗？')) return

    try {
      setCancelling(prev => ({ ...prev, [orderId]: true }))
      await orderApi.cancelOrder(orderId)
      toast.success('订单取消成功')
      loadData()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '取消订单失败')
    } finally {
      setCancelling(prev => ({ ...prev, [orderId]: false }))
    }
  }

  const handleFilterChange = (key: keyof OrderFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      status: 'pending',
      exchange: '',
      symbol: '',
      side: '',
      api_key_id: '',
      date_from: '',
      date_to: ''
    })
    setSearchTerm('')
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="w-4 h-4 text-yellow-500" />
      case 'filled':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />
      case 'cancelled':
        return <XCircleIcon className="w-4 h-4 text-red-500" />
      case 'partially_filled':
        return <ExclamationTriangleIcon className="w-4 h-4 text-orange-500" />
      default:
        return <ClockIcon className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: {[key: string]: string} = {
      'pending': '待成交',
      'filled': '已成交',
      'cancelled': '已取消',
      'partially_filled': '部分成交',
      'failed': '失败'
    }
    return statusMap[status] || status
  }

  const getSideColor = (side: string) => {
    return side === 'buy'
      ? 'text-green-600 dark:text-green-400'
      : 'text-red-600 dark:text-red-400'
  }

  const filteredOrders = orders.filter(order => {
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      return (
        order.symbol.toLowerCase().includes(term) ||
        order.exchange.toLowerCase().includes(term) ||
        (order.exchange_order_id && order.exchange_order_id.toLowerCase().includes(term))
      )
    }
    return true
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            订单记录
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            查看和管理您的订单，默认显示未成交订单
          </p>
        </div>
        <button
          onClick={loadData}
          disabled={loading}
          className="btn btn-secondary"
        >
          <ArrowPathIcon className="w-4 h-4 mr-2" />
          刷新
        </button>
      </div>

      {/* 搜索和筛选 */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜索框 */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索交易对、交易所或订单ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
          </div>

          {/* 筛选按钮 */}
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                'btn',
                showFilters ? 'btn-primary' : 'btn-secondary'
              )}
            >
              <FunnelIcon className="w-4 h-4 mr-2" />
              筛选
            </button>
            {(filters.exchange || filters.symbol || filters.side || filters.api_key_id || filters.date_from || filters.date_to) && (
              <button
                onClick={clearFilters}
                className="btn btn-secondary"
              >
                <XMarkIcon className="w-4 h-4 mr-2" />
                清除
              </button>
            )}
          </div>
        </div>

        {/* 筛选面板 */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  状态
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="input"
                >
                  <option value="">全部状态</option>
                  <option value="pending">待成交</option>
                  <option value="filled">已成交</option>
                  <option value="cancelled">已取消</option>
                  <option value="partially_filled">部分成交</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  交易所
                </label>
                <select
                  value={filters.exchange}
                  onChange={(e) => handleFilterChange('exchange', e.target.value)}
                  className="input"
                >
                  <option value="">全部交易所</option>
                  <option value="binance">Binance</option>
                  <option value="okx">OKX</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  交易方向
                </label>
                <select
                  value={filters.side}
                  onChange={(e) => handleFilterChange('side', e.target.value)}
                  className="input"
                >
                  <option value="">全部方向</option>
                  <option value="buy">买入</option>
                  <option value="sell">卖出</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  账户
                </label>
                <select
                  value={filters.api_key_id}
                  onChange={(e) => handleFilterChange('api_key_id', e.target.value)}
                  className="input"
                >
                  <option value="">全部账户</option>
                  {apiKeys.map(apiKey => (
                    <option key={apiKey.id} value={apiKey.id.toString()}>
                      {apiKey.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  开始日期
                </label>
                <input
                  type="date"
                  value={filters.date_from}
                  onChange={(e) => handleFilterChange('date_from', e.target.value)}
                  className="input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  结束日期
                </label>
                <input
                  type="date"
                  value={filters.date_to}
                  onChange={(e) => handleFilterChange('date_to', e.target.value)}
                  className="input"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 订单列表 */}
      <div className="card">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              暂无订单
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm || Object.values(filters).some(v => v)
                ? '没有找到符合条件的订单'
                : '您还没有任何订单'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    交易对
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    方向/类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    数量/价格
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    成交情况
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredOrders.map((order) => {
                  const apiKey = apiKeys.find(key => key.id === order.api_key_id)
                  const fillPercentage = order.amount > 0 ? (order.filled_amount / order.amount) * 100 : 0

                  return (
                    <tr key={order.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {order.symbol}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {order.exchange.toUpperCase()} • {order.market_type === 'spot' ? '现货' : '合约'}
                          </div>
                          {apiKey && (
                            <div className="text-xs text-gray-400 dark:text-gray-500">
                              {apiKey.name}
                            </div>
                          )}
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className={cn('text-sm font-medium', getSideColor(order.side))}>
                            {order.side === 'buy' ? '买入' : '卖出'}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {order.order_type === 'market' ? '市价单' : '限价单'}
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {order.amount.toFixed(8)}
                          </div>
                          {order.price && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              @ {order.price.toFixed(2)}
                            </div>
                          )}
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm text-gray-900 dark:text-white">
                            {order.filled_amount.toFixed(8)} / {order.amount.toFixed(8)}
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                            <div
                              className="bg-blue-600 h-1.5 rounded-full"
                              style={{ width: `${fillPercentage}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {fillPercentage.toFixed(1)}%
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(order.status)}
                          <span className="ml-2 text-sm text-gray-900 dark:text-white">
                            {getStatusText(order.status)}
                          </span>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        <div>
                          <div>创建: {new Date(order.created_at).toLocaleString()}</div>
                          {order.executed_at && (
                            <div>成交: {new Date(order.executed_at).toLocaleString()}</div>
                          )}
                          {order.cancelled_at && (
                            <div>取消: {new Date(order.cancelled_at).toLocaleString()}</div>
                          )}
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {order.status === 'pending' || order.status === 'partially_filled' ? (
                          <button
                            onClick={() => handleCancelOrder(order.id)}
                            disabled={cancelling[order.id]}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                          >
                            {cancelling[order.id] ? (
                              <div className="flex items-center">
                                <LoadingSpinner size="sm" className="mr-1" />
                                取消中...
                              </div>
                            ) : (
                              '取消订单'
                            )}
                          </button>
                        ) : (
                          <span className="text-gray-400 dark:text-gray-500">-</span>
                        )}
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 统计信息 */}
      {filteredOrders.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="card p-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">总订单数</div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {filteredOrders.length}
            </div>
          </div>

          <div className="card p-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">待成交</div>
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
              {filteredOrders.filter(o => o.status === 'pending').length}
            </div>
          </div>

          <div className="card p-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">已成交</div>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {filteredOrders.filter(o => o.status === 'filled').length}
            </div>
          </div>

          <div className="card p-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">已取消</div>
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">
              {filteredOrders.filter(o => o.status === 'cancelled').length}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default OrdersPage
