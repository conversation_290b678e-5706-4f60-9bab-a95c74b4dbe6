import React, { useEffect, useState } from 'react'
import { 
  CurrencyDollarIcon, 
  ChartBarIcon, 
  ShoppingCartIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'
import { userApi, tradeApi, orderApi, riskApi } from '@/services/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import { cn } from '@/utils/cn'

interface DashboardStats {
  totalOrders: number
  totalTrades: number
  totalStrategies: number
  totalApiKeys: number
  totalProfit: number
  totalLoss: number
}

interface RiskStatus {
  riskLevel: string
  criticalEvents: number
  eventsToday: number
  canTrade: boolean
}

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [riskStatus, setRiskStatus] = useState<RiskStatus | null>(null)
  const [activeOrders, setActiveOrders] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // 并行加载数据
      const [userStats, riskData, activeOrdersData] = await Promise.all([
        userApi.getStats(),
        riskApi.checkStatus(),
        orderApi.getActiveOrdersCount()
      ])

      setStats(userStats)
      setRiskStatus(riskData)
      setActiveOrders(activeOrdersData.active_orders_count)
    } catch (error) {
      console.error('加载仪表盘数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'text-success-600 bg-success-100 dark:bg-success-900 dark:text-success-200'
      case 'medium':
        return 'text-warning-600 bg-warning-100 dark:bg-warning-900 dark:text-warning-200'
      case 'high':
        return 'text-danger-600 bg-danger-100 dark:bg-danger-900 dark:text-danger-200'
      case 'critical':
        return 'text-danger-600 bg-danger-100 dark:bg-danger-900 dark:text-danger-200'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getRiskLevelText = (level: string) => {
    switch (level) {
      case 'low':
        return '低风险'
      case 'medium':
        return '中等风险'
      case 'high':
        return '高风险'
      case 'critical':
        return '严重风险'
      default:
        return '未知'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="加载仪表盘数据..." />
      </div>
    )
  }

  const netProfit = (stats?.totalProfit || 0) - (stats?.totalLoss || 0)
  const isProfit = netProfit >= 0

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          仪表盘
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          欢迎回来，查看您的交易概况
        </p>
      </div>

      {/* 风险状态警告 */}
      {riskStatus && riskStatus.riskLevel !== 'low' && (
        <div className="rounded-md bg-warning-50 dark:bg-warning-900 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-warning-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-warning-800 dark:text-warning-200">
                风险提醒
              </h3>
              <div className="mt-2 text-sm text-warning-700 dark:text-warning-300">
                <p>
                  当前风险等级：
                  <span className={cn('ml-1 px-2 py-1 rounded-full text-xs font-medium', getRiskLevelColor(riskStatus.riskLevel))}>
                    {getRiskLevelText(riskStatus.riskLevel)}
                  </span>
                </p>
                {riskStatus.criticalEvents > 0 && (
                  <p className="mt-1">
                    有 {riskStatus.criticalEvents} 个严重风控事件需要处理
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {/* 总盈亏 */}
        <div className="card p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className={cn(
                'flex items-center justify-center w-8 h-8 rounded-md',
                isProfit ? 'bg-success-100 dark:bg-success-900' : 'bg-danger-100 dark:bg-danger-900'
              )}>
                {isProfit ? (
                  <ArrowUpIcon className="w-5 h-5 text-success-600 dark:text-success-400" />
                ) : (
                  <ArrowDownIcon className="w-5 h-5 text-danger-600 dark:text-danger-400" />
                )}
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  总盈亏
                </dt>
                <dd className={cn(
                  'text-lg font-medium',
                  isProfit ? 'text-success-600 dark:text-success-400' : 'text-danger-600 dark:text-danger-400'
                )}>
                  {isProfit ? '+' : ''}{netProfit.toFixed(2)} USDT
                </dd>
              </dl>
            </div>
          </div>
        </div>

        {/* 总交易数 */}
        <div className="card p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-md">
                <ChartBarIcon className="w-5 h-5 text-primary-600 dark:text-primary-400" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  总交易数
                </dt>
                <dd className="text-lg font-medium text-gray-900 dark:text-white">
                  {stats?.totalTrades || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        {/* 活跃订单 */}
        <div className="card p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-8 h-8 bg-warning-100 dark:bg-warning-900 rounded-md">
                <ShoppingCartIcon className="w-5 h-5 text-warning-600 dark:text-warning-400" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  活跃订单
                </dt>
                <dd className="text-lg font-medium text-gray-900 dark:text-white">
                  {activeOrders}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        {/* 运行策略 */}
        <div className="card p-5">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-md">
                <CurrencyDollarIcon className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  总策略数
                </dt>
                <dd className="text-lg font-medium text-gray-900 dark:text-white">
                  {stats?.totalStrategies || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 交易统计 */}
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            交易统计
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">总订单数</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {stats?.totalOrders || 0}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">总交易数</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {stats?.totalTrades || 0}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">总盈利</span>
              <span className="text-sm font-medium text-success-600 dark:text-success-400">
                +{(stats?.totalProfit || 0).toFixed(2)} USDT
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">总亏损</span>
              <span className="text-sm font-medium text-danger-600 dark:text-danger-400">
                -{(stats?.totalLoss || 0).toFixed(2)} USDT
              </span>
            </div>
          </div>
        </div>

        {/* 风险状态 */}
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            风险状态
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">风险等级</span>
              <span className={cn('px-2 py-1 rounded-full text-xs font-medium', getRiskLevelColor(riskStatus?.riskLevel || 'low'))}>
                {getRiskLevelText(riskStatus?.riskLevel || 'low')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">严重事件</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {riskStatus?.criticalEvents || 0}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">今日事件</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {riskStatus?.eventsToday || 0}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">交易状态</span>
              <span className={cn(
                'px-2 py-1 rounded-full text-xs font-medium',
                riskStatus?.canTrade 
                  ? 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200'
                  : 'bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200'
              )}>
                {riskStatus?.canTrade ? '正常' : '受限'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="card p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          快速操作
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="btn btn-primary">
            创建策略
          </button>
          <button className="btn btn-secondary">
            手动交易
          </button>
          <button className="btn btn-secondary">
            查看订单
          </button>
          <button className="btn btn-secondary">
            系统设置
          </button>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
