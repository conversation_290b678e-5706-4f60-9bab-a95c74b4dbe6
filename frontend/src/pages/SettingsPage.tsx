import React, { useState, useEffect } from 'react'
import {
  PlusIcon,
  KeyIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ChatBubbleLeftRightIcon,
  BellIcon
} from '@heroicons/react/24/outline'
import { useForm } from 'react-hook-form'
import { apiKeyApi, telegramApi } from '@/services/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import { cn } from '@/utils/cn'
import toast from 'react-hot-toast'

interface APIKey {
  id: number
  name: string
  exchange: string
  is_testnet: boolean
  can_trade: boolean
  can_read: boolean
  is_active: boolean
  last_used: string | null
  created_at: string
}

interface TelegramSettings {
  id?: number
  chat_id: string | null
  username: string | null
  enabled: boolean
  notify_orders: boolean
  notify_trades: boolean
  notify_strategies: boolean
  notify_risks: boolean
  notify_errors: boolean
  max_notifications_per_hour: number
  quiet_hours_start: string | null
  quiet_hours_end: string | null
  is_verified: boolean
}

interface APIKeyForm {
  name: string
  exchange: string
  api_key: string
  secret_key: string
  passphrase?: string
  is_testnet: boolean
  can_trade: boolean
  can_read: boolean
}

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'api-keys' | 'telegram'>('api-keys')
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [telegramSettings, setTelegramSettings] = useState<TelegramSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingKey, setEditingKey] = useState<APIKey | null>(null)
  const [showSecrets, setShowSecrets] = useState<{[key: number]: boolean}>({})

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<APIKeyForm>()

  const selectedExchange = watch('exchange')

  useEffect(() => {
    // 调试：检查 API 函数是否正确导入
    console.log('apiKeyApi:', apiKeyApi)
    console.log('apiKeyApi.getApiKeys:', apiKeyApi.getApiKeys)
    console.log('telegramApi:', telegramApi)

    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      console.log('开始加载设置数据...')

      // 检查 API 函数是否存在
      if (typeof apiKeyApi.getApiKeys !== 'function') {
        throw new Error('apiKeyApi.getApiKeys is not a function')
      }

      console.log('调用 apiKeyApi.getApiKeys...')
      const apiKeysPromise = apiKeyApi.getApiKeys()

      console.log('调用 telegramApi.getSettings...')
      const telegramPromise = telegramApi.getSettings().catch((error) => {
        console.log('Telegram设置加载失败:', error)
        return null
      })

      const [apiKeysData, telegramData] = await Promise.all([
        apiKeysPromise,
        telegramPromise
      ])

      console.log('API密钥数据:', apiKeysData)
      console.log('Telegram数据:', telegramData)

      setApiKeys(apiKeysData || [])
      setTelegramSettings(telegramData)
    } catch (error: any) {
      console.error('加载设置数据失败:', error)
      toast.error(`加载设置数据失败: ${error.message || '未知错误'}`)
    } finally {
      setLoading(false)
    }
  }

  const onSubmitAPIKey = async (data: APIKeyForm) => {
    try {
      if (editingKey) {
        await apiKeyApi.updateApiKey(editingKey.id, data)
        toast.success('API密钥更新成功')
      } else {
        await apiKeyApi.createApiKey(data)
        toast.success('API密钥添加成功')
      }

      setShowAddForm(false)
      setEditingKey(null)
      reset()
      loadData()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '操作失败')
    }
  }

  const handleDeleteAPIKey = async (id: number) => {
    if (!confirm('确定要删除这个API密钥吗？')) return

    try {
      await apiKeyApi.deleteApiKey(id)
      toast.success('API密钥删除成功')
      loadData()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '删除失败')
    }
  }

  const handleTestConnection = async (id: number) => {
    try {
      const result = await apiKeyApi.testConnection(id)
      if (result.success) {
        toast.success('连接测试成功')
      } else {
        toast.error(`连接测试失败: ${result.error}`)
      }
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '测试失败')
    }
  }

  const toggleShowSecret = (keyId: number) => {
    setShowSecrets(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          系统设置
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          管理您的交易所账户和通知设置
        </p>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('api-keys')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'api-keys'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            )}
          >
            <KeyIcon className="w-5 h-5 inline mr-2" />
            API密钥管理
          </button>
          <button
            onClick={() => setActiveTab('telegram')}
            className={cn(
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'telegram'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            )}
          >
            <ChatBubbleLeftRightIcon className="w-5 h-5 inline mr-2" />
            Telegram通知
          </button>
        </nav>
      </div>

      {/* API密钥管理 */}
      {activeTab === 'api-keys' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              交易所API密钥
            </h2>
            <button
              onClick={() => {
                setShowAddForm(true)
                setEditingKey(null)
                reset()
              }}
              className="btn btn-primary"
            >
              <PlusIcon className="w-4 h-4 mr-2" />
              添加API密钥
            </button>
          </div>

          {/* API密钥列表 */}
          <div className="space-y-4">
            {apiKeys.map((apiKey) => (
              <div key={apiKey.id} className="card p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {apiKey.name}
                      </h3>
                      <span className={cn(
                        'px-2 py-1 text-xs font-medium rounded-full',
                        apiKey.exchange === 'binance'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      )}>
                        {apiKey.exchange.toUpperCase()}
                      </span>
                      {apiKey.is_testnet && (
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                          测试网
                        </span>
                      )}
                      {apiKey.is_active ? (
                        <CheckCircleIcon className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircleIcon className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                    <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-4">
                          <span>读取权限: {apiKey.can_read ? '✓' : '✗'}</span>
                          <span>交易权限: {apiKey.can_trade ? '✓' : '✗'}</span>
                          <span>状态: {apiKey.is_active ? '活跃' : '禁用'}</span>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span>创建时间: {apiKey.created_at ? new Date(apiKey.created_at).toLocaleString() : '未知'}</span>
                          <span>最后使用: {apiKey.last_used ? new Date(apiKey.last_used).toLocaleString() : '从未使用'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleTestConnection(apiKey.id)}
                      className="btn btn-secondary btn-sm"
                    >
                      测试连接
                    </button>
                    <button
                      onClick={() => {
                        setEditingKey(apiKey)
                        setShowAddForm(true)
                        reset({
                          name: apiKey.name,
                          exchange: apiKey.exchange,
                          is_testnet: apiKey.is_testnet,
                          can_trade: apiKey.can_trade,
                          can_read: apiKey.can_read
                        })
                      }}
                      className="btn btn-secondary btn-sm"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteAPIKey(apiKey.id)}
                      className="btn btn-danger btn-sm"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {apiKeys.length === 0 && (
              <div className="text-center py-12">
                <KeyIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  暂无API密钥
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  添加您的第一个交易所API密钥开始使用
                </p>
              </div>
            )}
          </div>

          {/* API密钥添加/编辑表单 */}
          {showAddForm && (
            <div className="card p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {editingKey ? '编辑API密钥' : '添加API密钥'}
              </h3>

              <form onSubmit={handleSubmit(onSubmitAPIKey)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      名称
                    </label>
                    <input
                      {...register('name', { required: '请输入名称' })}
                      type="text"
                      className="input"
                      placeholder="例如：币安主账户"
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      交易所
                    </label>
                    <select
                      {...register('exchange', { required: '请选择交易所' })}
                      className="input"
                    >
                      <option value="">选择交易所</option>
                      <option value="binance">Binance (币安)</option>
                      <option value="okx">OKX (欧易)</option>
                    </select>
                    {errors.exchange && (
                      <p className="mt-1 text-sm text-red-600">{errors.exchange.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    API Key
                  </label>
                  <input
                    {...register('api_key', { required: '请输入API Key' })}
                    type="text"
                    className="input"
                    placeholder="输入您的API Key"
                  />
                  {errors.api_key && (
                    <p className="mt-1 text-sm text-red-600">{errors.api_key.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Secret Key
                  </label>
                  <input
                    {...register('secret_key', { required: '请输入Secret Key' })}
                    type="password"
                    className="input"
                    placeholder="输入您的Secret Key"
                  />
                  {errors.secret_key && (
                    <p className="mt-1 text-sm text-red-600">{errors.secret_key.message}</p>
                  )}
                </div>

                {selectedExchange === 'okx' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Passphrase (OKX必需)
                    </label>
                    <input
                      {...register('passphrase', {
                        required: selectedExchange === 'okx' ? '请输入Passphrase' : false
                      })}
                      type="password"
                      className="input"
                      placeholder="输入您的Passphrase"
                    />
                    {errors.passphrase && (
                      <p className="mt-1 text-sm text-red-600">{errors.passphrase.message}</p>
                    )}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center">
                    <input
                      {...register('is_testnet')}
                      type="checkbox"
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900 dark:text-white">
                      测试网环境
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      {...register('can_read')}
                      type="checkbox"
                      defaultChecked
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900 dark:text-white">
                      读取权限
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      {...register('can_trade')}
                      type="checkbox"
                      defaultChecked
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="ml-2 block text-sm text-gray-900 dark:text-white">
                      交易权限
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddForm(false)
                      setEditingKey(null)
                      reset()
                    }}
                    className="btn btn-secondary"
                  >
                    取消
                  </button>
                  <button type="submit" className="btn btn-primary">
                    {editingKey ? '更新' : '添加'}
                  </button>
                </div>
              </form>
            </div>
          )}
        </div>
      )}

      {/* Telegram通知设置 */}
      {activeTab === 'telegram' && (
        <TelegramSettingsTab
          settings={telegramSettings}
          onUpdate={loadData}
        />
      )}
    </div>
  )
}

// Telegram设置组件
interface TelegramSettingsTabProps {
  settings: TelegramSettings | null
  onUpdate: () => void
}

const TelegramSettingsTab: React.FC<TelegramSettingsTabProps> = ({ settings, onUpdate }) => {
  const [loading, setLoading] = useState(false)
  const [botToken, setBotToken] = useState('')
  const [showTokenForm, setShowTokenForm] = useState(false)
  const [botTokenStatus, setBotTokenStatus] = useState<{configured: boolean, verified: boolean} | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<TelegramSettings>({
    defaultValues: settings || {
      enabled: false,
      notify_orders: true,
      notify_trades: true,
      notify_strategies: true,
      notify_risks: true,
      notify_errors: true,
      max_notifications_per_hour: 60,
      quiet_hours_start: null,
      quiet_hours_end: null
    }
  })

  useEffect(() => {
    if (settings) {
      reset(settings)
    }
  }, [settings, reset])

  // 加载Bot Token状态
  const loadBotTokenStatus = async () => {
    try {
      const status = await telegramApi.getBotTokenStatus()
      setBotTokenStatus(status)
    } catch (error) {
      console.error('加载Bot Token状态失败:', error)
      setBotTokenStatus({ configured: false, verified: false })
    }
  }

  useEffect(() => {
    loadBotTokenStatus()
  }, [])

  const onSubmitTelegram = async (data: TelegramSettings) => {
    try {
      setLoading(true)
      await telegramApi.updateSettings(data)
      toast.success('Telegram设置更新成功')
      onUpdate()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '更新失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSetBotToken = async () => {
    if (!botToken.trim()) {
      toast.error('请输入Bot Token')
      return
    }

    try {
      setLoading(true)
      await telegramApi.setBotToken(botToken)
      toast.success('Bot Token设置成功')
      setBotToken('')
      setShowTokenForm(false)
      // 重新加载Bot Token状态
      await loadBotTokenStatus()
      onUpdate()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '设置失败')
    } finally {
      setLoading(false)
    }
  }

  const handleTestNotification = async () => {
    try {
      setLoading(true)
      await telegramApi.testNotification()
      toast.success('测试通知发送成功')
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '发送失败')
    } finally {
      setLoading(false)
    }
  }

  const handleVerifyBotToken = async () => {
    try {
      setLoading(true)
      const result = await telegramApi.verifyBotToken()
      toast.success(`Bot验证成功: @${result.bot_info?.username}`)
      // 重新加载Bot Token状态
      await loadBotTokenStatus()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '验证失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">
          Telegram通知设置
        </h2>
        {settings?.is_verified && (
          <button
            onClick={handleTestNotification}
            disabled={loading}
            className="btn btn-secondary"
          >
            <BellIcon className="w-4 h-4 mr-2" />
            发送测试通知
          </button>
        )}
      </div>

      {/* Bot Token设置 */}
      <div className="card p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Bot Token配置
        </h3>

        {!showTokenForm ? (
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {botTokenStatus?.configured ? '✅ Bot Token已配置' : '❌ 尚未配置Bot Token'}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                需要先创建Telegram Bot并获取Token
              </p>
              {botTokenStatus?.configured && (
                <div className="space-y-1">
                  <p className="text-xs text-green-600 dark:text-green-400">
                    Token验证状态: {botTokenStatus.verified ? '已验证' : '未验证'}
                  </p>
                  {botTokenStatus.bot_username && (
                    <p className="text-xs text-blue-600 dark:text-blue-400">
                      Bot用户名: @{botTokenStatus.bot_username}
                    </p>
                  )}
                  {botTokenStatus.created_at && (
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      创建时间: {new Date(botTokenStatus.created_at).toLocaleString()}
                    </p>
                  )}
                </div>
              )}
            </div>
            <div className="flex space-x-2">
              {botTokenStatus?.configured && !botTokenStatus.verified && (
                <button
                  onClick={handleVerifyBotToken}
                  disabled={loading}
                  className="btn btn-secondary"
                >
                  验证Token
                </button>
              )}
              <button
                onClick={() => setShowTokenForm(true)}
                className="btn btn-primary"
              >
                {botTokenStatus?.configured ? '更新Token' : '设置Token'}
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Bot Token
              </label>
              <input
                type="password"
                value={botToken}
                onChange={(e) => setBotToken(e.target.value)}
                className="input"
                placeholder="输入您的Telegram Bot Token"
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                从 @BotFather 获取您的Bot Token
              </p>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowTokenForm(false)
                  setBotToken('')
                }}
                className="btn btn-secondary"
              >
                取消
              </button>
              <button
                onClick={handleSetBotToken}
                disabled={loading}
                className="btn btn-primary"
              >
                {loading ? '设置中...' : '设置Token'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 通知设置 */}
      {settings && (
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            通知设置
          </h3>

          <form onSubmit={handleSubmit(onSubmitTelegram)} className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  启用Telegram通知
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  开启后将通过Telegram发送交易通知
                </p>
              </div>
              <input
                {...register('enabled')}
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                通知类型
              </h4>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">订单通知</span>
                  <input
                    {...register('notify_orders')}
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">成交通知</span>
                  <input
                    {...register('notify_trades')}
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">策略通知</span>
                  <input
                    {...register('notify_strategies')}
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">风控通知</span>
                  <input
                    {...register('notify_risks')}
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">错误通知</span>
                  <input
                    {...register('notify_errors')}
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                通知频率控制
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    每小时最大通知数
                  </label>
                  <input
                    {...register('max_notifications_per_hour', {
                      min: { value: 1, message: '最少1条' },
                      max: { value: 100, message: '最多100条' }
                    })}
                    type="number"
                    className="input"
                    min="1"
                    max="100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    免打扰开始时间
                  </label>
                  <input
                    {...register('quiet_hours_start')}
                    type="time"
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    免打扰结束时间
                  </label>
                  <input
                    {...register('quiet_hours_end')}
                    type="time"
                    className="input"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className="btn btn-primary"
              >
                {loading ? '保存中...' : '保存设置'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  )
}

export default SettingsPage
