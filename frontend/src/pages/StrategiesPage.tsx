import React, { useState, useEffect } from 'react'
import {
  PlusIcon,
  PlayIcon,
  StopIcon,
  PencilIcon,
  TrashIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  ArrowPathIcon,
  ChartBarIcon,
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { strategyApi, apiKeyApi } from '@/services/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import { cn } from '@/utils/cn'
import toast from 'react-hot-toast'
import { useForm } from 'react-hook-form'

interface Strategy {
  id: number
  name: string
  description: string | null
  strategy_type: string
  exchange: string
  symbol: string
  market_type: string
  base_amount: number
  max_amount: number | null
  parameters: any
  status: string
  is_active: boolean
  total_orders: number
  total_trades: number
  total_profit: number
  total_loss: number
  win_rate: number
  created_at: string
  started_at: string | null
  last_executed_at: string | null
  last_error: string | null
}

interface APIKey {
  id: number
  name: string
  exchange: string
}

interface StrategyFilters {
  status: string
  strategy_type: string
  exchange: string
}

interface StrategyForm {
  name: string
  description?: string
  strategy_type: string
  exchange: string
  symbol: string
  market_type: string
  base_amount: number
  max_amount?: number
  parameters: any
  max_loss?: number
  stop_loss_percentage?: number
  take_profit_percentage?: number
  notes?: string
}

const StrategiesPage: React.FC = () => {
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingStrategy, setEditingStrategy] = useState<Strategy | null>(null)
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [actionLoading, setActionLoading] = useState<{[key: number]: boolean}>({})
  const [filters, setFilters] = useState<StrategyFilters>({
    status: '',
    strategy_type: '',
    exchange: ''
  })

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue
  } = useForm<StrategyForm>()

  const selectedStrategyType = watch('strategy_type')

  useEffect(() => {
    loadData()
  }, [filters])

  const loadData = async () => {
    try {
      setLoading(true)
      const [strategiesData, apiKeysData] = await Promise.all([
        strategyApi.getStrategies(filters),
        apiKeyApi.getApiKeys()
      ])
      setStrategies(strategiesData)
      setApiKeys(apiKeysData)
    } catch (error) {
      console.error('加载策略数据失败:', error)
      toast.error('加载策略数据失败')
    } finally {
      setLoading(false)
    }
  }

  const onSubmitStrategy = async (data: StrategyForm) => {
    try {
      if (editingStrategy) {
        await strategyApi.updateStrategy(editingStrategy.id, data)
        toast.success('策略更新成功')
      } else {
        await strategyApi.createStrategy(data)
        toast.success('策略创建成功')
      }

      setShowCreateForm(false)
      setEditingStrategy(null)
      reset()
      loadData()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '操作失败')
    }
  }

  const handleStartStrategy = async (id: number) => {
    try {
      setActionLoading(prev => ({ ...prev, [id]: true }))
      await strategyApi.startStrategy(id)
      toast.success('策略启动成功')
      loadData()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '启动失败')
    } finally {
      setActionLoading(prev => ({ ...prev, [id]: false }))
    }
  }

  const handleStopStrategy = async (id: number) => {
    try {
      setActionLoading(prev => ({ ...prev, [id]: true }))
      await strategyApi.stopStrategy(id)
      toast.success('策略停止成功')
      loadData()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '停止失败')
    } finally {
      setActionLoading(prev => ({ ...prev, [id]: false }))
    }
  }

  const handleDeleteStrategy = async (id: number) => {
    if (!confirm('确定要删除这个策略吗？此操作不可恢复。')) return

    try {
      await strategyApi.deleteStrategy(id)
      toast.success('策略删除成功')
      loadData()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '删除失败')
    }
  }

  const handleFilterChange = (key: keyof StrategyFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      status: '',
      strategy_type: '',
      exchange: ''
    })
    setSearchTerm('')
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <PlayIcon className="w-4 h-4 text-green-500" />
      case 'stopped':
        return <StopIcon className="w-4 h-4 text-gray-500" />
      case 'paused':
        return <ClockIcon className="w-4 h-4 text-yellow-500" />
      case 'error':
        return <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />
      case 'completed':
        return <CheckCircleIcon className="w-4 h-4 text-blue-500" />
      default:
        return <XCircleIcon className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: {[key: string]: string} = {
      'running': '运行中',
      'stopped': '已停止',
      'paused': '已暂停',
      'error': '错误',
      'completed': '已完成'
    }
    return statusMap[status] || status
  }

  const getStrategyTypeText = (type: string) => {
    const typeMap: {[key: string]: string} = {
      'grid': '网格策略',
      'martingale': '马丁格尔',
      'offset_order': '手动下单',
      'manual': '手动交易',
      'dca': '定投策略',
      'custom': '自定义'
    }
    return typeMap[type] || type
  }

  const getProfitColor = (profit: number) => {
    return profit > 0
      ? 'text-green-600 dark:text-green-400'
      : profit < 0
        ? 'text-red-600 dark:text-red-400'
        : 'text-gray-500'
  }

  const filteredStrategies = strategies.filter(strategy => {
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      return (
        strategy.name.toLowerCase().includes(term) ||
        strategy.symbol.toLowerCase().includes(term) ||
        strategy.exchange.toLowerCase().includes(term)
      )
    }
    return true
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            策略管理
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            创建和管理您的自动化交易策略
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadData}
            disabled={loading}
            className="btn btn-secondary"
          >
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            刷新
          </button>
          <button
            onClick={() => {
              setShowCreateForm(true)
              setEditingStrategy(null)
              reset()
            }}
            className="btn btn-primary"
          >
            <PlusIcon className="w-4 h-4 mr-2" />
            创建策略
          </button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜索框 */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索策略名称、交易对或交易所..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
          </div>

          {/* 筛选按钮 */}
          <div className="flex gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                'btn',
                showFilters ? 'btn-primary' : 'btn-secondary'
              )}
            >
              <FunnelIcon className="w-4 h-4 mr-2" />
              筛选
            </button>
            {Object.values(filters).some(v => v) && (
              <button
                onClick={clearFilters}
                className="btn btn-secondary"
              >
                <XMarkIcon className="w-4 h-4 mr-2" />
                清除
              </button>
            )}
          </div>
        </div>

        {/* 筛选面板 */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  状态
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="input"
                >
                  <option value="">全部状态</option>
                  <option value="running">运行中</option>
                  <option value="stopped">已停止</option>
                  <option value="paused">已暂停</option>
                  <option value="error">错误</option>
                  <option value="completed">已完成</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  策略类型
                </label>
                <select
                  value={filters.strategy_type}
                  onChange={(e) => handleFilterChange('strategy_type', e.target.value)}
                  className="input"
                >
                  <option value="">全部类型</option>
                  <option value="grid">网格策略</option>
                  <option value="martingale">马丁格尔</option>
                  <option value="offset_order">手动下单</option>
                  <option value="manual">手动交易</option>
                  <option value="dca">定投策略</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  交易所
                </label>
                <select
                  value={filters.exchange}
                  onChange={(e) => handleFilterChange('exchange', e.target.value)}
                  className="input"
                >
                  <option value="">全部交易所</option>
                  <option value="binance">Binance</option>
                  <option value="okx">OKX</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 策略列表 */}
      <div className="space-y-4">
        {filteredStrategies.length === 0 ? (
          <div className="card p-12 text-center">
            <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              暂无策略
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm || Object.values(filters).some(v => v)
                ? '没有找到符合条件的策略'
                : '创建您的第一个交易策略'}
            </p>
            {!searchTerm && !Object.values(filters).some(v => v) && (
              <div className="mt-6">
                <button
                  onClick={() => {
                    setShowCreateForm(true)
                    setEditingStrategy(null)
                    reset()
                  }}
                  className="btn btn-primary"
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  创建策略
                </button>
              </div>
            )}
          </div>
        ) : (
          filteredStrategies.map((strategy) => (
            <div key={strategy.id} className="card p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {strategy.name}
                    </h3>
                    <span className={cn(
                      'px-2 py-1 text-xs font-medium rounded-full',
                      strategy.strategy_type === 'grid'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : strategy.strategy_type === 'martingale'
                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                        : strategy.strategy_type === 'offset_order'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                    )}>
                      {getStrategyTypeText(strategy.strategy_type)}
                    </span>
                    <div className="flex items-center">
                      {getStatusIcon(strategy.status)}
                      <span className="ml-1 text-sm text-gray-600 dark:text-gray-400">
                        {getStatusText(strategy.status)}
                      </span>
                    </div>
                  </div>

                  <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-4">
                      <span>{strategy.exchange.toUpperCase()} • {strategy.symbol}</span>
                      <span>基础金额: ${strategy.base_amount}</span>
                      {strategy.max_amount && (
                        <span>最大金额: ${strategy.max_amount}</span>
                      )}
                    </div>
                  </div>

                  {strategy.description && (
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                      {strategy.description}
                    </p>
                  )}

                  {/* 统计信息 */}
                  <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">订单数</div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {strategy.total_orders}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">成交数</div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {strategy.total_trades}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">总盈利</div>
                      <div className={cn('text-sm font-medium', getProfitColor(strategy.total_profit))}>
                        {strategy.total_profit > 0 ? '+' : ''}{strategy.total_profit.toFixed(2)} USDT
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">胜率</div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {strategy.win_rate.toFixed(1)}%
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">创建时间</div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {new Date(strategy.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  {strategy.last_error && (
                    <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                      <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                            最近错误
                          </h3>
                          <div className="mt-1 text-sm text-red-700 dark:text-red-300">
                            {strategy.last_error}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-2 ml-6">
                  {strategy.status === 'stopped' || strategy.status === 'error' ? (
                    <button
                      onClick={() => handleStartStrategy(strategy.id)}
                      disabled={actionLoading[strategy.id]}
                      className="btn btn-success btn-sm"
                    >
                      {actionLoading[strategy.id] ? (
                        <LoadingSpinner size="sm" className="mr-1" />
                      ) : (
                        <PlayIcon className="w-4 h-4 mr-1" />
                      )}
                      启动
                    </button>
                  ) : strategy.status === 'running' ? (
                    <button
                      onClick={() => handleStopStrategy(strategy.id)}
                      disabled={actionLoading[strategy.id]}
                      className="btn btn-warning btn-sm"
                    >
                      {actionLoading[strategy.id] ? (
                        <LoadingSpinner size="sm" className="mr-1" />
                      ) : (
                        <StopIcon className="w-4 h-4 mr-1" />
                      )}
                      停止
                    </button>
                  ) : null}

                  <button
                    onClick={() => {
                      setEditingStrategy(strategy)
                      setShowCreateForm(true)
                      reset({
                        name: strategy.name,
                        description: strategy.description || '',
                        strategy_type: strategy.strategy_type,
                        exchange: strategy.exchange,
                        symbol: strategy.symbol,
                        market_type: strategy.market_type,
                        base_amount: strategy.base_amount,
                        max_amount: strategy.max_amount || undefined,
                        parameters: strategy.parameters
                      })
                    }}
                    className="btn btn-secondary btn-sm"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </button>

                  <button
                    onClick={() => handleDeleteStrategy(strategy.id)}
                    disabled={strategy.status === 'running'}
                    className="btn btn-danger btn-sm disabled:opacity-50"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default StrategiesPage
