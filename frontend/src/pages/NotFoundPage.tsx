import React from 'react'
import { Link } from 'react-router-dom'
import { HomeIcon } from '@heroicons/react/24/outline'

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-9xl font-bold text-primary-600 dark:text-primary-400">
            404
          </h1>
          <h2 className="mt-4 text-3xl font-bold text-gray-900 dark:text-white">
            页面未找到
          </h2>
          <p className="mt-2 text-base text-gray-500 dark:text-gray-400">
            抱歉，您访问的页面不存在或已被移除。
          </p>
          
          <div className="mt-8">
            <Link
              to="/dashboard"
              className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
            >
              <HomeIcon className="mr-2 h-5 w-5" />
              返回首页
            </Link>
          </div>
          
          <div className="mt-6">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              或者您可以{' '}
              <button
                onClick={() => window.history.back()}
                className="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
              >
                返回上一页
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NotFoundPage
