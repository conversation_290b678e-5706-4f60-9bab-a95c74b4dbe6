@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS变量定义 */
:root {
  /* 主题色 */
  --primary-50: #eff6ff;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* 成功色 */
  --success-500: #22c55e;
  --success-600: #16a34a;
  
  /* 危险色 */
  --danger-500: #ef4444;
  --danger-600: #dc2626;
  
  /* 警告色 */
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  /* Toast样式变量 */
  --toast-bg: #ffffff;
  --toast-color: #1f2937;
  --toast-border: #e5e7eb;
}

/* 暗色模式变量 */
.dark {
  --toast-bg: #374151;
  --toast-color: #f9fafb;
  --toast-border: #4b5563;
}

/* 基础样式 */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-200;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
  
  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮基础样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md;
    @apply transition-all duration-200 ease-in-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
    @apply focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
    @apply dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600;
    @apply focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700;
    @apply focus:ring-success-500;
  }
  
  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700;
    @apply focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50;
    @apply dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700;
  }
  
  /* 输入框样式 */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm;
    @apply placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
    @apply dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:placeholder-gray-500;
    @apply transition-colors duration-200;
  }
  
  .input-error {
    @apply border-danger-500 focus:ring-danger-500 focus:border-danger-500;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700;
    @apply transition-all duration-200;
  }
  
  .card-hover {
    @apply hover:shadow-medium hover:border-gray-300 dark:hover:border-gray-600;
  }
  
  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
  }
  
  .badge-gray {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
  }
  
  /* 表格样式 */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }
  
  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
    @apply bg-gray-50 dark:bg-gray-800;
  }
  
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }
  
  .table tbody tr {
    @apply hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150;
  }
  
  /* 加载动画 */
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
  }
  
  .loading-dots > div:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  .loading-dots > div:nth-child(3) {
    animation-delay: 0.4s;
  }
  
  /* 状态指示器 */
  .status-indicator {
    @apply inline-flex items-center;
  }
  
  .status-indicator::before {
    @apply content-[''] w-2 h-2 rounded-full mr-2;
  }
  
  .status-online::before {
    @apply bg-success-500;
  }
  
  .status-offline::before {
    @apply bg-gray-400;
  }
  
  .status-error::before {
    @apply bg-danger-500;
  }
  
  .status-warning::before {
    @apply bg-warning-500;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本渐变 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
  }
  
  /* 玻璃效果 */
  .glass {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-900/80;
    @apply border border-white/20 dark:border-gray-700/50;
  }
  
  /* 阴影效果 */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  /* 动画类 */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  /* 响应式隐藏 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* 安全区域 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}
