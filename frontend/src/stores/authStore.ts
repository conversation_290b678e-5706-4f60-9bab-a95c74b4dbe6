import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authApi } from '@/services/api'
import toast from 'react-hot-toast'

export interface User {
  id: number
  username: string
  email?: string
  is_active: boolean
  created_at?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (username: string, password: string) => Promise<boolean>
  register: (username: string, email: string, password: string) => Promise<boolean>
  logout: () => void
  refreshToken: () => Promise<boolean>
  initializeAuth: () => void
  updateUser: (user: Partial<User>) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,

      login: async (username: string, password: string) => {
        try {
          set({ isLoading: true })
          
          const response = await authApi.login(username, password)
          
          if (response.access_token && response.user) {
            set({
              token: response.access_token,
              user: response.user,
              isAuthenticated: true,
              isLoading: false
            })
            
            // 设置API默认token
            authApi.setToken(response.access_token)
            
            toast.success('登录成功')
            return true
          }
          
          return false
        } catch (error: any) {
          console.error('登录失败:', error)
          
          const message = error.response?.data?.detail || '登录失败，请检查用户名和密码'
          toast.error(message)
          
          set({ isLoading: false })
          return false
        }
      },

      register: async (username: string, email: string, password: string) => {
        try {
          set({ isLoading: true })
          
          await authApi.register(username, email, password)
          
          toast.success('注册成功，请登录')
          set({ isLoading: false })
          return true
        } catch (error: any) {
          console.error('注册失败:', error)
          
          const message = error.response?.data?.detail || '注册失败，请重试'
          toast.error(message)
          
          set({ isLoading: false })
          return false
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
        
        // 清除API token
        authApi.setToken(null)
        
        toast.success('已退出登录')
      },

      refreshToken: async () => {
        try {
          const { token } = get()
          if (!token) return false
          
          const response = await authApi.refreshToken()
          
          if (response.access_token) {
            set({ token: response.access_token })
            authApi.setToken(response.access_token)
            return true
          }
          
          return false
        } catch (error) {
          console.error('刷新token失败:', error)
          
          // Token无效，清除认证状态
          get().logout()
          return false
        }
      },

      initializeAuth: async () => {
        try {
          const { token } = get()
          
          if (!token) {
            set({ isLoading: false })
            return
          }
          
          // 设置API token
          authApi.setToken(token)
          
          // 验证token有效性
          const user = await authApi.getCurrentUser()
          
          if (user) {
            set({
              user,
              isAuthenticated: true,
              isLoading: false
            })
          } else {
            // Token无效，清除状态
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false
            })
          }
        } catch (error) {
          console.error('初始化认证失败:', error)
          
          // 清除无效状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })
          
          authApi.setToken(null)
        }
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData }
          })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
