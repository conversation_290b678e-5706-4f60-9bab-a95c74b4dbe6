import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authApi } from '@/services/api'
import toast from 'react-hot-toast'

export interface User {
  id: number
  username: string
  email?: string
  is_active: boolean
  created_at?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean

  // Actions
  login: (username: string, password: string) => Promise<boolean>
  register: (username: string, email: string, password: string) => Promise<boolean>
  logout: () => void
  refreshToken: () => Promise<boolean>
  initializeAuth: () => void
  updateUser: (user: Partial<User>) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: true,

      login: async (username: string, password: string) => {
        try {
          set({ isLoading: true })

          const response = await authApi.login(username, password)

          if (response.access_token && response.user) {
            set({
              token: response.access_token,
              user: response.user,
              isAuthenticated: true,
              isLoading: false
            })

            // 设置API默认token
            authApi.setToken(response.access_token)

            toast.success('登录成功')
            return true
          }

          return false
        } catch (error: any) {
          console.error('登录失败:', error)

          const message = error.response?.data?.detail || '登录失败，请检查用户名和密码'
          toast.error(message)

          set({ isLoading: false })
          return false
        }
      },

      register: async (username: string, email: string, password: string) => {
        try {
          set({ isLoading: true })

          await authApi.register(username, email, password)

          toast.success('注册成功，请登录')
          set({ isLoading: false })
          return true
        } catch (error: any) {
          console.error('注册失败:', error)

          const message = error.response?.data?.detail || '注册失败，请重试'
          toast.error(message)

          set({ isLoading: false })
          return false
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })

        // 清除API token
        authApi.setToken(null)

        toast.success('已退出登录')
      },

      refreshToken: async () => {
        try {
          const { token } = get()
          if (!token) return false

          const response = await authApi.refreshToken()

          if (response.access_token) {
            set({ token: response.access_token })
            authApi.setToken(response.access_token)
            return true
          }

          return false
        } catch (error) {
          console.error('刷新token失败:', error)

          // Token无效，清除认证状态
          get().logout()
          return false
        }
      },

      initializeAuth: async () => {
        try {
          const { token } = get()

          if (!token) {
            console.log('没有找到token，设置为未认证状态')
            set({ isLoading: false, isAuthenticated: false })
            return
          }

          console.log('找到token，验证有效性...')

          // 设置API token
          authApi.setToken(token)

          // 验证token有效性
          const user = await authApi.getCurrentUser()

          if (user) {
            console.log('Token验证成功，用户已认证:', user.username)
            set({
              user,
              isAuthenticated: true,
              isLoading: false
            })
          } else {
            console.log('Token验证失败，清除认证状态')
            // Token无效，清除状态
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false
            })
            authApi.setToken(null)
          }
        } catch (error: any) {
          console.error('初始化认证失败:', error)

          // 如果是网络错误或服务器错误，保持当前状态但设置为未加载
          if (!error.response || error.response.status >= 500) {
            console.log('网络或服务器错误，暂时保持未认证状态')
            set({
              isLoading: false,
              isAuthenticated: false
            })
            return
          }

          // 如果是401错误，说明token无效
          if (error.response?.status === 401) {
            console.log('Token已过期或无效，清除认证状态')
          }

          // 清除无效状态
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })

          authApi.setToken(null)
        }
      },

      updateUser: (userData: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData }
          })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
