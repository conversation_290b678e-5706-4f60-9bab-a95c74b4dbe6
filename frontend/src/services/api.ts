import axios, { AxiosInstance, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api/v1'

console.log('API配置:', {
  baseURL: API_BASE_URL,
  env: import.meta.env.VITE_API_URL
})

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('auth-storage')
    if (token) {
      try {
        const authData = JSON.parse(token)
        if (authData.state?.token) {
          config.headers.Authorization = `Bearer ${authData.state.token}`
          console.log('API请求添加token:', authData.state.token.substring(0, 20) + '...')
        } else {
          console.log('未找到token在auth-storage中')
        }
      } catch (error) {
        console.error('解析token失败:', error)
        // 清除无效的localStorage数据
        localStorage.removeItem('auth-storage')
      }
    } else {
      console.log('localStorage中没有auth-storage')
    }

    console.log('API请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    // 处理通用错误
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          // 未授权，但不要立即跳转，让组件处理
          console.log('API请求401错误，token可能已过期', {
            url: error.config?.url,
            method: error.config?.method
          })
          // 只在非登录相关页面时才显示错误信息
          if (!window.location.pathname.includes('/login') && !window.location.pathname.includes('/register')) {
            toast.error('登录已过期，请重新登录')
          }
          break
        case 403:
          toast.error('权限不足')
          break
        case 404:
          // 对于404错误，不显示toast，让页面组件处理
          console.log('API请求404错误:', error.config?.url)
          break
        case 422:
          // 验证错误
          if (data.detail && Array.isArray(data.detail)) {
            const errors = data.detail.map((err: any) => err.msg).join(', ')
            toast.error(`验证错误: ${errors}`)
          } else {
            toast.error(data.detail || '请求参数错误')
          }
          break
        case 500:
          toast.error('服务器内部错误')
          break
        default:
          // 只对非预期的错误显示通用错误信息
          if (status >= 500) {
            toast.error(data.detail || '服务器错误')
          }
      }
    } else if (error.request) {
      toast.error('网络连接失败，请检查网络')
    } else {
      toast.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

// 认证API
export const authApi = {
  setToken: (token: string | null) => {
    if (token) {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`
    } else {
      delete apiClient.defaults.headers.common['Authorization']
    }
  },

  login: async (username: string, password: string) => {
    const formData = new FormData()
    formData.append('username', username)
    formData.append('password', password)

    const response = await apiClient.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    return response.data
  },

  register: async (username: string, email: string, password: string) => {
    const response = await apiClient.post('/auth/register', {
      username,
      email,
      password,
    })
    return response.data
  },

  getCurrentUser: async () => {
    const response = await apiClient.get('/auth/me')
    return response.data
  },

  refreshToken: async () => {
    const response = await apiClient.post('/auth/refresh')
    return response.data
  },

  logout: async () => {
    const response = await apiClient.post('/auth/logout')
    return response.data
  },
}

// 用户API
export const userApi = {
  getProfile: async () => {
    const response = await apiClient.get('/users/profile')
    return response.data
  },

  updateProfile: async (data: any) => {
    const response = await apiClient.put('/users/profile', data)
    return response.data
  },

  getStats: async () => {
    const response = await apiClient.get('/users/stats')
    return response.data
  },

  deleteAccount: async () => {
    const response = await apiClient.delete('/users/account')
    return response.data
  },
}

// API密钥API
export const apiKeyApi = {
  getApiKeys: async () => {
    const response = await apiClient.get('/api-keys')
    return response.data
  },

  createApiKey: async (data: any) => {
    const response = await apiClient.post('/api-keys', data)
    return response.data
  },

  updateApiKey: async (id: number, data: any) => {
    const response = await apiClient.put(`/api-keys/${id}`, data)
    return response.data
  },

  deleteApiKey: async (id: number) => {
    const response = await apiClient.delete(`/api-keys/${id}`)
    return response.data
  },

  testConnection: async (id: number) => {
    const response = await apiClient.post(`/api-keys/${id}/test`)
    return response.data
  },
}

// 订单API
export const orderApi = {
  getOrders: async (params?: any) => {
    const response = await apiClient.get('/orders', { params })
    return response.data
  },

  createOrder: async (data: any) => {
    const response = await apiClient.post('/orders', data)
    return response.data
  },

  getOrder: async (id: number) => {
    const response = await apiClient.get(`/orders/${id}`)
    return response.data
  },

  cancelOrder: async (id: number) => {
    const response = await apiClient.delete(`/orders/${id}`)
    return response.data
  },

  getActiveOrdersCount: async () => {
    const response = await apiClient.get('/orders/active/count')
    return response.data
  },
}

// 交易API
export const tradeApi = {
  getTrades: async (params?: any) => {
    const response = await apiClient.get('/trades', { params })
    return response.data
  },

  getTrade: async (id: number) => {
    const response = await apiClient.get(`/trades/${id}`)
    return response.data
  },

  getTradeStats: async (days: number = 30) => {
    const response = await apiClient.get('/trades/stats/summary', {
      params: { days }
    })
    return response.data
  },

  getDailyStats: async (days: number = 30) => {
    const response = await apiClient.get('/trades/stats/daily', {
      params: { days }
    })
    return response.data
  },

  getSymbolStats: async (days: number = 30) => {
    const response = await apiClient.get('/trades/stats/symbols', {
      params: { days }
    })
    return response.data
  },
}

// 策略API
export const strategyApi = {
  getStrategies: async (params?: any) => {
    const response = await apiClient.get('/strategies', { params })
    return response.data
  },

  createStrategy: async (data: any) => {
    const response = await apiClient.post('/strategies', data)
    return response.data
  },

  getStrategy: async (id: number) => {
    const response = await apiClient.get(`/strategies/${id}`)
    return response.data
  },

  updateStrategy: async (id: number, data: any) => {
    const response = await apiClient.put(`/strategies/${id}`, data)
    return response.data
  },

  deleteStrategy: async (id: number) => {
    const response = await apiClient.delete(`/strategies/${id}`)
    return response.data
  },

  startStrategy: async (id: number) => {
    const response = await apiClient.post(`/strategies/${id}/start`)
    return response.data
  },

  stopStrategy: async (id: number) => {
    const response = await apiClient.post(`/strategies/${id}/stop`)
    return response.data
  },
}

// 行情API
export const marketApi = {
  getTicker: async (symbol: string) => {
    const response = await apiClient.get(`/market/ticker/${symbol}`)
    return response.data
  },

  getAllTickers: async (exchange?: string) => {
    const response = await apiClient.get('/market/ticker', {
      params: { exchange }
    })
    return response.data
  },

  getKlines: async (symbol: string, interval: string = '1h', limit: number = 100) => {
    const response = await apiClient.get(`/market/klines/${symbol}`, {
      params: { interval, limit }
    })
    return response.data
  },

  getOrderBook: async (symbol: string, limit: number = 20) => {
    const response = await apiClient.get(`/market/orderbook/${symbol}`, {
      params: { limit }
    })
    return response.data
  },

  getSymbols: async (exchange?: string) => {
    const response = await apiClient.get('/market/symbols', {
      params: { exchange }
    })
    return response.data
  },
}

// Telegram API
export const telegramApi = {
  getSettings: async () => {
    const response = await apiClient.get('/telegram')
    return response.data
  },

  createSettings: async (data: any) => {
    const response = await apiClient.post('/telegram', data)
    return response.data
  },

  updateSettings: async (data: any) => {
    const response = await apiClient.put('/telegram', data)
    return response.data
  },

  deleteSettings: async () => {
    const response = await apiClient.delete('/telegram')
    return response.data
  },

  verify: async () => {
    const response = await apiClient.post('/telegram/verify')
    return response.data
  },

  testNotification: async () => {
    const response = await apiClient.post('/telegram/test')
    return response.data
  },

  setBotToken: async (token: string) => {
    const response = await apiClient.post('/telegram/bot-token', { token })
    return response.data
  },

  getBotTokenStatus: async () => {
    const response = await apiClient.get('/telegram/bot-token/status')
    return response.data
  },
}

// 风控API
export const riskApi = {
  getEvents: async (params?: any) => {
    const response = await apiClient.get('/risk/events', { params })
    return response.data
  },

  getEvent: async (id: number) => {
    const response = await apiClient.get(`/risk/events/${id}`)
    return response.data
  },

  resolveEvent: async (id: number, notes?: string) => {
    const response = await apiClient.post(`/risk/events/${id}/resolve`, {
      resolution_notes: notes
    })
    return response.data
  },

  getStats: async (days: number = 30) => {
    const response = await apiClient.get('/risk/stats', {
      params: { days }
    })
    return response.data
  },

  getRules: async () => {
    const response = await apiClient.get('/risk/rules')
    return response.data
  },

  createRule: async (data: any) => {
    const response = await apiClient.post('/risk/rules', data)
    return response.data
  },

  checkStatus: async () => {
    const response = await apiClient.get('/risk/check')
    return response.data
  },
}

// 系统API
export const systemApi = {
  getHealth: async () => {
    const response = await apiClient.get('/health')
    return response.data
  },

  getInfo: async () => {
    const response = await apiClient.get('/info')
    return response.data
  },
}

export default apiClient
