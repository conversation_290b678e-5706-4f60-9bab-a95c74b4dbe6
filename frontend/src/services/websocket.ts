import { useAuthStore } from '@/stores/authStore'

export interface WebSocketMessage {
  type: string
  data?: any
  symbol?: string
  timestamp?: string
}

export interface TickerData {
  symbol: string
  price: number
  change_24h: number
  change_24h_percent: number
  timestamp: string
}

class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 1000
  private isConnecting = false
  private subscribers: Map<string, Set<(data: any) => void>> = new Map()
  private subscribedSymbols: Set<string> = new Set()

  constructor() {
    this.connect()
  }

  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host
    return `${protocol}//${host}/api/v1/market/ws`
  }

  private connect(): void {
    if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
      return
    }

    this.isConnecting = true
    const token = useAuthStore.getState().token

    if (!token) {
      console.warn('WebSocket: 未找到认证token')
      this.isConnecting = false
      return
    }

    try {
      const wsUrl = `${this.getWebSocketUrl()}?token=${encodeURIComponent(token)}`
      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        console.log('WebSocket连接已建立')
        this.isConnecting = false
        this.reconnectAttempts = 0

        // 重新订阅之前的symbols
        if (this.subscribedSymbols.size > 0) {
          this.subscribe(Array.from(this.subscribedSymbols))
        }
      }

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (error) {
          console.error('WebSocket消息解析失败:', error)
        }
      }

      this.ws.onclose = (event) => {
        console.log('WebSocket连接已关闭:', event.code, event.reason)
        this.isConnecting = false
        this.ws = null

        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++
          console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)
          
          setTimeout(() => {
            this.connect()
          }, this.reconnectInterval * this.reconnectAttempts)
        } else {
          console.error('WebSocket重连失败，已达到最大重试次数')
        }
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        this.isConnecting = false
      }

    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.isConnecting = false
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    const { type, data } = message

    switch (type) {
      case 'ticker':
        this.notifySubscribers('ticker', data)
        if (data.symbol) {
          this.notifySubscribers(`ticker:${data.symbol}`, data)
        }
        break

      case 'kline':
        this.notifySubscribers('kline', data)
        if (data.symbol) {
          this.notifySubscribers(`kline:${data.symbol}`, data)
        }
        break

      case 'orderbook':
        this.notifySubscribers('orderbook', data)
        if (data.symbol) {
          this.notifySubscribers(`orderbook:${data.symbol}`, data)
        }
        break

      case 'subscribed':
        console.log('WebSocket订阅成功:', data)
        break

      case 'unsubscribed':
        console.log('WebSocket取消订阅成功')
        break

      case 'error':
        console.error('WebSocket服务器错误:', data)
        break

      default:
        console.log('未知WebSocket消息类型:', type, data)
    }
  }

  private notifySubscribers(channel: string, data: any): void {
    const subscribers = this.subscribers.get(channel)
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('WebSocket订阅回调错误:', error)
        }
      })
    }
  }

  public subscribe(symbols: string[], exchange: string = 'binance', dataType: string = 'ticker', interval?: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      // 保存订阅信息，连接建立后自动订阅
      symbols.forEach(symbol => this.subscribedSymbols.add(symbol))
      return
    }

    const message: any = {
      type: 'subscribe',
      exchange: exchange,
      symbols: symbols,
      data_type: dataType
    }

    if (dataType === 'kline' && interval) {
      message.interval = interval
    }

    this.ws.send(JSON.stringify(message))
    symbols.forEach(symbol => this.subscribedSymbols.add(symbol))
  }

  public unsubscribe(symbols?: string[], exchange: string = 'binance', dataType: string = 'ticker', interval?: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return
    }

    const message: any = {
      type: 'unsubscribe',
      exchange: exchange,
      symbols: symbols || Array.from(this.subscribedSymbols),
      data_type: dataType
    }

    if (dataType === 'kline' && interval) {
      message.interval = interval
    }

    this.ws.send(JSON.stringify(message))

    if (symbols) {
      symbols.forEach(symbol => this.subscribedSymbols.delete(symbol))
    } else {
      this.subscribedSymbols.clear()
    }
  }

  public on(channel: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set())
    }

    this.subscribers.get(channel)!.add(callback)

    // 返回取消订阅函数
    return () => {
      const subscribers = this.subscribers.get(channel)
      if (subscribers) {
        subscribers.delete(callback)
        if (subscribers.size === 0) {
          this.subscribers.delete(channel)
        }
      }
    }
  }

  public off(channel: string, callback?: (data: any) => void): void {
    if (!callback) {
      this.subscribers.delete(channel)
      return
    }

    const subscribers = this.subscribers.get(channel)
    if (subscribers) {
      subscribers.delete(callback)
      if (subscribers.size === 0) {
        this.subscribers.delete(channel)
      }
    }
  }

  public disconnect(): void {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    this.subscribers.clear()
    this.subscribedSymbols.clear()
    this.reconnectAttempts = this.maxReconnectAttempts // 阻止自动重连
  }

  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  public getConnectionState(): string {
    if (!this.ws) return 'disconnected'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting'
      case WebSocket.OPEN:
        return 'connected'
      case WebSocket.CLOSING:
        return 'closing'
      case WebSocket.CLOSED:
        return 'disconnected'
      default:
        return 'unknown'
    }
  }
}

// 创建全局WebSocket实例
export const wsService = new WebSocketService()

// React Hook for WebSocket
export const useWebSocket = () => {
  return {
    subscribe: wsService.subscribe.bind(wsService),
    unsubscribe: wsService.unsubscribe.bind(wsService),
    on: wsService.on.bind(wsService),
    off: wsService.off.bind(wsService),
    isConnected: wsService.isConnected.bind(wsService),
    getConnectionState: wsService.getConnectionState.bind(wsService),
  }
}
