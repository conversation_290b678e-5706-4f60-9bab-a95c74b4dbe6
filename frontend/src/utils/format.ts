/**
 * 格式化工具函数
 */

/**
 * 格式化数字为货币格式
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USDT',
  decimals: number = 2
): string => {
  return `${amount.toFixed(decimals)} ${currency}`
}

/**
 * 格式化百分比
 */
export const formatPercentage = (
  value: number,
  decimals: number = 2
): string => {
  return `${value.toFixed(decimals)}%`
}

/**
 * 格式化大数字（K, M, B）
 */
export const formatLargeNumber = (num: number): string => {
  if (num >= 1e9) {
    return (num / 1e9).toFixed(1) + 'B'
  }
  if (num >= 1e6) {
    return (num / 1e6).toFixed(1) + 'M'
  }
  if (num >= 1e3) {
    return (num / 1e3).toFixed(1) + 'K'
  }
  return num.toString()
}

/**
 * 格式化日期时间
 */
export const formatDateTime = (
  date: string | Date,
  options?: Intl.DateTimeFormatOptions
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }
  
  return dateObj.toLocaleString('zh-CN', { ...defaultOptions, ...options })
}

/**
 * 格式化相对时间
 */
export const formatRelativeTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  
  if (diffSeconds < 60) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return formatDateTime(dateObj, { month: '2-digit', day: '2-digit' })
  }
}

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化交易对
 */
export const formatSymbol = (symbol: string): string => {
  // 将 BTCUSDT 格式化为 BTC/USDT
  const commonQuotes = ['USDT', 'BUSD', 'BTC', 'ETH', 'BNB']
  
  for (const quote of commonQuotes) {
    if (symbol.endsWith(quote)) {
      const base = symbol.slice(0, -quote.length)
      return `${base}/${quote}`
    }
  }
  
  return symbol
}

/**
 * 格式化订单状态
 */
export const formatOrderStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    open: '已挂单',
    filled: '已成交',
    partially_filled: '部分成交',
    cancelled: '已取消',
    rejected: '已拒绝',
    expired: '已过期'
  }
  
  return statusMap[status] || status
}

/**
 * 格式化策略类型
 */
export const formatStrategyType = (type: string): string => {
  const typeMap: Record<string, string> = {
    manual: '手动交易',
    grid: '网格策略',
    martingale: '马丁格尔',
    offset_order: '偏移挂单',
    dca: '定投策略',
    custom: '自定义策略'
  }
  
  return typeMap[type] || type
}

/**
 * 格式化风险等级
 */
export const formatRiskLevel = (level: string): string => {
  const levelMap: Record<string, string> = {
    low: '低风险',
    medium: '中等风险',
    high: '高风险',
    critical: '严重风险'
  }
  
  return levelMap[level] || level
}

/**
 * 截断文本
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

/**
 * 格式化价格精度
 */
export const formatPrice = (price: number, decimals: number = 8): string => {
  // 移除尾随零
  return parseFloat(price.toFixed(decimals)).toString()
}

/**
 * 格式化数量精度
 */
export const formatQuantity = (quantity: number, decimals: number = 8): string => {
  // 移除尾随零
  return parseFloat(quantity.toFixed(decimals)).toString()
}
