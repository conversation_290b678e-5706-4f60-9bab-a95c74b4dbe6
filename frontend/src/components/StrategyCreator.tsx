import React, { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { strategyApi, apiKey<PERSON><PERSON> } from '../services/api'

interface StrategyType {
  name: string
  description: string
  parameters: Record<string, any>
  risk_level: string
  suitable_market: string[]
  min_capital: number
}

interface APIKey {
  id: number
  name: string
  exchange: string
}

interface StrategyCreatorProps {
  onClose: () => void
  onSuccess: () => void
}

const StrategyCreator: React.FC<StrategyCreatorProps> = ({ onClose, onSuccess }) => {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [strategyTypes, setStrategyTypes] = useState<Record<string, StrategyType>>({})
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  
  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    strategy_type: '',
    exchange: '',
    symbol: 'BTCUSDT',
    market_type: 'spot',
    base_amount: 1000,
    max_amount: null as number | null,
    parameters: {} as Record<string, any>,
    api_key_id: null as number | null
  })

  // 验证结果
  const [validationResult, setValidationResult] = useState<any>(null)
  const [backtestResult, setBacktestResult] = useState<any>(null)

  useEffect(() => {
    loadStrategyTypes()
    loadApiKeys()
  }, [])

  const loadStrategyTypes = async () => {
    try {
      const types = await strategyApi.getStrategyTypes()
      setStrategyTypes(types)
    } catch (error) {
      toast.error('加载策略类型失败')
    }
  }

  const loadApiKeys = async () => {
    try {
      const keys = await apiKeyApi.getApiKeys()
      setApiKeys(keys)
    } catch (error) {
      toast.error('加载API密钥失败')
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleParameterChange = (paramName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [paramName]: value
      }
    }))
  }

  const validateStrategy = async () => {
    try {
      setLoading(true)
      const result = await strategyApi.validateStrategy(
        formData.strategy_type,
        formData.parameters
      )
      setValidationResult(result)
      
      if (result.valid) {
        toast.success('策略配置验证通过')
        setStep(3)
      } else {
        toast.error(result.message)
      }
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '验证失败')
    } finally {
      setLoading(false)
    }
  }

  const runBacktest = async () => {
    try {
      setLoading(true)
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 3) // 3个月回测

      const backtestData = {
        strategy_type: formData.strategy_type,
        symbol: formData.symbol,
        initial_capital: formData.base_amount,
        parameters: formData.parameters,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        interval: '1h',
        fee_rate: 0.001
      }

      const result = await strategyApi.runBacktest(backtestData)
      setBacktestResult(result.result)
      toast.success('回测完成')
      setStep(4)
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '回测失败')
    } finally {
      setLoading(false)
    }
  }

  const createStrategy = async () => {
    try {
      setLoading(true)
      await strategyApi.createStrategy(formData)
      toast.success('策略创建成功')
      onSuccess()
      onClose()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '创建失败')
    } finally {
      setLoading(false)
    }
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">基础信息</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">策略名称</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className="input input-bordered w-full"
            placeholder="输入策略名称"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">策略类型</label>
          <select
            value={formData.strategy_type}
            onChange={(e) => handleInputChange('strategy_type', e.target.value)}
            className="select select-bordered w-full"
          >
            <option value="">选择策略类型</option>
            {Object.entries(strategyTypes).map(([key, type]) => (
              <option key={key} value={key}>
                {type.name} - {type.description}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">交易对</label>
          <input
            type="text"
            value={formData.symbol}
            onChange={(e) => handleInputChange('symbol', e.target.value)}
            className="input input-bordered w-full"
            placeholder="BTCUSDT"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">初始资金</label>
          <input
            type="number"
            value={formData.base_amount}
            onChange={(e) => handleInputChange('base_amount', Number(e.target.value))}
            className="input input-bordered w-full"
            min="100"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">API密钥</label>
          <select
            value={formData.api_key_id || ''}
            onChange={(e) => handleInputChange('api_key_id', Number(e.target.value))}
            className="select select-bordered w-full"
          >
            <option value="">选择API密钥</option>
            {apiKeys.map(key => (
              <option key={key.id} value={key.id}>
                {key.name} ({key.exchange})
              </option>
            ))}
          </select>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">策略描述</label>
        <textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          className="textarea textarea-bordered w-full"
          rows={3}
          placeholder="描述策略的目标和特点"
        />
      </div>

      <div className="flex justify-end space-x-2">
        <button onClick={onClose} className="btn btn-ghost">
          取消
        </button>
        <button
          onClick={() => setStep(2)}
          disabled={!formData.name || !formData.strategy_type}
          className="btn btn-primary"
        >
          下一步
        </button>
      </div>
    </div>
  )

  const renderStep2 = () => {
    const currentType = strategyTypes[formData.strategy_type]
    if (!currentType) return null

    return (
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">策略参数配置</h3>
        
        <div className="bg-base-200 p-4 rounded-lg">
          <h4 className="font-medium mb-2">{currentType.name}</h4>
          <p className="text-sm text-gray-600 mb-2">{currentType.description}</p>
          <div className="flex items-center space-x-4 text-sm">
            <span className={`badge ${
              currentType.risk_level === 'high' ? 'badge-error' :
              currentType.risk_level === 'medium' ? 'badge-warning' : 'badge-success'
            }`}>
              风险: {currentType.risk_level}
            </span>
            <span className="text-gray-500">
              最小资金: ${currentType.min_capital}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(currentType.parameters).map(([paramName, paramConfig]: [string, any]) => (
            <div key={paramName}>
              <label className="block text-sm font-medium mb-2">
                {paramConfig.name}
              </label>
              
              {paramConfig.type === 'select' ? (
                <select
                  value={formData.parameters[paramName] || paramConfig.default || ''}
                  onChange={(e) => handleParameterChange(paramName, e.target.value)}
                  className="select select-bordered w-full"
                >
                  {paramConfig.options?.map((option: any) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type={paramConfig.type === 'int' ? 'number' : paramConfig.type}
                  value={formData.parameters[paramName] || paramConfig.default || ''}
                  onChange={(e) => {
                    const value = paramConfig.type === 'int' || paramConfig.type === 'float' 
                      ? Number(e.target.value) 
                      : e.target.value
                    handleParameterChange(paramName, value)
                  }}
                  min={paramConfig.min}
                  max={paramConfig.max}
                  step={paramConfig.type === 'float' ? '0.01' : '1'}
                  className="input input-bordered w-full"
                  placeholder={paramConfig.description}
                />
              )}
              
              {paramConfig.description && (
                <p className="text-xs text-gray-500 mt-1">{paramConfig.description}</p>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-between">
          <button onClick={() => setStep(1)} className="btn btn-ghost">
            上一步
          </button>
          <button
            onClick={validateStrategy}
            disabled={loading}
            className="btn btn-primary"
          >
            {loading ? '验证中...' : '验证配置'}
          </button>
        </div>
      </div>
    )
  }

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">配置验证结果</h3>
      
      {validationResult && (
        <div className="space-y-4">
          <div className={`alert ${validationResult.valid ? 'alert-success' : 'alert-error'}`}>
            <span>{validationResult.message}</span>
          </div>

          {validationResult.risk_metrics && (
            <div className="bg-base-200 p-4 rounded-lg">
              <h4 className="font-medium mb-3">风险评估</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">最大潜在亏损:</span>
                  <span className="ml-2 font-medium">
                    ${validationResult.risk_metrics.max_potential_loss?.toFixed(2)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">资金利用率:</span>
                  <span className="ml-2 font-medium">
                    {(validationResult.risk_metrics.capital_utilization * 100)?.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          )}

          {validationResult.capital_requirements && (
            <div className="bg-base-200 p-4 rounded-lg">
              <h4 className="font-medium mb-3">资金需求</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">最小资金:</span>
                  <span className="ml-2 font-medium">
                    ${validationResult.capital_requirements.min_capital}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">建议资金:</span>
                  <span className="ml-2 font-medium">
                    ${validationResult.capital_requirements.recommended_capital?.toFixed(0)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="flex justify-between">
        <button onClick={() => setStep(2)} className="btn btn-ghost">
          上一步
        </button>
        <div className="space-x-2">
          <button
            onClick={runBacktest}
            disabled={loading}
            className="btn btn-secondary"
          >
            {loading ? '回测中...' : '运行回测'}
          </button>
          <button
            onClick={createStrategy}
            disabled={loading}
            className="btn btn-primary"
          >
            {loading ? '创建中...' : '直接创建'}
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">创建策略</h2>
          <button onClick={onClose} className="btn btn-ghost btn-sm">
            ✕
          </button>
        </div>

        {/* 步骤指示器 */}
        <div className="steps steps-horizontal w-full mb-8">
          <div className={`step ${step >= 1 ? 'step-primary' : ''}`}>基础信息</div>
          <div className={`step ${step >= 2 ? 'step-primary' : ''}`}>参数配置</div>
          <div className={`step ${step >= 3 ? 'step-primary' : ''}`}>验证配置</div>
          <div className={`step ${step >= 4 ? 'step-primary' : ''}`}>回测结果</div>
        </div>

        {/* 步骤内容 */}
        {step === 1 && renderStep1()}
        {step === 2 && renderStep2()}
        {step === 3 && renderStep3()}
      </div>
    </div>
  )
}

export default StrategyCreator
