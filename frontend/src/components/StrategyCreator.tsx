import React, { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { strategy<PERSON>pi, api<PERSON>eyA<PERSON> } from '../services/api'
import {
  SparklesIcon,
  RocketLaunchIcon,
  ChartBarIcon,
  CogIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  XMarkIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  BeakerIcon,
  AdjustmentsHorizontalIcon,
  ChartPieIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'

interface StrategyType {
  name: string
  description: string
  parameters: Record<string, any>
  risk_level: string
  suitable_market: string[]
  min_capital: number
}

interface APIKey {
  id: number
  name: string
  exchange: string
}

interface StrategyCreatorProps {
  onClose: () => void
  onSuccess: () => void
}

const StrategyCreator: React.FC<StrategyCreatorProps> = ({ onClose, onSuccess }) => {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [strategyTypes, setStrategyTypes] = useState<Record<string, StrategyType>>({})
  const [apiKeys, setApiKeys] = useState<APIKey[]>([])
  
  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    strategy_type: '',
    exchange: '',
    symbol: 'BTCUSDT',
    market_type: 'spot',
    base_amount: 1000,
    max_amount: null as number | null,
    parameters: {} as Record<string, any>,
    api_key_id: null as number | null
  })

  // 验证结果
  const [validationResult, setValidationResult] = useState<any>(null)
  const [backtestResult, setBacktestResult] = useState<any>(null)

  useEffect(() => {
    loadStrategyTypes()
    loadApiKeys()
  }, [])

  const loadStrategyTypes = async () => {
    try {
      const types = await strategyApi.getStrategyTypes()
      setStrategyTypes(types)
    } catch (error) {
      toast.error('加载策略类型失败')
    }
  }

  const loadApiKeys = async () => {
    try {
      const keys = await apiKeyApi.getApiKeys()
      setApiKeys(keys)
    } catch (error) {
      toast.error('加载API密钥失败')
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleParameterChange = (paramName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [paramName]: value
      }
    }))
  }

  const validateStrategy = async () => {
    try {
      setLoading(true)
      const result = await strategyApi.validateStrategy(
        formData.strategy_type,
        formData.parameters
      )
      setValidationResult(result)
      
      if (result.valid) {
        toast.success('策略配置验证通过')
        setStep(3)
      } else {
        toast.error(result.message)
      }
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '验证失败')
    } finally {
      setLoading(false)
    }
  }

  const runBacktest = async () => {
    try {
      setLoading(true)
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 3) // 3个月回测

      const backtestData = {
        strategy_type: formData.strategy_type,
        symbol: formData.symbol,
        initial_capital: formData.base_amount,
        parameters: formData.parameters,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        interval: '1h',
        fee_rate: 0.001
      }

      const result = await strategyApi.runBacktest(backtestData)
      setBacktestResult(result.result)
      toast.success('回测完成')
      setStep(4)
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '回测失败')
    } finally {
      setLoading(false)
    }
  }

  const createStrategy = async () => {
    try {
      setLoading(true)
      await strategyApi.createStrategy(formData)
      toast.success('策略创建成功')
      onSuccess()
      onClose()
    } catch (error: any) {
      toast.error(error.response?.data?.detail || '创建失败')
    } finally {
      setLoading(false)
    }
  }

  const renderStep1 = () => (
    <div className="space-y-8">
      {/* 标题卡片 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-500 rounded-lg">
            <CogIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">基础信息配置</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">设置策略的基本参数和交易配置</p>
          </div>
        </div>
      </div>
      
      {/* 表单卡片 */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <SparklesIcon className="w-4 h-4 text-blue-500" />
              <span>策略名称</span>
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
              placeholder="为您的策略起个响亮的名字"
            />
          </div>

          <div className="space-y-2">
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <ChartBarIcon className="w-4 h-4 text-purple-500" />
              <span>策略类型</span>
            </label>
            <select
              value={formData.strategy_type}
              onChange={(e) => handleInputChange('strategy_type', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
            >
              <option value="">选择策略类型</option>
              {Object.entries(strategyTypes).map(([key, type]) => (
                <option key={key} value={key}>
                  {type.name} - {type.description}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <ArrowTrendingUpIcon className="w-4 h-4 text-green-500" />
              <span>交易对</span>
            </label>
            <input
              type="text"
              value={formData.symbol}
              onChange={(e) => handleInputChange('symbol', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
              placeholder="BTCUSDT"
            />
          </div>

          <div className="space-y-2">
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <CurrencyDollarIcon className="w-4 h-4 text-yellow-500" />
              <span>初始资金</span>
            </label>
            <input
              type="number"
              value={formData.base_amount}
              onChange={(e) => handleInputChange('base_amount', Number(e.target.value))}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
              min="100"
              placeholder="1000"
            />
          </div>

          <div className="md:col-span-2 space-y-2">
            <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <ShieldCheckIcon className="w-4 h-4 text-indigo-500" />
              <span>API密钥</span>
            </label>
            <select
              value={formData.api_key_id || ''}
              onChange={(e) => handleInputChange('api_key_id', Number(e.target.value))}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
            >
              <option value="">选择API密钥</option>
              {apiKeys.map(key => (
                <option key={key.id} value={key.id}>
                  {key.name} ({key.exchange})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 描述卡片 */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
        <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          <SparklesIcon className="w-4 h-4 text-pink-500" />
          <span>策略描述</span>
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 resize-none"
          rows={4}
          placeholder="描述您的策略目标、适用场景和预期收益..."
        />
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-between items-center pt-4">
        <button 
          onClick={onClose} 
          className="px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
        >
          取消
        </button>
        <button
          onClick={() => setStep(2)}
          disabled={!formData.name || !formData.strategy_type}
          className="flex items-center space-x-2 px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          <span>下一步</span>
          <ArrowRightIcon className="w-4 h-4" />
        </button>
      </div>
    </div>
  )

  const renderStep2 = () => {
    const currentType = strategyTypes[formData.strategy_type]
    if (!currentType) return null

    return (
      <div className="space-y-8">
        {/* 策略信息卡片 */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-500 rounded-lg">
                <CogIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">参数配置</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">调整策略参数以优化交易表现</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                currentType.risk_level === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                currentType.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
              }`}>
                {currentType.risk_level === 'high' ? '🔴 高风险' :
                 currentType.risk_level === 'medium' ? '🟡 中风险' : '🟢 低风险'}
              </div>
              <div className="bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full">
                <span className="text-xs font-medium text-blue-800 dark:text-blue-400">
                  💰 最低资金: ${currentType.min_capital}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 参数配置卡片 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.entries(currentType.parameters).map(([paramName, paramConfig]: [string, any]) => (
              <div key={paramName} className="space-y-3">
                <label className="flex items-center space-x-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  <CogIcon className="w-4 h-4 text-indigo-500" />
                  <span>{paramConfig.name}</span>
                </label>
                {paramConfig.type === 'select' ? (
                  <select
                    value={formData.parameters[paramName] || paramConfig.default || ''}
                    onChange={(e) => handleParameterChange(paramName, e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                  >
                    {paramConfig.options?.map((option: any) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : (
                  <div className="relative">
                    <input
                      type={paramConfig.type === 'int' ? 'number' : paramConfig.type}
                      value={formData.parameters[paramName] || paramConfig.default || ''}
                      onChange={(e) => {
                        const value = paramConfig.type === 'int' || paramConfig.type === 'float' 
                          ? Number(e.target.value) 
                          : e.target.value
                        handleParameterChange(paramName, value)
                      }}
                      min={paramConfig.min}
                      max={paramConfig.max}
                      step={paramConfig.type === 'float' ? '0.01' : '1'}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200"
                      placeholder={paramConfig.description}
                    />
                    {paramConfig.min !== undefined && paramConfig.max !== undefined && (
                      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        范围: {paramConfig.min} - {paramConfig.max}
                      </div>
                    )}
                  </div>
                )}
                {paramConfig.description && (
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                    <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
                      💡 {paramConfig.description}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between items-center pt-4">
          <button 
            onClick={() => setStep(1)} 
            className="flex items-center space-x-2 px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            <span>上一步</span>
          </button>
          <button
            onClick={validateStrategy}
            disabled={loading}
            className="flex items-center space-x-2 px-8 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <span>{loading ? '验证中...' : '验证配置'}</span>
            <ShieldCheckIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    )
  }

  const renderStep3 = () => (
    <div className="space-y-8">
      {/* 标题卡片 */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-500 rounded-lg">
            <ShieldCheckIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">验证结果</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">策略参数验证完成，查看风险评估结果</p>
          </div>
        </div>
      </div>
      
      {validationResult && (
        <div className="space-y-6">
          {/* 验证状态 */}
          <div className={`rounded-xl p-6 border ${validationResult.valid 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' 
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
          }`}>
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                validationResult.valid ? 'bg-green-500' : 'bg-red-500'
              }`}>
                {validationResult.valid ? (
                  <CheckCircleIcon className="w-6 h-6 text-white" />
                ) : (
                  <ExclamationTriangleIcon className="w-6 h-6 text-white" />
                )}
              </div>
              <div>
                <h4 className={`text-lg font-semibold ${
                  validationResult.valid 
                    ? 'text-green-800 dark:text-green-400' 
                    : 'text-red-800 dark:text-red-400'
                }`}>
                  {validationResult.valid ? '✅ 验证通过' : '❌ 验证失败'}
                </h4>
                <p className={`text-sm ${
                  validationResult.valid 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {validationResult.message}
                </p>
              </div>
            </div>
          </div>

          {validationResult.risk_metrics && (
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
                <h4 className="text-lg font-semibold text-white flex items-center space-x-2">
                  <ExclamationTriangleIcon className="w-5 h-5" />
                  <span>风险评估</span>
                </h4>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-red-500 rounded-lg">
                        <ExclamationTriangleIcon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-red-800 dark:text-red-400">最大潜在亏损</p>
                        <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                          ${validationResult.risk_metrics.max_potential_loss?.toFixed(2) || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-500 rounded-lg">
                        <ChartBarIcon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-blue-800 dark:text-blue-400">资金利用率</p>
                        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {(validationResult.risk_metrics.capital_utilization * 100)?.toFixed(1) || 'N/A'}%
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {validationResult.capital_requirements && (
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 px-6 py-4">
                <h4 className="text-lg font-semibold text-white flex items-center space-x-2">
                  <CurrencyDollarIcon className="w-5 h-5" />
                  <span>资金需求</span>
                </h4>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-yellow-500 rounded-lg">
                        <CurrencyDollarIcon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-yellow-800 dark:text-yellow-400">最小资金</p>
                        <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                          ${validationResult.capital_requirements.min_capital || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-800">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-green-500 rounded-lg">
                        <ArrowTrendingUpIcon className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-green-800 dark:text-green-400">建议资金</p>
                        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                          ${validationResult.capital_requirements.recommended_capital?.toFixed(0) || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-between items-center pt-4">
        <button 
          onClick={() => setStep(2)} 
          className="flex items-center space-x-2 px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
        >
          <ArrowLeftIcon className="w-4 h-4" />
          <span>上一步</span>
        </button>
        <div className="flex space-x-3">
          <button
            onClick={runBacktest}
            disabled={loading}
            className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-lg hover:from-indigo-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <BeakerIcon className="w-4 h-4" />
            <span>{loading ? '回测中...' : '运行回测'}</span>
          </button>
          <button
            onClick={createStrategy}
            disabled={loading}
            className="flex items-center space-x-2 px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <RocketLaunchIcon className="w-4 h-4" />
            <span>{loading ? '创建中...' : '直接创建'}</span>
          </button>
        </div>
      </div>
    </div>
  )

  const renderStep4 = () => (
    <div className="space-y-8">
      {/* 标题卡片 */}
      <div className="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-indigo-200 dark:border-indigo-800">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-indigo-500 rounded-lg">
            <BeakerIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">回测结果</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">基于历史数据的策略表现分析</p>
          </div>
        </div>
      </div>
      
      {backtestResult && (
        <div className="space-y-6">
          {/* 核心指标卡片 */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
            <div className="bg-gradient-to-r from-purple-500 to-indigo-500 px-6 py-4">
              <h4 className="text-lg font-semibold text-white flex items-center space-x-2">
                <ChartBarIcon className="w-5 h-5" />
                <span>核心表现指标</span>
              </h4>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className={`rounded-lg p-4 border ${
                  backtestResult.total_return >= 0 
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                }`}>
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      backtestResult.total_return >= 0 ? 'bg-green-500' : 'bg-red-500'
                    }`}>
                      <ArrowTrendingUpIcon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className={`text-sm font-medium ${
                        backtestResult.total_return >= 0 
                          ? 'text-green-800 dark:text-green-400'
                          : 'text-red-800 dark:text-red-400'
                      }`}>总收益</p>
                      <p className={`text-2xl font-bold ${
                        backtestResult.total_return >= 0 
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {backtestResult.total_return >= 0 ? '+' : ''}{backtestResult.total_return?.toFixed(2)}%
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-500 rounded-lg">
                      <ChartBarIcon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-800 dark:text-blue-400">胜率</p>
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {backtestResult.win_rate?.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-800">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-red-500 rounded-lg">
                      <ExclamationTriangleIcon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-red-800 dark:text-red-400">最大回撤</p>
                      <p className="text-2xl font-bold text-red-600 dark:text-red-400">
                        -{backtestResult.max_drawdown?.toFixed(2)}%
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-500 rounded-lg">
                      <SparklesIcon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-purple-800 dark:text-purple-400">夏普比率</p>
                      <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {backtestResult.sharpe_ratio?.toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 策略评估 */}
          <div className={`rounded-xl p-6 border ${
            backtestResult.total_return >= 10 
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
              : backtestResult.total_return >= 0
              ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
          }`}>
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                backtestResult.total_return >= 10 ? 'bg-green-500' :
                backtestResult.total_return >= 0 ? 'bg-yellow-500' : 'bg-red-500'
              }`}>
                {backtestResult.total_return >= 10 ? (
                  <CheckCircleIcon className="w-6 h-6 text-white" />
                ) : backtestResult.total_return >= 0 ? (
                  <ExclamationTriangleIcon className="w-6 h-6 text-white" />
                ) : (
                  <XCircleIcon className="w-6 h-6 text-white" />
                )}
              </div>
              <div>
                <h4 className={`text-lg font-semibold ${
                  backtestResult.total_return >= 10 ? 'text-green-800 dark:text-green-400' :
                  backtestResult.total_return >= 0 ? 'text-yellow-800 dark:text-yellow-400' :
                  'text-red-800 dark:text-red-400'
                }`}>
                  {backtestResult.total_return >= 10 ? '🎉 策略表现优秀' :
                   backtestResult.total_return >= 0 ? '⚠️ 策略表现一般' : '❌ 策略表现不佳'}
                </h4>
                <p className={`text-sm ${
                  backtestResult.total_return >= 10 ? 'text-green-600 dark:text-green-400' :
                  backtestResult.total_return >= 0 ? 'text-yellow-600 dark:text-yellow-400' :
                  'text-red-600 dark:text-red-400'
                }`}>
                  {backtestResult.total_return >= 10 ? '回测结果显示该策略具有良好的盈利潜力，建议创建使用。' :
                   backtestResult.total_return >= 0 ? '回测结果显示该策略盈利能力有限，建议谨慎使用或调整参数。' :
                   '回测结果显示该策略存在亏损风险，建议重新调整参数或选择其他策略。'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-between items-center pt-4">
        <button 
          onClick={() => setStep(3)} 
          className="flex items-center space-x-2 px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
        >
          <ArrowLeftIcon className="w-4 h-4" />
          <span>上一步</span>
        </button>
        <button
          onClick={createStrategy}
          disabled={loading}
          className="flex items-center space-x-2 px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          <RocketLaunchIcon className="w-4 h-4" />
          <span>{loading ? '创建中...' : '创建策略'}</span>
        </button>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-5xl max-h-[95vh] overflow-hidden">
        {/* 头部渐变背景 */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                <RocketLaunchIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">创建智能策略</h2>
                <p className="text-blue-100 text-sm">构建您的专属量化交易策略</p>
              </div>
            </div>
            <button 
              onClick={onClose} 
              className="p-2 hover:bg-white/20 rounded-lg transition-colors duration-200"
            >
              <XMarkIcon className="w-6 h-6 text-white" />
            </button>
          </div>
        </div>
        
        <div className="p-6 overflow-y-auto max-h-[calc(95vh-120px)]">

          {/* 步骤指示器 */}
          <div className="mb-10">
            <div className="relative">
              {/* 连接线背景 */}
              <div className="absolute top-7 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              
              {/* 连接线前景 - 动态渐变 */}
              <div 
                className="absolute top-7 left-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 rounded-full transition-all duration-700 ease-in-out" 
                style={{width: `${((step - 1) / 3) * 100}%`}}
              ></div>
              
              {/* 步骤指示器 */}
              <div className="flex items-center justify-between relative">
                {[
                  { num: 1, title: '基础信息', icon: CogIcon, color: 'blue' },
                  { num: 2, title: '参数配置', icon: ChartBarIcon, color: 'purple' },
                  { num: 3, title: '验证配置', icon: ShieldCheckIcon, color: 'indigo' },
                  { num: 4, title: '回测结果', icon: BeakerIcon, color: 'green' }
                ].map(({ num, title, icon: Icon, color }) => (
                  <div key={num} className="flex flex-col items-center">
                    {/* 步骤圆圈 */}
                    <div 
                      className={`w-14 h-14 rounded-full flex items-center justify-center transition-all duration-500 ${step === num
                        ? `bg-gradient-to-br from-${color}-400 to-${color}-600 text-white shadow-lg shadow-${color}-200 dark:shadow-${color}-900/30 scale-110 border-2 border-white dark:border-gray-800`
                        : step > num
                        ? 'bg-gradient-to-br from-green-400 to-emerald-600 text-white shadow-md'
                        : 'bg-white dark:bg-gray-800 text-gray-400 border-2 border-gray-200 dark:border-gray-700'
                      }`}
                    >
                      <div className={`${step === num ? 'animate-pulse-slow' : ''}`}>
                        {step > num ? (
                          <CheckCircleIcon className="w-7 h-7" />
                        ) : (
                          <Icon className="w-7 h-7" />
                        )}
                      </div>
                    </div>
                    
                    {/* 步骤标题 */}
                    <div className="mt-3 text-center">
                      <span className={`text-sm font-semibold transition-colors duration-300 ${step === num
                        ? `text-${color}-600 dark:text-${color}-400`
                        : step > num
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-gray-500 dark:text-gray-400'
                      }`}>
                        {title}
                      </span>
                      
                      {/* 步骤数字 */}
                      <div className={`mt-1 text-xs font-medium ${step >= num
                        ? 'text-gray-600 dark:text-gray-300'
                        : 'text-gray-400 dark:text-gray-500'
                      }`}>
                        步骤 {num}/4
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 步骤内容 */}
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
          {step === 4 && renderStep4()}
        </div>
      </div>
    </div>
  )
}

export default StrategyCreator
