<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="个人量化交易平台 - 安全、轻量、可部署的量化交易解决方案" />
    <meta name="keywords" content="量化交易,加密货币,交易机器人,币安,OKX" />
    <title>个人量化交易平台</title>
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 主题色 -->
    <meta name="theme-color" content="#3b82f6" />
    
    <!-- PWA支持 -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 防止FOUC -->
    <style>
      html {
        font-family: 'Inter', system-ui, sans-serif;
      }
      
      /* 加载动画 */
      .loading-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 初始加载页面 */
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .dark #initial-loader {
        background: #111827;
        color: #ffffff;
      }
      
      /* 检测暗色模式 */
      @media (prefers-color-scheme: dark) {
        #initial-loader {
          background: #111827;
          color: #ffffff;
        }
      }
    </style>
  </head>
  <body>
    <!-- 初始加载页面 -->
    <div id="initial-loader">
      <div class="loading-spinner"></div>
      <p style="margin-top: 20px; font-size: 16px; color: #6b7280;">
        正在加载个人量化交易平台...
      </p>
    </div>
    
    <!-- React应用挂载点 -->
    <div id="root"></div>
    
    <!-- 主脚本 -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- 移除加载页面 -->
    <script>
      // 当React应用加载完成后移除加载页面
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loader = document.getElementById('initial-loader');
          if (loader) {
            loader.style.opacity = '0';
            loader.style.transition = 'opacity 0.3s ease-out';
            setTimeout(function() {
              loader.remove();
            }, 300);
          }
        }, 500);
      });
    </script>
  </body>
</html>
