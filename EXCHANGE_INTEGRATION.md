# 🔗 交易所集成说明

## 📋 概述

本项目已完成币安(Binance)和OKX交易所的完整集成，采用接口抽象设计，支持现货和期货交易。

## 🏗️ 架构设计

### 抽象接口层
- **BaseExchange**: 定义统一的交易所接口
- **数据模型**: 标准化的订单、交易、持仓数据结构
- **工厂模式**: ExchangeFactory统一创建交易所实例

### 具体实现层
- **BinanceExchange**: 币安交易所实现
- **OKXExchange**: OKX交易所实现
- **服务层**: ExchangeService提供业务逻辑封装

## 🔧 支持的功能

### 账户管理
- ✅ 连接测试
- ✅ 账户信息获取
- ✅ 余额查询

### 交易功能
- ✅ 下单（市价单、限价单）
- ✅ 取消订单
- ✅ 订单查询
- ✅ 订单历史
- ✅ 成交历史

### 持仓管理
- ✅ 持仓查询（期货）
- ✅ 持仓同步

### 行情数据
- ✅ 实时行情
- ✅ K线数据
- ✅ 订单簿
- ✅ 交易对信息

## 📝 使用方法

### 1. 配置API密钥

#### 币安API密钥
```python
# 通过API接口添加
POST /api/v1/api-keys
{
    "name": "币安主账户",
    "exchange": "binance",
    "api_key": "your_binance_api_key",
    "secret_key": "your_binance_secret_key",
    "is_testnet": false,
    "can_read": true,
    "can_trade": true
}
```

#### OKX API密钥
```python
# OKX需要额外的passphrase
POST /api/v1/api-keys
{
    "name": "OKX主账户",
    "exchange": "okx",
    "api_key": "your_okx_api_key",
    "secret_key": "your_okx_secret_key",
    "passphrase": "your_okx_passphrase",
    "is_testnet": false,
    "can_read": true,
    "can_trade": true
}
```

### 2. 测试连接

```python
# 测试API密钥连接
POST /api/v1/api-keys/{api_key_id}/test
```

### 3. 获取账户余额

```python
# 获取余额信息
GET /api/v1/api-keys/{api_key_id}/balances
```

### 4. 下单交易

```python
# 创建订单
POST /api/v1/orders
{
    "api_key_id": 1,
    "symbol": "BTCUSDT",
    "side": "buy",
    "order_type": "limit",
    "amount": 0.001,
    "price": 50000,
    "market_type": "spot"
}
```

### 5. 查询持仓

```python
# 获取持仓信息
GET /api/v1/positions?api_key_id=1
```

## 🔍 代码示例

### 直接使用交易所类

```python
from app.exchanges.factory import ExchangeFactory

# 创建币安交易所实例
exchange = ExchangeFactory.create_exchange(
    exchange_name="binance",
    api_key="your_api_key",
    secret_key="your_secret_key",
    testnet=False
)

# 测试连接
connected = await exchange.test_connection()

# 获取行情
ticker = await exchange.get_ticker("BTCUSDT")
print(f"BTC价格: ${ticker.price}")

# 下单
from app.exchanges.base import OrderSide, OrderType, MarketType

order = await exchange.create_order(
    symbol="BTCUSDT",
    side=OrderSide.BUY,
    order_type=OrderType.LIMIT,
    amount=0.001,
    price=50000,
    market_type=MarketType.SPOT
)
```

### 使用服务层

```python
from app.services.exchange_service import exchange_service

# 通过服务层下单（推荐）
order_result = await exchange_service.create_order(
    db=db,
    api_key_id=1,
    user_id=user.id,
    symbol="BTCUSDT",
    side="buy",
    order_type="limit",
    amount=0.001,
    price=50000
)
```

## 🔒 安全特性

### API密钥加密
- 所有API密钥使用AES-256加密存储
- 主密码保护，确保密钥安全
- 支持测试网和生产环境分离

### 权限控制
- 可配置读取权限（查询余额、订单等）
- 可配置交易权限（下单、取消订单等）
- 用户数据完全隔离

### 错误处理
- 完整的异常捕获和处理
- 详细的错误日志记录
- 优雅的降级处理

## 📊 支持的交易对格式

### 币安格式
- 现货: `BTCUSDT`, `ETHUSDT`, `BNBUSDT`
- 期货: `BTCUSDT`, `ETHUSDT` (永续合约)

### OKX格式
- 现货: `BTC-USDT`, `ETH-USDT`, `BNB-USDT`
- 期货: `BTC-USDT-SWAP`, `ETH-USDT-SWAP`

系统会自动进行格式转换，用户统一使用标准格式（如`BTCUSDT`）。

## 🚀 扩展新交易所

### 1. 创建交易所实现类

```python
from app.exchanges.base import BaseExchange

class NewExchange(BaseExchange):
    async def test_connection(self) -> bool:
        # 实现连接测试
        pass
    
    async def create_order(self, ...):
        # 实现下单逻辑
        pass
    
    # 实现其他必要方法...
```

### 2. 注册到工厂类

```python
from app.exchanges.factory import ExchangeFactory

ExchangeFactory.register_exchange("new_exchange", NewExchange)
```

### 3. 更新API密钥验证

在API密钥创建时添加新交易所的验证逻辑。

## 🧪 测试

### 运行交易所集成测试

```bash
cd backend
python test_exchanges.py
```

### 测试内容
- ✅ 交易所工厂类功能
- ✅ 数据模型验证
- ✅ 币安连接和行情获取
- ✅ OKX连接和行情获取
- ✅ 错误处理机制

## 📋 注意事项

### API限制
- 币安: 1200请求/分钟（权重限制）
- OKX: 20请求/2秒（频率限制）

### 网络要求
- 建议使用稳定的网络连接
- 海外服务器访问更稳定
- 支持代理配置（如需要）

### 资金安全
- 建议使用子账户API密钥
- 限制API密钥权限（只开启必要权限）
- 定期轮换API密钥
- 设置合理的风控参数

## 🔧 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 确认API端点可访问
   - 考虑使用代理

2. **签名错误**
   - 检查API密钥和密钥是否正确
   - 确认时间同步（特别是OKX）
   - 检查passphrase（OKX必需）

3. **权限不足**
   - 确认API密钥有相应权限
   - 检查IP白名单设置
   - 验证账户状态

4. **订单被拒绝**
   - 检查余额是否充足
   - 确认交易对是否正确
   - 验证价格和数量精度

### 日志查看

```bash
# 查看交易所相关日志
tail -f logs/trading.log | grep -i exchange
```

## 📞 技术支持

如遇到交易所集成相关问题：

1. 查看错误日志
2. 检查API密钥配置
3. 验证网络连接
4. 参考官方API文档
5. 提交Issue并附上详细错误信息

更多信息请参考：
- [币安API文档](https://binance-docs.github.io/apidocs/)
- [OKX API文档](https://www.okx.com/docs-v5/)
