#!/usr/bin/env python3
"""
后端API测试脚本
"""
import sys
import os
import asyncio
import httpx
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.core.config import settings
from backend.app.core.database import init_database, db_manager
from backend.app.core.security import validate_master_password

API_BASE_URL = f"http://localhost:{settings.backend_port}"

class APITester:
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=API_BASE_URL, timeout=30.0)
        self.token = None
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def test_health(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = await self.client.get("/health")
            if response.status_code == 200:
                print("✅ 健康检查通过")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    async def test_system_info(self):
        """测试系统信息"""
        print("🔍 测试系统信息...")
        try:
            response = await self.client.get("/info")
            if response.status_code == 200:
                info = response.json()
                print(f"✅ 系统信息: {info['app_name']} v{info['version']}")
                return True
            else:
                print(f"❌ 获取系统信息失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取系统信息异常: {e}")
            return False
    
    async def test_register(self):
        """测试用户注册"""
        print("🔍 测试用户注册...")
        try:
            user_data = {
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "TestPassword123"
            }
            
            response = await self.client.post("/api/v1/auth/register", json=user_data)
            if response.status_code == 200:
                print("✅ 用户注册成功")
                return True
            else:
                print(f"❌ 用户注册失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 用户注册异常: {e}")
            return False
    
    async def test_login(self):
        """测试用户登录"""
        print("🔍 测试用户登录...")
        try:
            login_data = {
                "username": "testuser",
                "password": "TestPassword123"
            }
            
            response = await self.client.post(
                "/api/v1/auth/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data["access_token"]
                self.client.headers["Authorization"] = f"Bearer {self.token}"
                print("✅ 用户登录成功")
                return True
            else:
                print(f"❌ 用户登录失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 用户登录异常: {e}")
            return False
    
    async def test_user_profile(self):
        """测试获取用户信息"""
        print("🔍 测试获取用户信息...")
        try:
            response = await self.client.get("/api/v1/auth/me")
            if response.status_code == 200:
                user = response.json()
                print(f"✅ 用户信息: {user['username']}")
                return True
            else:
                print(f"❌ 获取用户信息失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取用户信息异常: {e}")
            return False
    
    async def test_user_stats(self):
        """测试用户统计"""
        print("🔍 测试用户统计...")
        try:
            response = await self.client.get("/api/v1/users/stats")
            if response.status_code == 200:
                stats = response.json()
                print(f"✅ 用户统计: {stats}")
                return True
            else:
                print(f"❌ 获取用户统计失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取用户统计异常: {e}")
            return False
    
    async def test_risk_check(self):
        """测试风控检查"""
        print("🔍 测试风控检查...")
        try:
            response = await self.client.get("/api/v1/risk/check")
            if response.status_code == 200:
                risk = response.json()
                print(f"✅ 风控状态: {risk['risk_level']}")
                return True
            else:
                print(f"❌ 风控检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 风控检查异常: {e}")
            return False

async def main():
    """主测试函数"""
    print("🧠 个人量化交易平台 - 后端API测试")
    print("=" * 50)
    
    # 检查环境
    print("🔧 检查运行环境...")
    
    # 验证主密码
    if not validate_master_password():
        print("❌ 主密码验证失败")
        return False
    print("✅ 主密码验证通过")
    
    # 初始化数据库
    try:
        init_database()
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False
    
    # 测试数据库连接
    if db_manager.test_connection():
        print("✅ 数据库连接正常")
    else:
        print("❌ 数据库连接失败")
        return False
    
    print("\n🚀 开始API测试...")
    print("-" * 30)
    
    # 运行API测试
    async with APITester() as tester:
        tests = [
            ("健康检查", tester.test_health),
            ("系统信息", tester.test_system_info),
            ("用户注册", tester.test_register),
            ("用户登录", tester.test_login),
            ("用户信息", tester.test_user_profile),
            ("用户统计", tester.test_user_stats),
            ("风控检查", tester.test_risk_check),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if await test_func():
                    passed += 1
                print()
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}\n")
        
        print("=" * 50)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！后端API运行正常")
            return True
        else:
            print("⚠️  部分测试失败，请检查配置")
            return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        sys.exit(1)
