# 🚀 部署指南

## 📋 系统要求

### 开发环境
- Python 3.9+
- Node.js 18+
- Git

### 生产环境
- Docker & Docker Compose
- 2GB+ RAM
- 10GB+ 磁盘空间

## 🛠️ 开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd lazy
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

**重要配置项：**
```env
# 主密码 (用于加密API密钥)
MASTER_PASSWORD=your-secure-master-password

# JWT密钥
SECRET_KEY=your-super-secret-jwt-key

# Telegram Bot Token (可选)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
```

### 3. 后端部署

```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 前端部署

```bash
cd frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 启动开发服务器
npm run dev
```

### 5. 一键启动脚本

```bash
# 使用提供的启动脚本
chmod +x start.sh
./start.sh
```

## 🐳 Docker部署

### 1. 使用Docker Compose

```bash
# 配置环境变量
cp .env.example .env
nano .env

# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 2. 单独构建镜像

```bash
# 构建后端镜像
docker build -t trading-api ./backend

# 构建前端镜像
docker build -t trading-web ./frontend

# 运行容器
docker run -d -p 8000:8000 --name api trading-api
docker run -d -p 3000:3000 --name web trading-web
```

## 🔧 生产环境配置

### 1. 环境变量配置

```env
# 生产环境配置
DEBUG=false
SECRET_KEY=your-production-secret-key
MASTER_PASSWORD=your-production-master-password

# 数据库配置
DATABASE_URL=sqlite:///./data/trading.db

# CORS配置
ALLOWED_ORIGINS=https://yourdomain.com

# SSL配置 (推荐)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

### 2. Nginx配置

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    # 前端静态文件
    location / {
        proxy_pass http://web:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # API代理
    location /api/ {
        proxy_pass http://api:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://api:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 3. 数据备份

```bash
# 备份数据库
cp data/trading.db backup/trading_$(date +%Y%m%d_%H%M%S).db

# 自动备份脚本
#!/bin/bash
BACKUP_DIR="/path/to/backup"
DATE=$(date +%Y%m%d_%H%M%S)
cp data/trading.db $BACKUP_DIR/trading_$DATE.db
find $BACKUP_DIR -name "trading_*.db" -mtime +7 -delete
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 2. SSL证书

```bash
# 使用Let's Encrypt
certbot --nginx -d yourdomain.com
```

### 3. 定期更新

```bash
# 更新系统
apt update && apt upgrade -y

# 更新Docker镜像
docker-compose pull
docker-compose up -d
```

## 📊 监控与日志

### 1. 日志配置

```yaml
# docker-compose.yml 日志配置
services:
  api:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 2. 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查系统信息
curl http://localhost:8000/info
```

### 3. 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
df -h
```

## 🚨 故障排除

### 常见问题

1. **主密码错误**
   ```bash
   # 检查环境变量
   echo $MASTER_PASSWORD
   
   # 重新设置主密码
   export MASTER_PASSWORD=your-password
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库文件权限
   ls -la data/trading.db
   
   # 重新初始化数据库
   rm data/trading.db
   python -c "from app.core.database import init_database; init_database()"
   ```

3. **端口冲突**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :8000
   
   # 修改端口配置
   export BACKEND_PORT=8001
   ```

4. **前端构建失败**
   ```bash
   # 清理缓存
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

### 日志查看

```bash
# Docker日志
docker-compose logs api
docker-compose logs web

# 应用日志
tail -f logs/trading.log

# 系统日志
journalctl -u docker
```

## 📞 技术支持

如遇到部署问题，请：

1. 检查系统要求是否满足
2. 确认环境变量配置正确
3. 查看相关日志文件
4. 参考故障排除章节

更多技术支持请查看项目文档或提交Issue。
